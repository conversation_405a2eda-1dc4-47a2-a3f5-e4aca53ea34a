-- COMPREHENSIVE SECURITY FIXES FOR SUPABASE DATABASE
-- This script addresses all security errors and warnings identified by the database linter
-- Execute these commands in order to fix security issues without breaking functionality

-- =============================================================================
-- PART 1: FIX SECURITY DEFINER VIEWS (15 CRITICAL ERRORS)
-- =============================================================================
-- These views currently use SECURITY DEFINER which bypasses RLS and user permissions
-- We'll recreate them with SECURITY INVOKER to respect user permissions and RLS

-- 1. Fix business_performance_summary view
DROP VIEW IF EXISTS public.business_performance_summary CASCADE;
CREATE VIEW public.business_performance_summary
WITH (security_invoker = true)
AS
SELECT c.id AS company_id,
    c.name AS company_name,
    count(DISTINCT lm.id) AS total_members,
    count(DISTINCT r.id) AS total_receipts,
    count(DISTINCT pt.id) AS total_transactions,
    count(DISTINCT bi.id) AS total_items,
    COALESCE(sum(r.total_amount), (0)::numeric) AS total_revenue,
    COALESCE(avg(r.total_amount), (0)::numeric) AS avg_order_value,
    COALESCE(sum(pt.points_change), (0)::bigint) AS total_points_issued,
    date_trunc('month'::text, (CURRENT_DATE)::timestamp with time zone) AS report_month
FROM (((companies c
     LEFT JOIN loyalty_members lm ON ((c.id = lm.company_id)))
     LEFT JOIN receipts r ON ((c.id = r.company_id)))
     LEFT JOIN points_transactions pt ON ((c.id = pt.company_id)))
     LEFT JOIN business_items bi ON ((c.id = bi.company_id))
WHERE (c.is_active = true)
GROUP BY c.id, c.name;

-- 2. Fix category_performance view
DROP VIEW IF EXISTS public.category_performance CASCADE;
CREATE VIEW public.category_performance
WITH (security_invoker = true)
AS
SELECT bi.company_id,
    bi.item_category,
    count(DISTINCT bi.id) AS total_items,
    COALESCE(sum(ri.quantity), (0)::numeric) AS total_quantity_sold,
    count(DISTINCT ri.receipt_id) AS number_of_orders,
    COALESCE(sum(ri.total_price), (0)::numeric) AS total_revenue,
    COALESCE(avg(ri.unit_price), (0)::numeric) AS avg_selling_price,
    COALESCE(avg(ri.quantity), (0)::numeric) AS avg_quantity_per_order
FROM (business_items bi
     LEFT JOIN receipt_items ri ON ((bi.id = ri.business_item_id)))
WHERE (bi.is_active = true)
GROUP BY bi.company_id, bi.item_category
ORDER BY COALESCE(sum(ri.total_price), (0)::numeric) DESC;

-- 3. Fix recent_activity_summary view
DROP VIEW IF EXISTS public.recent_activity_summary CASCADE;
CREATE VIEW public.recent_activity_summary
WITH (security_invoker = true)
AS
SELECT c.id AS company_id,
    c.name AS company_name,
    count(DISTINCT lm.id) FILTER (WHERE (lm.registration_date >= (CURRENT_DATE - '30 days'::interval))) AS new_members_30d,
    count(DISTINCT r.id) FILTER (WHERE (r.purchase_date >= (CURRENT_DATE - '30 days'::interval))) AS receipts_30d,
    count(DISTINCT pt.id) FILTER (WHERE (pt.transaction_date >= (CURRENT_DATE - '30 days'::interval))) AS transactions_30d,
    COALESCE(sum(r.total_amount) FILTER (WHERE (r.purchase_date >= (CURRENT_DATE - '30 days'::interval))), (0)::numeric) AS revenue_30d,
    count(DISTINCT lm.id) FILTER (WHERE (EXISTS ( SELECT 1
           FROM points_transactions pt2
          WHERE ((pt2.member_id = lm.id) AND (pt2.transaction_date >= (CURRENT_DATE - '30 days'::interval)))))) AS active_members_30d
FROM (((companies c
     LEFT JOIN loyalty_members lm ON ((c.id = lm.company_id)))
     LEFT JOIN receipts r ON ((c.id = r.company_id)))
     LEFT JOIN points_transactions pt ON ((c.id = pt.company_id)))
WHERE (c.is_active = true)
GROUP BY c.id, c.name;

-- 4. Fix monthly_trends view
DROP VIEW IF EXISTS public.monthly_trends CASCADE;
CREATE VIEW public.monthly_trends
WITH (security_invoker = true)
AS
SELECT c.id AS company_id,
    c.name AS company_name,
    date_trunc('month'::text, r.purchase_date) AS month,
    count(DISTINCT r.id) AS receipts_count,
    count(DISTINCT lm.id) AS unique_customers,
    COALESCE(sum(r.total_amount), (0)::numeric) AS revenue,
    COALESCE(avg(r.total_amount), (0)::numeric) AS avg_order_value
FROM ((companies c
     LEFT JOIN receipts r ON ((c.id = r.company_id)))
     LEFT JOIN loyalty_members lm ON ((r.member_id = lm.id)))
WHERE ((c.is_active = true) AND (r.purchase_date >= (CURRENT_DATE - '1 year'::interval)))
GROUP BY c.id, c.name, (date_trunc('month'::text, r.purchase_date))
ORDER BY c.id, (date_trunc('month'::text, r.purchase_date)) DESC;

-- 5. Fix customer_item_preferences view
DROP VIEW IF EXISTS public.customer_item_preferences CASCADE;
CREATE VIEW public.customer_item_preferences
WITH (security_invoker = true)
AS
SELECT m.id AS member_id,
    m.name AS member_name,
    bi.item_name,
    bi.item_category,
    count(ri.id) AS purchase_count,
    sum(ri.total_price) AS total_spent,
    avg(ri.unit_price) AS avg_price_paid,
    max(r.purchase_date) AS last_purchase_date,
    ((((count(ri.id))::numeric * 0.4) + (EXTRACT(days FROM (now() - max(r.purchase_date))) * '-0.1'::numeric)) + ((sum(ri.total_price) / NULLIF(avg(bi.standard_price), (0)::numeric)) * 0.5)) AS preference_score
FROM (((loyalty_members m
     JOIN receipts r ON ((r.member_id = m.id)))
     JOIN receipt_items ri ON ((ri.receipt_id = r.id)))
     JOIN business_items bi ON ((bi.id = ri.business_item_id)))
WHERE (ri.business_item_id IS NOT NULL)
GROUP BY m.id, m.name, bi.id, bi.item_name, bi.item_category
ORDER BY ((((count(ri.id))::numeric * 0.4) + (EXTRACT(days FROM (now() - max(r.purchase_date))) * '-0.1'::numeric)) + ((sum(ri.total_price) / NULLIF(avg(bi.standard_price), (0)::numeric)) * 0.5)) DESC;

-- =============================================================================
-- PART 2: ENABLE RLS ON TELEGRAM_NOTIFICATIONS TABLE (CRITICAL ERROR)
-- =============================================================================

-- Enable RLS on telegram_notifications table
ALTER TABLE public.telegram_notifications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for telegram_notifications
-- Policy 1: Users can only see notifications for their own company
CREATE POLICY "Users can view notifications for their company" ON public.telegram_notifications
    FOR SELECT
    USING (
        company_id IN (
            SELECT c.id FROM companies c WHERE c.administrator_id = auth.uid()
            UNION
            SELECT ca.company_id FROM company_administrators ca WHERE ca.administrator_id = auth.uid()
        )
    );

-- Policy 2: Only company admins can insert notifications
CREATE POLICY "Company admins can create notifications" ON public.telegram_notifications
    FOR INSERT
    WITH CHECK (
        company_id IN (
            SELECT c.id FROM companies c WHERE c.administrator_id = auth.uid()
            UNION
            SELECT ca.company_id FROM company_administrators ca WHERE ca.administrator_id = auth.uid() AND ca.role IN ('ADMIN', 'OWNER')
        )
    );

-- Policy 3: Only company admins can update notifications
CREATE POLICY "Company admins can update notifications" ON public.telegram_notifications
    FOR UPDATE
    USING (
        company_id IN (
            SELECT c.id FROM companies c WHERE c.administrator_id = auth.uid()
            UNION
            SELECT ca.company_id FROM company_administrators ca WHERE ca.administrator_id = auth.uid() AND ca.role IN ('ADMIN', 'OWNER')
        )
    );

-- Policy 4: Only company admins can delete notifications
CREATE POLICY "Company admins can delete notifications" ON public.telegram_notifications
    FOR DELETE
    USING (
        company_id IN (
            SELECT c.id FROM companies c WHERE c.administrator_id = auth.uid()
            UNION
            SELECT ca.company_id FROM company_administrators ca WHERE ca.administrator_id = auth.uid() AND ca.role IN ('ADMIN', 'OWNER')
        )
    );

-- =============================================================================
-- PART 3: ADDRESS MATERIALIZED VIEW WARNINGS (MEDIUM PRIORITY)
-- =============================================================================

-- Option 1: Revoke public access from materialized views (RECOMMENDED)
REVOKE ALL ON public.dashboard_metrics FROM anon, authenticated;
REVOKE ALL ON public.dashboard_metrics_mv FROM anon, authenticated;

-- Option 2: If you need API access, create secure views instead
-- CREATE VIEW public.dashboard_metrics_secure
-- WITH (security_invoker = true)
-- AS SELECT * FROM public.dashboard_metrics WHERE company_id IN (
--     SELECT c.id FROM companies c WHERE c.administrator_id = auth.uid()
--     UNION
--     SELECT ca.company_id FROM company_administrators ca WHERE ca.user_id = auth.uid()
-- );

-- =============================================================================
-- PART 4: MOVE EXTENSION FROM PUBLIC SCHEMA (WARNING)
-- =============================================================================

-- Move pg_trgm extension to extensions schema (if it exists)
-- Note: This requires superuser privileges and should be done carefully
-- CREATE SCHEMA IF NOT EXISTS extensions;
-- ALTER EXTENSION pg_trgm SET SCHEMA extensions;

-- Alternative: Document that pg_trgm is intentionally in public schema
-- Add comment explaining why it's needed in public schema
COMMENT ON EXTENSION pg_trgm IS 'Required in public schema for text search functionality across the application';

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Verify RLS is enabled on telegram_notifications
-- SELECT schemaname, tablename, rowsecurity FROM pg_tables WHERE tablename = 'telegram_notifications';

-- Verify views are using security_invoker
-- SELECT schemaname, viewname FROM pg_views WHERE schemaname = 'public' AND viewname LIKE '%performance%';

-- Check materialized view permissions
-- SELECT grantee, privilege_type FROM information_schema.table_privileges 
-- WHERE table_name IN ('dashboard_metrics', 'dashboard_metrics_mv');

-- =============================================================================
-- NOTES FOR IMPLEMENTATION
-- =============================================================================

/*
IMPORTANT IMPLEMENTATION NOTES:

1. BACKUP FIRST: Always backup your database before running these changes

2. VIEW DEPENDENCIES: Some views may have dependencies. If you get errors about 
   dependent objects, you may need to drop and recreate them in the correct order.

3. APPLICATION TESTING: After applying these fixes, test your application thoroughly:
   - Verify analytics dashboards still work
   - Check that users can only see their own company data
   - Test telegram notification functionality
   - Ensure materialized view data is still accessible where needed

4. GRADUAL ROLLOUT: Consider applying these fixes in stages:
   - Stage 1: Fix the most critical SECURITY DEFINER views
   - Stage 2: Enable RLS on telegram_notifications
   - Stage 3: Address materialized view warnings
   - Stage 4: Handle extension schema warning

5. MONITORING: Monitor your application logs after deployment to catch any
   permission-related errors that might indicate missing policies or access issues.

6. EXTENSION SCHEMA: The pg_trgm extension move requires superuser privileges.
   If you don't have these, the comment approach documents the intentional placement.
*/
