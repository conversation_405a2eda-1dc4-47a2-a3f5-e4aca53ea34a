-- Add missing columns to companies table for admin business creation
-- These columns are needed for the admin business management interface

-- Add email, phone, address, and currency columns if they don't exist
DO $$
BEGIN
  -- Add email column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'email') THEN
    ALTER TABLE companies ADD COLUMN email TEXT;
  END IF;

  -- Add phone column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'phone') THEN
    ALTER TABLE companies ADD COLUMN phone TEXT;
  END IF;

  -- Add address column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'address') THEN
    ALTER TABLE companies ADD COLUMN address TEXT;
  END IF;

  -- Add currency column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                WHERE table_name = 'companies' AND column_name = 'currency') THEN
    ALTER TABLE companies ADD COLUMN currency TEXT DEFAULT 'ETB';
  END IF;

END $$;
