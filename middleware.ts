import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { updateSession } from '@/utils/supabase/middleware'
import { checkPasswordChangeRequired } from '@/lib/auth-middleware'

export async function middleware(request: NextRequest) {
  // Skip middleware for static files, API routes, cashier invitations, and assets
  if (
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.includes('.') ||
    request.nextUrl.pathname === '/favicon.ico' ||
    request.nextUrl.pathname.startsWith('/public/') ||
    (request.nextUrl.pathname === '/cashiers/accept' && request.nextUrl.searchParams.has('token'))
  ) {
    return NextResponse.next()
  }

  // Use the official Supabase updateSession pattern
  const sessionResponse = await updateSession(request)
  
  // If session update resulted in a redirect, return it
  if (sessionResponse.status === 302) {
    return sessionResponse
  }

  // Check for password change requirement
  const passwordChangeResponse = await checkPasswordChangeRequired(request)
  if (passwordChangeResponse) {
    return passwordChangeResponse
  }

  return sessionResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files (images, etc)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public|.*\\.(?:svg|png|jpg|jpeg|gif|webp|css|js|ico|woff|woff2|ttf|eot)$).*)',
  ],
}
