-- Test the dashboard configuration trigger
BEGIN;

-- Create a test company
INSERT INTO companies (
    id,
    name,
    slug,
    administrator_id,
    points_earning_ratio,
    is_active
) VALUES (
    gen_random_uuid(),
    'Test Company Trigger',
    'test-company-trigger',
    '13de1684-1cdd-4943-928b-e8b22010b296',
    1,
    true
) RETURNING id;

-- Check if dashboard configurations were created automatically
SELECT
    'Dashboard configs created:' as status,
    count(*) as config_count
FROM dashboard_configurations
WHERE company_id = (
    SELECT id FROM companies WHERE name = 'Test Company Trigger'
);

-- Show the actual configurations created
SELECT
    widget_id,
    widget_name,
    display_order
FROM dashboard_configurations
WHERE company_id = (
    SELECT id FROM companies WHERE name = 'Test Company Trigger'
)
ORDER BY display_order;

ROLLBACK;
