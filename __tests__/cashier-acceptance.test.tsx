import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import { useRouter, useSearchParams } from 'next/navigation'
import AcceptInvitationPage from '@/app/cashiers/accept/page'
import { toast } from 'sonner'

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Mock Sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}))

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('AcceptInvitationPage', () => {
  const mockRouter = {
    push: jest.fn(),
  }
  
  const mockSearchParams = {
    get: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup default mocks
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)
    mockSearchParams.get.mockReturnValue('valid-token')
    
    // Default fetch mock for invitation details
    mockFetch.mockImplementationOnce(() => Promise.resolve({
      ok: true,
      json: async () => ({
        email: '<EMAIL>',
        companyName: 'Test Company',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      }),
    }))
  })

  it('should show loading state initially', () => {
    render(<AcceptInvitationPage />)
    expect(screen.getByText('Loading Invitation')).toBeInTheDocument()
  })

  it('should show error when no token is provided', async () => {
    mockSearchParams.get.mockReturnValue(null)
    
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Invitation Error')).toBeInTheDocument()
      expect(screen.getByText('Invalid invitation link. No token provided.')).toBeInTheDocument()
    })
  })

  it('should show error when API returns an error', async () => {
    mockFetch.mockReset()
    mockFetch.mockImplementationOnce(() => Promise.resolve({
      ok: false,
      json: async () => ({ error: 'Invitation expired' }),
    }))
    
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Invitation Error')).toBeInTheDocument()
      expect(screen.getByText('Invitation expired')).toBeInTheDocument()
    })
  })

  it('should display invitation details when loaded successfully', async () => {
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Accept Cashier Invitation')).toBeInTheDocument()
      expect(screen.getByText(/You've been invited to be a cashier at/)).toBeInTheDocument()
      expect(screen.getByText('Test Company')).toBeInTheDocument()
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument()
    })
  })

  it('should validate passwords match', async () => {
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Accept Cashier Invitation')).toBeInTheDocument()
    })
    
    // Fill out form with mismatched passwords
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password456' } })
    
    // Submit form
    fireEvent.click(screen.getByText('Create Account & Accept Invitation'))
    
    expect(toast.error).toHaveBeenCalledWith('Passwords do not match')
    expect(global.fetch).toHaveBeenCalledTimes(1) // Only the initial fetch, not the form submission
  })

  it('should validate password length', async () => {
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Accept Cashier Invitation')).toBeInTheDocument()
    })
    
    // Fill out form with short password
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: '12345' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: '12345' } })
    
    // Submit form
    fireEvent.click(screen.getByText('Create Account & Accept Invitation'))
    
    expect(toast.error).toHaveBeenCalledWith('Password must be at least 6 characters')
    expect(global.fetch).toHaveBeenCalledTimes(1) // Only the initial fetch, not the form submission
  })

  it('should submit form and redirect on success', async () => {
    // Mock successful form submission
    mockFetch.mockImplementation((url) => {
      if (url.includes('?token=')) {
        return Promise.resolve({
          ok: true,
          json: async () => ({
            email: '<EMAIL>',
            companyName: 'Test Company',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          }),
        })
      } else {
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true }),
        })
      }
    })
    
    // Mock setTimeout
    jest.useFakeTimers()
    
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Accept Cashier Invitation')).toBeInTheDocument()
    })
    
    // Fill out form correctly
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } })
    
    // Submit form
    fireEvent.click(screen.getByText('Create Account & Accept Invitation'))
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/cashiers/accept', expect.any(Object))
      expect(toast.success).toHaveBeenCalledWith('Account created successfully!')
    })
    
    // Fast-forward timers
    jest.runAllTimers()
    
    expect(mockRouter.push).toHaveBeenCalledWith('/auth/signin')
    
    jest.useRealTimers()
  })

  it('should show error when form submission fails', async () => {
    // Mock failed form submission
    mockFetch.mockImplementation((url) => {
      if (url.includes('?token=')) {
        return Promise.resolve({
          ok: true,
          json: async () => ({
            email: '<EMAIL>',
            companyName: 'Test Company',
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          }),
        })
      } else {
        return Promise.resolve({
          ok: false,
          json: async () => ({ error: 'Failed to create account' }),
        })
      }
    })
    
    render(<AcceptInvitationPage />)
    
    await waitFor(() => {
      expect(screen.getByText('Accept Cashier Invitation')).toBeInTheDocument()
    })
    
    // Fill out form correctly
    fireEvent.change(screen.getByLabelText('Full Name'), { target: { value: 'John Doe' } })
    fireEvent.change(screen.getByLabelText('Password'), { target: { value: 'password123' } })
    fireEvent.change(screen.getByLabelText('Confirm Password'), { target: { value: 'password123' } })
    
    // Submit form
    fireEvent.click(screen.getByText('Create Account & Accept Invitation'))
    
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/cashiers/accept', expect.any(Object))
      expect(toast.error).toHaveBeenCalledWith('Failed to create account')
    })
  })
})
