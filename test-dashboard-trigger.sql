-- Test the fixed dashboard configuration trigger
-- This will test if creating a company automatically creates dashboard configurations

BEGIN;

-- Create a test company
INSERT INTO companies (
    id,
    name,
    slug,
    administrator_id,
    points_earning_ratio,
    is_active
) VALUES (
    '12345678-1234-1234-1234-123456789012',
    'Test Company',
    'test-company',
    '13de1684-1cdd-4943-928b-e8b22010b296', -- real user UUID
    1,
    true
);-- Check if dashboard configurations were created automatically
SELECT
    company_id,
    widget_id,
    widget_name,
    is_visible,
    display_order
FROM dashboard_configurations
WHERE company_id = '12345678-1234-1234-1234-123456789012'
ORDER BY display_order;

-- Clean up the test
DELETE FROM dashboard_configurations WHERE company_id = '12345678-1234-1234-1234-123456789012';
DELETE FROM companies WHERE id = '12345678-1234-1234-1234-123456789012';ROLLBACK;
