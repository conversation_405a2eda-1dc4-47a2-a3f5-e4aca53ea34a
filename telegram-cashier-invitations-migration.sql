-- Migration: Add Telegram support to cashier invitations
-- This migration adds columns to track Telegram delivery for cashier invitations

-- Add new columns to cashier_invitations table
ALTER TABLE cashier_invitations
ADD COLUMN telegram_sent_at TIMESTAMPTZ,
ADD COLUMN telegram_chat_id VARCHAR(255);

-- Add comments for documentation
COMMENT ON COLUMN cashier_invitations.telegram_sent_at IS 'Timestamp when invitation was sent via Telegram';
COMMENT ON COLUMN cashier_invitations.telegram_chat_id IS 'Telegram Chat ID of the invitation recipient';

-- Create index for efficient queries on telegram delivery status
CREATE INDEX idx_cashier_invitations_telegram_sent
ON cashier_invitations(telegram_sent_at)
WHERE telegram_sent_at IS NOT NULL;

-- Update any existing RLS policies if needed (cashier_invitations should inherit from companies security)
-- No additional RLS policies needed as this table already uses company-based security
