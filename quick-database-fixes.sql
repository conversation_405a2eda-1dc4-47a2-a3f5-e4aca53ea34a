-- Critical database security fixes for RLS access issues
-- Run this to fix the database function security problems

-- Fix the main onboarding status function
CREATE OR REPLACE FUNCTION public.get_onboarding_status_optimized(p_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $func$
DECLARE
  company_record record;
  result json;
BEGIN
  SELECT
    c.id,
    c.name,
    c.created_at,
    COALESCE(counts.member_count, 0) as member_count,
    COALESCE(counts.transaction_count, 0) as transaction_count,
    COALESCE(counts.reward_count, 0) as reward_count,
    COALESCE(counts.tier_count, 0) as tier_count
  INTO company_record
  FROM public.companies c
  LEFT JOIN (
    SELECT
      c.id as company_id,
      COUNT(DISTINCT lm.id) as member_count,
      COUNT(DISTINCT pt.id) as transaction_count,
      COUNT(DISTINCT r.id) as reward_count,
      COUNT(DISTINCT td.id) as tier_count
    FROM public.companies c
    LEFT JOIN public.loyalty_members lm ON c.id = lm.company_id
    LEFT JOIN public.points_transactions pt ON c.id = pt.company_id
    LEFT JOIN public.rewards r ON c.id = r.company_id
    LEFT JOIN public.tier_definitions td ON c.id = td.company_id
    WHERE c.administrator_id = p_user_id
    GROUP BY c.id
  ) counts ON c.id = counts.company_id
  WHERE c.administrator_id = p_user_id
  ORDER BY c.created_at DESC
  LIMIT 1;

  IF NOT FOUND THEN
    RETURN json_build_object(
      'hasCompany', false,
      'completionPercentage', 0,
      'nextSteps', json_build_array('Create your company profile')
    );
  END IF;

  result := json_build_object(
    'hasCompany', true,
    'companyId', company_record.id,
    'companyName', company_record.name,
    'memberCount', company_record.member_count,
    'transactionCount', company_record.transaction_count,
    'rewardCount', company_record.reward_count,
    'tierCount', company_record.tier_count,
    'completionPercentage', CASE
      WHEN company_record.member_count >= 5 AND company_record.reward_count >= 1 AND company_record.tier_count >= 2 THEN 100
      WHEN company_record.member_count >= 1 AND company_record.reward_count >= 1 THEN 75
      WHEN company_record.member_count >= 1 OR company_record.reward_count >= 1 THEN 50
      ELSE 25
    END,
    'nextSteps', CASE
      WHEN company_record.member_count = 0 THEN json_build_array('Add your first loyalty member')
      WHEN company_record.reward_count = 0 THEN json_build_array('Create your first reward')
      WHEN company_record.tier_count < 2 THEN json_build_array('Set up member tiers')
      ELSE json_build_array('Your loyalty program is ready!')
    END
  );

  RETURN result;
END;
$func$;

-- Fix calculate_member_tier function
CREATE OR REPLACE FUNCTION public.calculate_member_tier(p_company_id uuid, p_points numeric)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $func$
DECLARE
  tier_name text := 'Basic';
BEGIN
  SELECT td.name INTO tier_name
  FROM public.tier_definitions td
  WHERE td.company_id = p_company_id
    AND p_points >= td.minimum_points
  ORDER BY td.minimum_points DESC
  LIMIT 1;

  RETURN COALESCE(tier_name, 'Basic');
END;
$func$;

-- Fix trigger functions to use SECURITY DEFINER
CREATE OR REPLACE FUNCTION public.update_member_point_balances()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $func$
BEGIN
  UPDATE public.loyalty_members
  SET
    lifetime_points = COALESCE((
      SELECT SUM(points_amount)
      FROM public.points_transactions
      WHERE member_id = COALESCE(NEW.member_id, OLD.member_id)
        AND transaction_type = 'EARN'
    ), 0),
    redeemed_points = COALESCE((
      SELECT SUM(points_amount)
      FROM public.points_transactions
      WHERE member_id = COALESCE(NEW.member_id, OLD.member_id)
        AND transaction_type = 'REDEEM'
    ), 0),
    expired_points = COALESCE((
      SELECT SUM(points_amount)
      FROM public.points_transactions
      WHERE member_id = COALESCE(NEW.member_id, OLD.member_id)
        AND transaction_type = 'EXPIRE'
    ), 0)
  WHERE id = COALESCE(NEW.member_id, OLD.member_id);

  RETURN COALESCE(NEW, OLD);
END;
$func$;

-- Fix transaction creation function
CREATE OR REPLACE FUNCTION public.create_points_transaction(
  p_member_id uuid,
  p_company_id uuid,
  p_transaction_type text,
  p_points_amount numeric,
  p_reference_id uuid DEFAULT NULL,
  p_description text DEFAULT NULL,
  p_metadata jsonb DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $func$
DECLARE
  new_transaction_id uuid;
  current_tier text;
BEGIN
  new_transaction_id := gen_random_uuid();

  INSERT INTO public.points_transactions (
    id,
    member_id,
    company_id,
    transaction_type,
    points_amount,
    reference_id,
    description,
    metadata,
    created_at
  ) VALUES (
    new_transaction_id,
    p_member_id,
    p_company_id,
    p_transaction_type,
    p_points_amount,
    p_reference_id,
    p_description,
    p_metadata,
    now()
  );

  current_tier := public.calculate_member_tier(
    p_company_id,
    (SELECT COALESCE(lifetime_points, 0) - COALESCE(redeemed_points, 0) - COALESCE(expired_points, 0)
     FROM public.loyalty_members
     WHERE id = p_member_id)
  );

  UPDATE public.loyalty_members
  SET loyalty_tier = current_tier
  WHERE id = p_member_id AND company_id = p_company_id;

  RETURN new_transaction_id;
END;
$func$;

-- Fix telegram notification function
CREATE OR REPLACE FUNCTION public.send_telegram_notification_for_transaction()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $func$
DECLARE
  member_record record;
  points_balance numeric;
  notification_message text;
BEGIN
  SELECT
    lm.name,
    lm.telegram_chat_id,
    lm.loyalty_id,
    COALESCE(lm.lifetime_points, 0) - COALESCE(lm.redeemed_points, 0) - COALESCE(lm.expired_points, 0) as available_points
  INTO member_record
  FROM public.loyalty_members lm
  WHERE lm.id = NEW.member_id;

  IF member_record.telegram_chat_id IS NOT NULL THEN
    notification_message := CASE NEW.transaction_type
      WHEN 'EARN' THEN
        format('🎉 Congratulations %s! You earned %s points. Your current balance: %s points (ID: %s)',
          member_record.name, NEW.points_amount, member_record.available_points, member_record.loyalty_id)
      WHEN 'REDEEM' THEN
        format('✅ %s, you redeemed %s points. Remaining balance: %s points (ID: %s)',
          member_record.name, NEW.points_amount, member_record.available_points, member_record.loyalty_id)
      WHEN 'EXPIRE' THEN
        format('⏰ %s, %s points expired from your account. Current balance: %s points (ID: %s)',
          member_record.name, NEW.points_amount, member_record.available_points, member_record.loyalty_id)
      ELSE
        format('📊 %s, your point balance has been updated. Current balance: %s points (ID: %s)',
          member_record.name, member_record.available_points, member_record.loyalty_id)
    END;

    INSERT INTO public.telegram_notifications (
      chat_id,
      message,
      member_id,
      company_id,
      created_at
    ) VALUES (
      member_record.telegram_chat_id,
      notification_message,
      NEW.member_id,
      NEW.company_id,
      now()
    );
  END IF;

  RETURN NEW;
END;
$func$;

-- Confirm the fixes are applied
SELECT 'Database security fixes applied successfully!' as result;
