'use client'

import React, { createContext, useContext } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'

interface DashboardWidget {
  id: string
  company_id: string
  widget_id: string
  widget_name: string
  is_visible: boolean
  display_order: number
  custom_settings: Record<string, unknown>
  created_at: string
  updated_at: string
}

interface DashboardConfiguration {
  company_id: string
  widgets: DashboardWidget[]
}

interface DashboardConfigurationContextValue {
  configuration: DashboardConfiguration | null
  isLoading: boolean
  error: Error | null
  updateWidgetVisibility: (widgetId: string, isVisible: boolean) => Promise<void>
  updateWidgetOrder: (widgetId: string, newOrder: number) => Promise<void>
  updateWidgetSettings: (widgetId: string, settings: Record<string, unknown>) => Promise<void>
  isWidgetVisible: (widgetId: string) => boolean
  getWidgetOrder: (widgetId: string) => number
  getVisibleWidgets: () => DashboardWidget[]
}

const DashboardConfigurationContext = createContext<DashboardConfigurationContextValue | null>(null)

interface DashboardConfigurationProviderProps {
  children: React.ReactNode
  companyId: string
}

export function DashboardConfigurationProvider({
  children,
  companyId
}: DashboardConfigurationProviderProps) {
  const queryClient = useQueryClient()

  // Fetch dashboard configuration
  const {
    data: configuration,
    isLoading,
    error
  } = useQuery({
    queryKey: ['dashboard-configuration', companyId],
    queryFn: async (): Promise<DashboardConfiguration> => {
      const response = await fetch(`/api/business/dashboard-configuration?companyId=${companyId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard configuration')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!companyId
  })

  // Update configuration mutation
  const updateConfigurationMutation = useMutation({
    mutationFn: async (widgets: DashboardWidget[]) => {
      const response = await fetch(`/api/business/dashboard-configuration?companyId=${companyId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ widgets }),
      })

      if (!response.ok) {
        throw new Error('Failed to update dashboard configuration')
      }

      return response.json()
    },
    onSuccess: (data) => {
      // Update the query cache with the new data
      queryClient.setQueryData(['dashboard-configuration', companyId], data)
    },
    onError: (error) => {
      console.error('Failed to update dashboard configuration:', error)
    }
  })

  const updateWidgetVisibility = async (widgetId: string, isVisible: boolean) => {
    if (!configuration) return

    const updatedWidgets = configuration.widgets.map(widget =>
      widget.widget_id === widgetId
        ? { ...widget, is_visible: isVisible }
        : widget
    )

    await updateConfigurationMutation.mutateAsync(updatedWidgets)
  }

  const updateWidgetOrder = async (widgetId: string, newOrder: number) => {
    if (!configuration) return

    const updatedWidgets = configuration.widgets.map(widget =>
      widget.widget_id === widgetId
        ? { ...widget, display_order: newOrder }
        : widget
    )

    await updateConfigurationMutation.mutateAsync(updatedWidgets)
  }

  const updateWidgetSettings = async (widgetId: string, settings: Record<string, unknown>) => {
    if (!configuration) return

    const updatedWidgets = configuration.widgets.map(widget =>
      widget.widget_id === widgetId
        ? { ...widget, custom_settings: { ...widget.custom_settings, ...settings } }
        : widget
    )

    await updateConfigurationMutation.mutateAsync(updatedWidgets)
  }

  const isWidgetVisible = (widgetId: string): boolean => {
    if (!configuration) return true // Default to visible if no config
    const widget = configuration.widgets.find(w => w.widget_id === widgetId)
    return widget?.is_visible ?? true
  }

  const getWidgetOrder = (widgetId: string): number => {
    if (!configuration) return 0
    const widget = configuration.widgets.find(w => w.widget_id === widgetId)
    return widget?.display_order ?? 0
  }

  const getVisibleWidgets = (): DashboardWidget[] => {
    if (!configuration) return []
    return configuration.widgets
      .filter(widget => widget.is_visible)
      .sort((a, b) => a.display_order - b.display_order)
  }

  const contextValue: DashboardConfigurationContextValue = {
    configuration: configuration || null,
    isLoading,
    error,
    updateWidgetVisibility,
    updateWidgetOrder,
    updateWidgetSettings,
    isWidgetVisible,
    getWidgetOrder,
    getVisibleWidgets
  }

  return (
    <DashboardConfigurationContext.Provider value={contextValue}>
      {children}
    </DashboardConfigurationContext.Provider>
  )
}

export function useDashboardConfiguration() {
  const context = useContext(DashboardConfigurationContext)
  if (!context) {
    throw new Error('useDashboardConfiguration must be used within a DashboardConfigurationProvider')
  }
  return context
}

// Hook to check if a specific widget should be rendered
export function useWidgetVisibility(widgetId: string) {
  const { isWidgetVisible, isLoading } = useDashboardConfiguration()
  return {
    isVisible: isWidgetVisible(widgetId),
    isLoading
  }
}

// Hook to get widget order for sorting
export function useWidgetOrder(widgetId: string) {
  const { getWidgetOrder, isLoading } = useDashboardConfiguration()
  return {
    order: getWidgetOrder(widgetId),
    isLoading
  }
}
