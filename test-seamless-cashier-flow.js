/**
 * Test script for seamless cashier registration flow
 * This script tests the complete flow from invitation acceptance to login
 */

import { chromium } from 'playwright';

async function testSeamlessCashierFlow() {
  console.log('🚀 Starting seamless cashier registration flow test...');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Test the invitation acceptance page
    console.log('📧 Testing invitation acceptance page...');
    const invitationUrl = 'http://localhost:3000/cashiers/accept?token=ce25f396914fe32f1759b8465914f033';
    await page.goto(invitationUrl);
    
    // Wait for page to load
    await page.waitForSelector('h1, h2, h3', { timeout: 10000 });
    
    // Check if invitation details are loaded
    const pageTitle = await page.textContent('h1, h2, h3');
    console.log('📄 Page title:', pageTitle);
    
    // Look for the auto-create button
    const autoCreateButton = await page.locator('button:has-text("Create Account Automatically")');
    if (await autoCreateButton.isVisible()) {
      console.log('✅ Auto-create button found');
      
      // Click the auto-create button
      console.log('🔄 Clicking auto-create button...');
      await autoCreateButton.click();
      
      // Wait for the account creation to complete
      await page.waitForTimeout(3000);
      
      // Check if account was created successfully
      const successMessage = await page.locator('text=Account Created Successfully').first();
      if (await successMessage.isVisible()) {
        console.log('✅ Account created successfully!');
        
        // Look for temporary password
        const tempPasswordElement = await page.locator('[class*="font-mono"]').first();
        if (await tempPasswordElement.isVisible()) {
          const tempPassword = await tempPasswordElement.textContent();
          console.log('🔑 Temporary password generated:', tempPassword);
          
          // Get email from the page
          const emailElement = await page.locator('text=<EMAIL>').first();
          const email = await emailElement.textContent();
          console.log('📧 Email:', email);
          
          // Click "Go to Login" button
          const loginButton = await page.locator('button:has-text("Go to Login")');
          if (await loginButton.isVisible()) {
            console.log('🔄 Navigating to login page...');
            await loginButton.click();
            
            // Wait for login page to load
            await page.waitForURL('**/login');
            console.log('✅ Successfully redirected to login page');
            
            // Test login with temporary password
            console.log('🔐 Testing login with temporary password...');
            await page.fill('input[type="email"]', email);
            await page.fill('input[type="password"]', tempPassword);
            await page.click('button[type="submit"]');
            
            // Wait for potential redirect
            await page.waitForTimeout(3000);
            
            // Check if redirected to change password page
            const currentUrl = page.url();
            if (currentUrl.includes('/change-password')) {
              console.log('✅ Successfully redirected to change password page');
              console.log('🔄 Testing password change flow...');
              
              // Fill in password change form
              await page.fill('input[id="currentPassword"]', tempPassword);
              await page.fill('input[id="newPassword"]', 'NewSecurePassword123!');
              await page.fill('input[id="confirmPassword"]', 'NewSecurePassword123!');
              
              // Submit password change
              await page.click('button[type="submit"]');
              await page.waitForTimeout(3000);
              
              // Check if redirected to dashboard
              const finalUrl = page.url();
              if (finalUrl.includes('/dashboard')) {
                console.log('✅ Successfully completed password change and redirected to dashboard');
                console.log('🎉 SEAMLESS CASHIER REGISTRATION FLOW COMPLETED SUCCESSFULLY!');
              } else {
                console.log('⚠️ Password change completed but not redirected to dashboard. Current URL:', finalUrl);
              }
            } else {
              console.log('⚠️ Login successful but not redirected to change password. Current URL:', currentUrl);
            }
          } else {
            console.log('❌ "Go to Login" button not found');
          }
        } else {
          console.log('❌ Temporary password not found on page');
        }
      } else {
        console.log('❌ Account creation failed - success message not found');
      }
    } else {
      console.log('❌ Auto-create button not found');
      
      // Check for manual form as fallback
      const manualForm = await page.locator('form').first();
      if (await manualForm.isVisible()) {
        console.log('📝 Manual form found as fallback');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the test
testSeamlessCashierFlow().catch(console.error);
