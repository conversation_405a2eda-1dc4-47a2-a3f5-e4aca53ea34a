'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useQuery } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { 
  Users, 
  DollarSign, 
  Target, 
  TrendingUp,
  Activity,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
  Info
} from 'lucide-react'

interface BusinessMetrics {
  memberAcquisitionRate: {
    last30Days: number
    previous30Days: number
    growthRate: number
    trend: 'up' | 'down'
  }
  memberRetention: {
    retentionRate: number
    benchmark: number
    status: 'good' | 'fair' | 'poor'
  }
  averageTransactionValue: {
    value: number
    currency: string
    sampleSize: number
  }
  customerLifetimeValue: {
    avgPoints: number
    estimatedValue: number
    currency: string
  }
  programROI: {
    roi: number
    totalValue: number
    totalCost: number
    status: 'excellent' | 'good' | 'break-even' | 'loss'
  }
  memberEngagement: {
    score: number
    activeMembers: number
    totalTransactions: number
    frequency: 'high' | 'medium' | 'low'
  }
}

export default function BusinessAnalyticsDashboard() {
  const { company } = useCompany()

  const { data: businessData, isLoading, error } = useQuery({
    queryKey: ['businessMetrics', company?.id],
    queryFn: async () => {
      if (!company?.id) return { data: null }
      const response = await fetch(`/api/business-metrics?companyId=${company.id}`)
      if (!response.ok) throw new Error('Failed to fetch business metrics')
      return response.json()
    },
    enabled: !!company?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  })

  if (isLoading) {
    return <BusinessAnalyticsSkeleton />
  }

  if (error || !businessData?.data) {
    return (
      <div className="grid gap-6">
        <div className="text-center p-8 text-muted-foreground">
          <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-30" />
          <p>Unable to load business analytics</p>
          <p className="text-sm mt-1">Please try again later</p>
        </div>
      </div>
    )
  }

  const metrics: BusinessMetrics = businessData.data

  const businessCards = [
    {
      title: "Member Acquisition",
      explanation: "Number of new customers who joined your loyalty program in the last 30 days, with growth trend vs. previous period",
      value: metrics.memberAcquisitionRate.last30Days,
      subtitle: "New members (30d)",
      trend: {
        value: metrics.memberAcquisitionRate.growthRate,
        direction: metrics.memberAcquisitionRate.trend
      },
      icon: <Users className="h-5 w-5" />,
      color: "blue"
    },
    {
      title: "Member Retention",
      explanation: "Percentage of members (registered 30+ days ago) who remained active in the last 30 days. Industry benchmark for loyalty programs is 60%",
      value: `${metrics.memberRetention.retentionRate}%`,
      subtitle: `vs ${metrics.memberRetention.benchmark}% benchmark`,
      status: metrics.memberRetention.status,
      icon: <Target className="h-5 w-5" />,
      color: metrics.memberRetention.status === 'good' ? 'green' :
             metrics.memberRetention.status === 'fair' ? 'yellow' : 'red'
    },
    {
      title: "Avg Transaction Value",
      explanation: "Average monetary value of customer transactions that earned points in the last 30 days",
      value: `${metrics.averageTransactionValue.value} ${metrics.averageTransactionValue.currency}`,
      subtitle: `${metrics.averageTransactionValue.sampleSize} transactions`,
      icon: <DollarSign className="h-5 w-5" />,
      color: "emerald"
    },
    {
      title: "Customer Lifetime Value",
      explanation: "Average total points earned per customer since joining, representing their engagement and spending with your business",
      value: `${metrics.customerLifetimeValue.avgPoints} pts`,
      subtitle: `≈ ${metrics.customerLifetimeValue.estimatedValue} ${metrics.customerLifetimeValue.currency}`,
      icon: <TrendingUp className="h-5 w-5" />,
      color: "purple"
    },
    {
      title: "Program ROI",
      explanation: "Return on Investment of your loyalty program: (Total Customer Value - Reward Costs) / Reward Costs × 100",
      value: `${metrics.programROI.roi}%`,
      subtitle: `Status: ${metrics.programROI.status}`,
      status: metrics.programROI.status,
      icon: <BarChart3 className="h-5 w-5" />,
      color: metrics.programROI.status === 'excellent' ? 'green' :
             metrics.programROI.status === 'good' ? 'blue' :
             metrics.programROI.status === 'break-even' ? 'yellow' : 'red'
    },
    {
      title: "Member Engagement",
      explanation: "Average number of transactions per active member in the last 30 days. Higher scores indicate more frequent customer interactions",
      value: `${metrics.memberEngagement.score}`,
      subtitle: `${metrics.memberEngagement.frequency} frequency`,
      detail: `${metrics.memberEngagement.activeMembers} active members`,
      icon: <Activity className="h-5 w-5" />,
      color: metrics.memberEngagement.frequency === 'high' ? 'green' :
             metrics.memberEngagement.frequency === 'medium' ? 'yellow' : 'red'
    }
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "from-blue-50 to-blue-100/30 dark:from-blue-950/20 dark:to-blue-900/10 text-blue-600",
      green: "from-green-50 to-green-100/30 dark:from-green-950/20 dark:to-green-900/10 text-green-600",
      yellow: "from-yellow-50 to-yellow-100/30 dark:from-yellow-950/20 dark:to-yellow-900/10 text-yellow-600",
      red: "from-red-50 to-red-100/30 dark:from-red-950/20 dark:to-red-900/10 text-red-600",
      emerald: "from-emerald-50 to-emerald-100/30 dark:from-emerald-950/20 dark:to-emerald-900/10 text-emerald-600",
      purple: "from-purple-50 to-purple-100/30 dark:from-purple-950/20 dark:to-purple-900/10 text-purple-600",
      gray: "from-gray-50 to-gray-100/30 dark:from-gray-950/20 dark:to-gray-900/10 text-gray-600"
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            Business Analytics Summary
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {businessCards.map((card, index) => (
              <Card key={index} className="premium-card overflow-hidden hover:shadow-card-hover transition-all duration-200">
                <CardHeader className={`flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r ${getColorClasses(card.color)}`}>
                  <div className="flex items-center gap-2">
                    <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help transition-colors" />
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <p className="text-sm">{card.explanation}</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className={`rounded-full p-1.5 bg-background/80`}>
                    {card.icon}
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-3">
                    <div className="flex items-baseline">
                      <div className="text-2xl font-bold font-numeric">
                        {typeof card.value === 'number' && card.value.toLocaleString ? card.value.toLocaleString() : card.value}
                      </div>
                      {card.trend && (
                        <div className={`flex items-center ml-3 text-xs font-medium ${
                          card.trend.direction === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {card.trend.direction === 'up' ? (
                            <ArrowUpRight className="h-3 w-3 mr-1" />
                          ) : (
                            <ArrowDownRight className="h-3 w-3 mr-1" />
                          )}
                          {Math.abs(card.trend.value)}%
                        </div>
                      )}
                    </div>
                    <div className="space-y-1">
                      <p className="text-xs text-muted-foreground">{card.subtitle}</p>
                      {card.detail && (
                        <p className="text-xs text-muted-foreground">{card.detail}</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}

function BusinessAnalyticsSkeleton() {
  return (
    <div className="space-y-6">
      <div>
        <Skeleton className="h-6 w-48 mb-4" />
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-5 w-5 rounded" />
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <Skeleton className="h-8 w-16 mb-3" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
