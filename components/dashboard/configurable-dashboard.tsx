'use client'

import React from 'react'
import { DashboardConfigurationProvider } from '@/contexts/dashboard-configuration-context'
import { DashboardSettingsToggle } from '@/components/dashboard-settings'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ShoppingBag } from 'lucide-react'
import { useCompany } from '@/contexts/company-context'
import BusinessPurchaseAnalytics from '@/components/business-purchase-analytics'

interface ConfigurableDashboardProps {
  className?: string
}

function DashboardContent({ className }: ConfigurableDashboardProps) {
  return (
    <div className="space-y-4">
      {/* Dashboard Settings Toggle */}
      <div className="flex justify-end">
        <DashboardSettingsToggle />
      </div>

      {/* Original Business Purchase Analytics Component */}
      <BusinessPurchaseAnalytics className={className} />
    </div>
  )
}

export default function ConfigurableDashboard({ className }: ConfigurableDashboardProps) {
  const { company } = useCompany()

  if (!company?.id) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Business Purchase Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <ShoppingBag className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No company selected</p>
            <p className="text-sm">Please select a company to view analytics</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <DashboardConfigurationProvider companyId={company.id}>
      <DashboardContent className={className} />
    </DashboardConfigurationProvider>
  )
}
