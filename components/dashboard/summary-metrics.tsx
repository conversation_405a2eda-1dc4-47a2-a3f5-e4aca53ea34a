'use client'

import React from 'react'
import { DollarSign, Receipt, Users, Package, TrendingUp, TrendingDown } from 'lucide-react'
import { useWidgetVisibility } from '@/contexts/dashboard-configuration-context'

interface SummaryMetricsData {
  total_revenue: number
  total_receipts: number
  total_members: number
  avg_order_value: number
  total_items: number
  total_transactions: number
}

interface GrowthData {
  revenue_growth: number
  customer_growth: number
}

interface SummaryMetricsProps {
  summary: SummaryMetricsData
  growth: GrowthData
  recentActivity: {
    receipts: number
    new_members: number
  }
}

export function SummaryMetrics({ summary, growth, recentActivity }: SummaryMetricsProps) {
  const { isVisible, isLoading } = useWidgetVisibility('summary_metrics')

  if (isLoading) return <div className="h-32 bg-muted animate-pulse rounded-lg" />
  if (!isVisible) return null

  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-medium text-blue-600">Total Revenue</span>
        </div>
        <div className="text-2xl font-bold text-blue-700 dark:text-blue-300">
          ETB {summary.total_revenue.toLocaleString()}
        </div>
        {growth.revenue_growth !== 0 && (
          <div className={`flex items-center gap-1 text-xs ${
            growth.revenue_growth > 0 ? 'text-green-600' : 'text-red-600'
          }`}>
            {growth.revenue_growth > 0 ? (
              <TrendingUp className="h-3 w-3" />
            ) : (
              <TrendingDown className="h-3 w-3" />
            )}
            {Math.abs(growth.revenue_growth).toFixed(1)}% vs last month
          </div>
        )}
      </div>

      <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
        <div className="flex items-center gap-2">
          <Receipt className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium text-green-600">Total Orders</span>
        </div>
        <div className="text-2xl font-bold text-green-700 dark:text-green-300">
          {summary.total_receipts.toLocaleString()}
        </div>
        <div className="text-xs text-muted-foreground">
          {recentActivity.receipts} in last 30 days
        </div>
      </div>

      <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-purple-600" />
          <span className="text-sm font-medium text-purple-600">Total Members</span>
        </div>
        <div className="text-2xl font-bold text-purple-700 dark:text-purple-300">
          {summary.total_members.toLocaleString()}
        </div>
        <div className="text-xs text-muted-foreground">
          +{recentActivity.new_members} new this month
        </div>
      </div>

      <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
        <div className="flex items-center gap-2">
          <Package className="h-4 w-4 text-orange-600" />
          <span className="text-sm font-medium text-orange-600">Avg Order Value</span>
        </div>
        <div className="text-2xl font-bold text-orange-700 dark:text-orange-300">
          ETB {summary.avg_order_value.toFixed(0)}
        </div>
        <div className="text-xs text-muted-foreground">
          {summary.total_items} unique items
        </div>
      </div>
    </div>
  )
}
