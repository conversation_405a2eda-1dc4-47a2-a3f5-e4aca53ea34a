'use client'

import { useState } from 'react'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { QRCodeSVG } from 'qrcode.react'
import {
  MessageCircle,
  Send,
  Copy,
  QrCode,
  CheckCircle,
  Loader2,
  UserPlus,
  Type
} from 'lucide-react'

interface SeamlessCashierInviteProps {
  companyId: string
  companyName: string
  trigger?: React.ReactNode
}

interface InvitationResponse {
  success: boolean
  invitation: {
    id: string
    email: string
    expiresAt: string
    invitationToken: string
    invitationLink: string
    telegramLink: string
  }
  qrCode?: string
}

export function SeamlessCashierInvite({
  companyId,
  companyName,
  trigger
}: SeamlessCashierInviteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    name: ''
  })
  const [invitationResult, setInvitationResult] = useState<InvitationResponse | null>(null)
  const [copiedLink, setCopiedLink] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email) {
      toast.error('Please enter an email address')
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/cashiers/invite-seamless', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          name: formData.name,
          companyId
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation')
      }

      setInvitationResult(data)
      toast.success('🎉 Cashier invitation created successfully!')

    } catch (error) {
      console.error('Error sending invitation:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to send invitation')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedLink(true)
      setTimeout(() => setCopiedLink(false), 2000)
      toast.success('Invitation link copied!')
    } catch {
      toast.error('Failed to copy to clipboard')
    }
  }

  const shareViaWhatsApp = () => {
    if (!invitationResult?.invitation.invitationLink) return

    const message = `Hi${formData.name ? ` ${formData.name}` : ''}! 👋\n\nYou've been invited to be a cashier at ${companyName}!\n\nClick this link to get started:\n${invitationResult.invitation.invitationLink}\n\nJust click the link and follow the instructions to create your account. Welcome to the team! 🎉`

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const shareViaSMS = () => {
    if (!invitationResult?.invitation.invitationLink) return

    const message = `Hi${formData.name ? ` ${formData.name}` : ''}! You've been invited to be a cashier at ${companyName}. Click: ${invitationResult.invitation.invitationLink}`
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`
    window.open(smsUrl)
  }

  const resetForm = () => {
    setFormData({ email: '', name: '' })
    setInvitationResult(null)
    setCopiedLink(false)
    setShowQRCode(false)
  }

  const handleClose = () => {
    setIsOpen(false)
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open)
      if (!open) resetForm()
    }}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="gap-2">
            <UserPlus className="h-4 w-4" />
            Invite Cashier
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Invite New Cashier
          </DialogTitle>
          <DialogDescription>
            Create a seamless invitation link for your new cashier. They just need to click the link!
          </DialogDescription>
        </DialogHeader>

        {!invitationResult ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Cashier Details</CardTitle>
                <CardDescription>
                  Just enter their email and we&apos;ll create a direct signup link!
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Full Name (Optional)</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  />
                  <p className="text-xs text-muted-foreground">
                    Used for personalized messages
                  </p>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Creating Invitation...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Create Invitation
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="space-y-6">
            {/* Success Message */}
            <div className="flex items-center gap-2 p-4 bg-green-50 rounded-lg border border-green-200">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">Invitation Created Successfully!</p>
                <p className="text-sm text-green-600">
                  Share the link below with your new cashier
                </p>
              </div>
            </div>

            {/* Invitation Link */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Invitation Link</CardTitle>
                <CardDescription>
                  Share this link via any messaging app - they just need to click it!
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={invitationResult.invitation.invitationLink}
                    readOnly
                    className="font-mono text-sm"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard(invitationResult.invitation.invitationLink)}
                  >
                    {copiedLink ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>

                {/* QR Code Toggle */}
                <div className="flex items-center justify-between">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowQRCode(!showQRCode)}
                    className="gap-2"
                  >
                    <QrCode className="h-4 w-4" />
                    {showQRCode ? 'Hide' : 'Show'} QR Code
                  </Button>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={shareViaWhatsApp}
                      className="gap-2"
                    >
                      <MessageCircle className="h-4 w-4" />
                      WhatsApp
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={shareViaSMS}
                      className="gap-2"
                    >
                      <Type className="h-4 w-4" />
                      SMS
                    </Button>
                  </div>
                </div>

                {/* QR Code */}
                {showQRCode && (
                  <div className="flex justify-center p-4 bg-white rounded-lg border">
                    <QRCodeSVG
                      value={invitationResult.invitation.invitationLink}
                      size={200}
                      level="M"
                      includeMargin
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">What Happens Next?</CardTitle>
              </CardHeader>
              <CardContent>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Share the link with your new cashier</li>
                  <li>They click the link and it opens the signup page</li>
                  <li>They create an account with their name and password</li>
                  <li>They&apos;re automatically assigned the cashier role</li>
                  <li>They&apos;re guided through the signup process</li>
                  <li>You&apos;ll get notified when they complete setup</li>
                </ol>

                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    💡 <strong>Pro Tip:</strong> The invitation expires in 7 days.
                    If they don&apos;t use it by then, just create a new one!
                  </p>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button variant="outline" onClick={resetForm}>
                Create Another Invitation
              </Button>
              <Button onClick={handleClose}>
                Done
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
