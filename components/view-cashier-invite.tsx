'use client'

import { useState } from 'react'
import { toast } from 'sonner'
import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { QRCodeSVG } from 'qrcode.react'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  MessageCircle,
  Copy,
  QrCode,
  CheckCircle,
  Loader2,
  Eye,
  Type,
  Trash2,
  RefreshCw
} from 'lucide-react'

interface ViewCashierInviteProps {
  invitationId: string
}

interface InvitationDetails {
  id: string
  email: string
  createdAt: string
  expiresAt: string
  usedAt: string | null
  invitationLink: string
  telegramLink: string
  companyName: string
}

export function ViewCashierInvite({ invitationId }: ViewCashierInviteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [invitationDetails, setInvitationDetails] = useState<InvitationDetails | null>(null)
  const [copiedLink, setCopiedLink] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const fetchInvitationDetails = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/cashiers/invite/${invitationId}/get-details`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(errorText)
      }

      const data = await response.json()
      setInvitationDetails(data.invitation)
      setShowQRCode(true) // Show QR code by default when viewing an existing invitation
    } catch (error) {
      console.error('Error fetching invitation details:', error)
      toast.error('Failed to load invitation details')
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenDialog = () => {
    setIsOpen(true)
    fetchInvitationDetails()
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedLink(true)
      setTimeout(() => setCopiedLink(false), 2000)
      toast.success('Invitation link copied!')
    } catch {
      toast.error('Failed to copy to clipboard')
    }
  }

  const shareViaWhatsApp = () => {
    if (!invitationDetails?.invitationLink) return

    const message = `Hi! 👋\n\nYou've been invited to be a cashier at ${invitationDetails.companyName}!\n\nClick this link to get started:\n${invitationDetails.invitationLink}\n\nJust click the link and follow the instructions. Welcome to the team! 🎉`

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  const shareViaSMS = () => {
    if (!invitationDetails?.invitationLink) return

    const message = `Hi! You've been invited to be a cashier at ${invitationDetails.companyName}. Click: ${invitationDetails.invitationLink}`
    const smsUrl = `sms:?body=${encodeURIComponent(message)}`
    window.open(smsUrl)
  }

  const deleteInvitation = async () => {
    if (!invitationDetails) return
    
    setIsDeleting(true)
    try {
      const response = await fetch(`/api/cashiers/invite/${invitationId}/delete`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete invitation')
      }

      const data = await response.json()
      toast.success(data.message)
      handleClose()
      // Refresh the parent component by triggering a page reload or callback
      window.location.reload()
    } catch (error) {
      console.error('Error deleting invitation:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to delete invitation')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleClose = () => {
    setIsOpen(false)
    setInvitationDetails(null)
    setCopiedLink(false)
    setShowQRCode(false)
    setIsDeleting(false)
  }

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleOpenDialog}
        className="text-blue-500 hover:text-blue-700"
      >
        <Eye className="h-4 w-4" />
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              View Cashier Invitation
            </DialogTitle>
            <DialogDescription>
              Share the invitation link or QR code with your cashier
            </DialogDescription>
          </DialogHeader>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : invitationDetails ? (
            <div className="space-y-6">
              {/* Invitation Link */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Invitation Link</CardTitle>
                  <CardDescription>
                    Share this link via any messaging app - they just need to click it!
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={invitationDetails.invitationLink}
                      readOnly
                      className="font-mono text-sm"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(invitationDetails.invitationLink)}
                    >
                      {copiedLink ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {/* QR Code Toggle */}
                  <div className="flex items-center justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowQRCode(!showQRCode)}
                      className="gap-2"
                    >
                      <QrCode className="h-4 w-4" />
                      {showQRCode ? 'Hide' : 'Show'} QR Code
                    </Button>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={shareViaWhatsApp}
                        className="gap-2"
                      >
                        <MessageCircle className="h-4 w-4" />
                        WhatsApp
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={shareViaSMS}
                        className="gap-2"
                      >
                        <Type className="h-4 w-4" />
                        SMS
                      </Button>
                    </div>
                  </div>

                  {/* QR Code */}
                  {showQRCode && (
                    <div className="flex justify-center p-4 bg-white rounded-lg border">
                      <QRCodeSVG
                        value={invitationDetails.invitationLink}
                        size={200}
                        level="M"
                        includeMargin
                      />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">What Happens Next?</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                      <h4 className="font-semibold text-green-800 mb-2">✨ Seamless Registration (Recommended)</h4>
                      <ol className="list-decimal list-inside space-y-1 text-sm text-green-700">
                        <li>Share the link with your new cashier</li>
                        <li>They click the link and see the invitation page</li>
                        <li>They click &quot;Create Account Automatically&quot;</li>
                        <li>System generates a secure temporary password instantly</li>
                        <li>They can login immediately and are forced to change password</li>
                        <li>Ready to work in under 2 minutes!</li>
                      </ol>
                    </div>

                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-2">📝 Manual Registration (Alternative)</h4>
                      <ol className="list-decimal list-inside space-y-1 text-sm text-blue-700">
                        <li>They can also choose to fill out a registration form</li>
                        <li>Enter their name and create their own password</li>
                        <li>Account created with their chosen credentials</li>
                      </ol>
                    </div>
                  </div>

                  <div className="mt-4 p-3 bg-amber-50 rounded-lg border border-amber-200">
                    <p className="text-sm text-amber-800">
                      ⏰ <strong>Expiration:</strong> The invitation expires {new Date(invitationDetails.expiresAt) < new Date() ? 'has expired' : `in ${Math.ceil((new Date(invitationDetails.expiresAt).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days`}.
                      {new Date(invitationDetails.expiresAt) < new Date() ? ' Create a new one if needed!' : ''}
                    </p>
                  </div>

                  {invitationDetails.usedAt && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                      <p className="text-sm text-green-800">
                        ✅ <strong>Invitation Accepted:</strong> This invitation was used on {new Date(invitationDetails.usedAt).toLocaleDateString()}.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Delete/Retry Section for Used Invitations */}
              {invitationDetails.usedAt && (
                <Card className="border-orange-200 bg-orange-50">
                  <CardHeader>
                    <CardTitle className="text-lg text-orange-800 flex items-center gap-2">
                      <RefreshCw className="h-5 w-5" />
                      Need to Retry?
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-orange-700 mb-4">
                      This invitation has already been used. If there were issues with the registration 
                      or you need to re-invite this person, you can delete this invitation and create a new one.
                    </p>
                    <div className="bg-orange-100 p-3 rounded-lg mb-4">
                      <p className="text-xs text-orange-600 font-medium">⚠️ Warning:</p>
                      <p className="text-xs text-orange-600">
                        Deleting this invitation will also remove the user&apos;s access to your business 
                        if they had already registered. You&apos;ll need to send them a new invitation.
                      </p>
                    </div>
                    <Button
                      onClick={deleteInvitation}
                      disabled={isDeleting}
                      variant="destructive"
                      size="sm"
                      className="w-full"
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Deleting...
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete & Allow Retry
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              )}

              <DialogFooter>
                <Button onClick={handleClose}>
                  Close
                </Button>
              </DialogFooter>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <p>Failed to load invitation details. Please try again.</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
