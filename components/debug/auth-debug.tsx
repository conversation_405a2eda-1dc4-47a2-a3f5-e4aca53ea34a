'use client'

import { useAuth } from '@/hooks/use-auth'
import { useCompanyAdminQuery } from '@/hooks/use-company-admin-query'
import { useUserRole } from '@/hooks/use-user-role'
import { useCompany } from '@/contexts/company-context'
import { useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'

export function AuthDebug() {
  const auth = useAuth()
  const adminQuery = useCompanyAdminQuery()
  const userRole = useUserRole()
  const company = useCompany()
  const queryClient = useQueryClient()

  const handleRefreshAuth = () => {
    console.log('🔄 Manually invalidating auth cache...')
    queryClient.invalidateQueries({ queryKey: ['auth'] })
    queryClient.invalidateQueries({ queryKey: ['company', 'admin'] })
  }

  const handleRefreshCompany = () => {
    console.log('🔄 Manually refreshing company data...')
    company.refreshCompany()
    queryClient.invalidateQueries({ queryKey: ['company'] })
  }

  const handleHardRefresh = () => {
    console.log('🔄 Hard refresh - clearing all caches...')
    queryClient.clear()
    window.location.reload()
  }

  return (
    <div className="p-4 border rounded-lg bg-muted/50 space-y-4">
      <h3 className="font-semibold">Auth Debug Info</h3>

      <div className="space-y-2 text-sm">
        <div>
          <strong>Auth State:</strong>
          <pre className="text-xs bg-background p-2 rounded mt-1">
            {JSON.stringify({
              hasUser: !!auth.user,
              userEmail: auth.user?.email,
              isLoading: auth.isLoading,
              isAuthenticated: auth.isAuthenticated,
              error: auth.error?.message
            }, null, 2)}
          </pre>
        </div>

        <div>
          <strong>Admin Query:</strong>
          <pre className="text-xs bg-background p-2 rounded mt-1">
            {JSON.stringify({
              hasAdminData: !!adminQuery.adminData,
              companyId: adminQuery.companyId,
              isAdmin: adminQuery.isAdmin,
              isLoading: adminQuery.isLoading,
              role: adminQuery.adminData?.role
            }, null, 2)}
          </pre>
        </div>

        <div>
          <strong>User Role:</strong>
          <pre className="text-xs bg-background p-2 rounded mt-1">
            {JSON.stringify({
              role: userRole.role,
              isOwner: userRole.isOwner,
              isCashier: userRole.isCashier,
              hasRole: userRole.hasRole
            }, null, 2)}
          </pre>
        </div>

        <div>
          <strong>Company Context:</strong>
          <pre className="text-xs bg-background p-2 rounded mt-1">
            {JSON.stringify({
              hasCompany: !!company.company,
              companyId: company.company?.id,
              companyName: company.company?.name,
              isLoading: company.isLoading,
              error: company.error?.message
            }, null, 2)}
          </pre>
        </div>
      </div>

      <div className="flex gap-2">
        <Button onClick={handleRefreshAuth} variant="outline" size="sm">
          Refresh Auth
        </Button>
        <Button onClick={handleRefreshCompany} variant="outline" size="sm">
          Refresh Company
        </Button>
        <Button onClick={handleHardRefresh} variant="destructive" size="sm">
          Hard Refresh
        </Button>
      </div>
    </div>
  )
}
