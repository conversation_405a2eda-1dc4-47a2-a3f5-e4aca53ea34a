'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Switch } from '@/components/ui/switch'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Settings, Save, RotateCcw, Eye, EyeOff } from 'lucide-react'
import { useDashboardConfiguration } from '@/contexts/dashboard-configuration-context'
import { toast } from 'sonner'

interface DashboardSettingsProps {
  isOpen: boolean
  onClose: () => void
}

export function DashboardSettings({ isOpen, onClose }: DashboardSettingsProps) {
  const {
    configuration,
    isLoading,
    updateWidgetVisibility
  } = useDashboardConfiguration()

  const [hasChanges, setHasChanges] = useState(false)
  const [localSettings, setLocalSettings] = useState<Record<string, boolean>>({})

  // Initialize local settings when configuration loads
  React.useEffect(() => {
    if (configuration && !isLoading) {
      const settings: Record<string, boolean> = {}
      configuration.widgets.forEach(widget => {
        settings[widget.widget_id] = widget.is_visible
      })
      setLocalSettings(settings)
      setHasChanges(false)
    }
  }, [configuration, isLoading])

  const handleToggle = (widgetId: string, isVisible: boolean) => {
    setLocalSettings(prev => ({
      ...prev,
      [widgetId]: isVisible
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    try {
      // Update all widgets that have changed
      const promises = Object.entries(localSettings).map(([widgetId, isVisible]) => {
        const originalWidget = configuration?.widgets.find(w => w.widget_id === widgetId)
        if (originalWidget && originalWidget.is_visible !== isVisible) {
          return updateWidgetVisibility(widgetId, isVisible)
        }
        return Promise.resolve()
      })

      await Promise.all(promises)
      setHasChanges(false)
      toast.success('Dashboard settings saved successfully!')
    } catch (error) {
      console.error('Failed to save dashboard settings:', error)
      toast.error('Failed to save dashboard settings. Please try again.')
    }
  }

  const handleReset = () => {
    if (configuration) {
      const settings: Record<string, boolean> = {}
      configuration.widgets.forEach(widget => {
        settings[widget.widget_id] = widget.is_visible
      })
      setLocalSettings(settings)
      setHasChanges(false)
    }
  }

  const getVisibleCount = () => {
    return Object.values(localSettings).filter(Boolean).length
  }

  const getTotalCount = () => {
    return Object.keys(localSettings).length
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="space-y-1">
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Dashboard Settings
            </CardTitle>
            <CardDescription>
              Customize which analytics widgets are visible on your dashboard.
              {getTotalCount() > 0 && (
                <span className="block mt-1 text-sm">
                  {getVisibleCount()} of {getTotalCount()} widgets enabled
                </span>
              )}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onClose}>
            Close
          </Button>
        </CardHeader>

        <CardContent className="space-y-4 max-h-[50vh] overflow-y-auto">
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="h-4 bg-muted rounded w-32 animate-pulse" />
                    <div className="h-3 bg-muted rounded w-20 animate-pulse" />
                  </div>
                  <div className="h-6 w-11 bg-muted rounded-full animate-pulse" />
                </div>
              ))}
            </div>
          ) : (
            configuration?.widgets
              .sort((a, b) => a.display_order - b.display_order)
              .map((widget) => (
                <div
                  key={widget.widget_id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="space-y-1">
                    <Label
                      htmlFor={`widget-${widget.widget_id}`}
                      className="text-sm font-medium cursor-pointer flex items-center gap-2"
                    >
                      {localSettings[widget.widget_id] ? (
                        <Eye className="h-4 w-4 text-green-500" />
                      ) : (
                        <EyeOff className="h-4 w-4 text-muted-foreground" />
                      )}
                      {widget.widget_name}
                    </Label>
                    <p className="text-xs text-muted-foreground">
                      Widget ID: {widget.widget_id}
                    </p>
                  </div>
                  <Switch
                    id={`widget-${widget.widget_id}`}
                    checked={localSettings[widget.widget_id] || false}
                    onCheckedChange={(checked) => handleToggle(widget.widget_id, checked)}
                  />
                </div>
              ))
          )}

          {!isLoading && configuration?.widgets.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Settings className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No dashboard widgets found.</p>
              <p className="text-sm">Please refresh the page or contact support if this persists.</p>
            </div>
          )}
        </CardContent>

        {!isLoading && configuration && configuration.widgets.length > 0 && (
          <div className="border-t p-4">
            <div className="flex items-center justify-between gap-3">
              <div className="text-sm text-muted-foreground">
                {hasChanges ? (
                  <span className="text-amber-600 font-medium">• Unsaved changes</span>
                ) : (
                  <span className="text-green-600">• All changes saved</span>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  disabled={!hasChanges}
                  className="gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  Reset
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={!hasChanges}
                  size="sm"
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  Save Changes
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  )
}

// Simple toggle button for opening dashboard settings
export function DashboardSettingsToggle() {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="gap-2"
      >
        <Settings className="h-4 w-4" />
        Dashboard Settings
      </Button>

      <DashboardSettings
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  )
}
