'use client'

import { useState } from 'react'
import { useCompany } from '@/contexts/company-context'
import { useRole } from '@/hooks/use-role'
import { createClient } from '@/lib/supabase/client'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'

// UI Components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Loader2, Plus, UserPlus, Trash2, Clock, Mail, AlertCircle, CheckCircle2, XCircle } from 'lucide-react'
import { SeamlessCashierInvite } from '@/components/seamless-cashier-invite'
import { ViewCashierInvite } from '@/components/view-cashier-invite'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface Administrator {
  id: string
  email: string
  name: string
  phone_number?: string
}

interface Cashier {
  id: string
  administrator_id: string
  company_id: string
  role: string
  created_at: string
  administrator: Administrator
}

interface CashierApiResponse {
  id: string
  administratorId: string
  name: string
  email: string
  isActive: boolean
  createdAt: string
}

interface PendingInvitation {
  id: string
  email: string
  created_at: string
  expires_at: string
  used_at: string | null
}

export function CashierManagement() {
  const { company } = useCompany()
  const { isOwner, role } = useRole()

  console.log('CashierManagement - Debug Info:', {
    companyId: company?.id,
    role,
    isOwner,
  })
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newCashier, setNewCashier] = useState({
    email: '',
    name: '',
    phone: ''
  })
  // Using defaultValue for Tabs instead of state
  const queryClient = useQueryClient()

  // Query to fetch active cashiers
  const { data: cashiers, isLoading } = useQuery({
    queryKey: ['cashiers', company?.id],
    queryFn: async () => {
      if (!company?.id) {
        console.log('CashierManagement: No company ID available')
        return []
      }

      console.log('CashierManagement: Fetching cashiers for company:', company.id)

      // First test authentication
      const debugResponse = await fetch('/api/debug-auth', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      const debugResult = await debugResponse.json()
      console.log('Debug auth result:', debugResult)

      // Use the API route with credentials to ensure cookies are sent
      const response = await fetch(`/api/cashiers?companyId=${company.id}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error fetching cashiers:', errorText)
        toast.error('Failed to load cashiers')
        return []
      }

      const result = await response.json()
      console.log('CashierManagement: API result:', result)

      // Transform API response to match the Cashier interface
      const cashiers: Cashier[] = (result.cashiers || []).map((item: CashierApiResponse) => ({
        id: item.id,
        administrator_id: item.administratorId,
        company_id: company.id,
        role: 'CASHIER',
        created_at: item.createdAt,
        administrator: {
          id: item.administratorId,
          email: item.email,
          name: item.name,
          phone_number: undefined
        }
      }))

      console.log('CashierManagement: Transformed cashiers:', cashiers)
      return cashiers
    },
    enabled: !!company?.id && isOwner
  })

  // Query to fetch pending invitations
  const { data: pendingInvitations, isLoading: isLoadingInvitations } = useQuery({
    queryKey: ['cashier-invitations', company?.id],
    queryFn: async () => {
      if (!company?.id) {
        console.log('CashierManagement: No company ID available for invitations')
        return []
      }

      console.log('CashierManagement: Fetching pending invitations for company:', company.id)

      // Use the API route to fetch pending invitations
      const response = await fetch(`/api/cashiers/invite?companyId=${company.id}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Error fetching pending invitations:', errorText)
        toast.error('Failed to load pending invitations')
        return []
      }

      const result = await response.json()
      console.log('CashierManagement: Pending invitations result:', result)
      
      return result.invitations || []
    },
    enabled: !!company?.id && isOwner
  })

  // Mutation to add a new cashier
  const addCashierMutation = useMutation({
    mutationFn: async (cashierData: typeof newCashier) => {
      if (!company?.id) throw new Error('Company ID not found')

      const supabase = createClient()

      // Call the add_cashier function we created in SQL
      const { data, error } = await supabase
        .rpc('add_cashier', {
          p_company_id: company.id,
          p_email: cashierData.email,
          p_name: cashierData.name,
          p_phone_number: cashierData.phone || null
        })

      if (error) throw error

      return data
    },
    onSuccess: () => {
      toast.success('Cashier added successfully')
      setIsAddDialogOpen(false)
      setNewCashier({ email: '', name: '', phone: '' })
      queryClient.invalidateQueries({ queryKey: ['cashiers', company?.id] })
    },
    onError: (error) => {
      console.error('Error adding cashier:', error)
      toast.error('Failed to add cashier')
    }
  })

  // Mutation to cancel a pending or accepted invitation
  const cancelInvitationMutation = useMutation({
    mutationFn: async (invitationId: string) => {
      console.log(`Cancelling invitation with ID: ${invitationId}`)
      const response = await fetch(`/api/cashiers/invite/${invitationId}/delete`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error(`Error response from server: ${response.status}`, errorText)
        throw new Error(errorText)
      }

      return response.json()
    },
    onSuccess: () => {
      toast.success('Invitation cancelled successfully')
      queryClient.invalidateQueries({ queryKey: ['cashier-invitations', company?.id] })
    },
    onError: (error) => {
      console.error('Error cancelling invitation:', error)
      toast.error('Failed to cancel invitation')
    }
  })

  // Mutation to remove a cashier
  const removeCashierMutation = useMutation({
    mutationFn: async (cashierId: string) => {
      const supabase = createClient()

      const { error } = await supabase
        .from('company_administrators')
        .delete()
        .eq('id', cashierId)

      if (error) throw error
    },
    onSuccess: () => {
      toast.success('Cashier removed successfully')
      queryClient.invalidateQueries({ queryKey: ['cashiers', company?.id] })
    },
    onError: (error) => {
      console.error('Error removing cashier:', error)
      toast.error('Failed to remove cashier')
    }
  })

  // Handle form submission
  const handleAddCashier = (e: React.FormEvent) => {
    e.preventDefault()
    addCashierMutation.mutate(newCashier)
  }

  // If not an owner, don't show this component
  if (!isOwner) {
    return (
      <div className="p-4 text-center text-muted-foreground">
        <p>Only business owners can manage staff members.</p>
        <p className="text-sm mt-2">Current role: {role || 'Loading...'}</p>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <UserPlus className="h-5 w-5 text-muted-foreground" />
            <span>Cashier Management</span>
          </div>
          <div className="flex gap-2">
            {/* Seamless Direct URL Invite Button */}
            <SeamlessCashierInvite
              companyId={company?.id || ''}
              companyName={company?.name || ''}
            />

            {/* Traditional Add Cashier Dialog */}
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Cashier
                </Button>
              </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Cashier</DialogTitle>
                <DialogDescription>
                  Add a new cashier to manage day-to-day operations. They will have access to members and transactions.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddCashier}>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newCashier.email}
                      onChange={(e) => setNewCashier({ ...newCashier, email: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      placeholder="John Doe"
                      value={newCashier.name}
                      onChange={(e) => setNewCashier({ ...newCashier, name: e.target.value })}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number (Optional)</Label>
                    <Input
                      id="phone"
                      placeholder="+1234567890"
                      value={newCashier.phone}
                      onChange={(e) => setNewCashier({ ...newCashier, phone: e.target.value })}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsAddDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={addCashierMutation.isPending}
                  >
                    {addCashierMutation.isPending && (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    Add Cashier
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
          </div>
        </CardTitle>
        <CardDescription>
          Manage cashiers who can process transactions and manage members. Invite new cashiers with direct signup links.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="active" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="active" className="flex items-center gap-1">
              <CheckCircle2 className="h-4 w-4" />
              Active Cashiers
            </TabsTrigger>
            <TabsTrigger value="pending" className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              Pending Invitations
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="active">
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : cashiers && cashiers.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Added On</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cashiers.map((cashier) => (
                    <TableRow key={cashier.id}>
                      <TableCell className="font-medium">{cashier.administrator.name}</TableCell>
                      <TableCell>{cashier.administrator.email}</TableCell>
                      <TableCell>{cashier.administrator.phone_number || '-'}</TableCell>
                      <TableCell>{new Date(cashier.created_at).toLocaleDateString()}</TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCashierMutation.mutate(cashier.id)}
                          disabled={removeCashierMutation.isPending}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>No cashiers found. Add a cashier to manage day-to-day operations.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="pending">
            {isLoadingInvitations ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            ) : pendingInvitations && pendingInvitations.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Invited</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingInvitations.map((invitation: PendingInvitation) => (
                    <TableRow key={invitation.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          {invitation.email}
                        </div>
                      </TableCell>
                      <TableCell>{formatDistanceToNow(new Date(invitation.created_at), { addSuffix: true })}</TableCell>
                      <TableCell>
                        {new Date(invitation.expires_at) < new Date() ? (
                          <div className="flex items-center gap-1 text-red-500">
                            <AlertCircle className="h-4 w-4" />
                            Expired
                          </div>
                        ) : (
                          formatDistanceToNow(new Date(invitation.expires_at), { addSuffix: true })
                        )}
                      </TableCell>
                      <TableCell>
                        {invitation.used_at ? (
                          <div className="flex items-center gap-1 text-green-500">
                            <CheckCircle2 className="h-4 w-4" />
                            Accepted
                          </div>
                        ) : new Date(invitation.expires_at) < new Date() ? (
                          <div className="flex items-center gap-1 text-red-500">
                            <XCircle className="h-4 w-4" />
                            Expired
                          </div>
                        ) : (
                          <div className="flex items-center gap-1 text-amber-500">
                            <Clock className="h-4 w-4" />
                            Pending
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        {/* Show actions for both pending and accepted invitations */}
                        <div className="flex justify-end gap-2">
                          <ViewCashierInvite invitationId={invitation.id} />
                          {/* Only show delete button if not expired */}
                          {new Date(invitation.expires_at) > new Date() && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => cancelInvitationMutation.mutate(invitation.id)}
                              disabled={cancelInvitationMutation.isPending}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <p>No pending invitations found.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
        
        {/* Debug info */}
        {(isLoading || isLoadingInvitations) && (
          <div className="mt-4 p-3 bg-muted rounded text-xs text-left">
            <p><strong>Debug Info:</strong></p>
            <p>Company ID: {company?.id || 'Not available'}</p>
            <p>Role: {role || 'Not available'}</p>
            <p>Is Owner: {isOwner ? 'Yes' : 'No'}</p>
            <p>Query Enabled: {!!(company?.id && isOwner) ? 'Yes' : 'No'}</p>
            <p>Cashiers Array: {cashiers ? `Length: ${cashiers.length}` : 'Null/Undefined'}</p>
            <p>Invitations Array: {pendingInvitations ? `Length: ${pendingInvitations.length}` : 'Null/Undefined'}</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
