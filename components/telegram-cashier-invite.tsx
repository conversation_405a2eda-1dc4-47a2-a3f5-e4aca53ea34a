'use client'

import { useState } from 'react'
import { toast } from 'sonner'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { QRCodeSVG } from 'qrcode.react'
import {
  MessageCircle,
  Send,
  Copy,
  QrCode,
  ExternalLink,
  CheckCircle,
  Loader2,
  AlertCircle
} from 'lucide-react'

interface TelegramCashierInviteProps {
  companyId: string
  companyName: string
  trigger?: React.ReactNode
}

interface InvitationResponse {
  message: string
  invitation: {
    id: string
    email: string
    expiresAt: string
    invitationLink: string
  }
  telegram: {
    sent: boolean
    error?: string
    chatId?: string
  }
}

export function TelegramCashierInvite({
  companyId,
  trigger
}: TelegramCashierInviteProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    telegramChatId: ''
  })
  const [invitationResult, setInvitationResult] = useState<InvitationResponse | null>(null)
  const [copiedLink, setCopiedLink] = useState(false)
  const [copiedChatId, setCopiedChatId] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email || !formData.telegramChatId) {
      toast.error('Please fill in all required fields')
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/cashiers/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          companyId,
          telegramChatId: formData.telegramChatId
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to send invitation')
      }

      setInvitationResult(data)

      if (data.telegram.sent) {
        toast.success('🎉 Cashier invitation sent successfully via Telegram!')
      } else if (data.telegram.error) {
        toast.warning(`Invitation created but Telegram failed: ${data.telegram.error}`)
      } else {
        toast.success('Invitation created successfully')
      }

    } catch (error) {
      console.error('Error sending invitation:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to send invitation')
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = async (text: string, type: 'link' | 'chatid') => {
    try {
      await navigator.clipboard.writeText(text)
      if (type === 'link') {
        setCopiedLink(true)
        setTimeout(() => setCopiedLink(false), 2000)
        toast.success('Invitation link copied!')
      } else {
        setCopiedChatId(true)
        setTimeout(() => setCopiedChatId(false), 2000)
        toast.success('Chat ID copied!')
      }
    } catch {
      toast.error('Failed to copy to clipboard')
    }
  }

  const resetForm = () => {
    setFormData({ email: '', name: '', telegramChatId: '' })
    setInvitationResult(null)
    setCopiedLink(false)
    setCopiedChatId(false)
    setShowQRCode(false)
  }

  const handleClose = () => {
    setIsOpen(false)
    resetForm()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open)
      if (!open) resetForm()
    }}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="gap-2">
            <MessageCircle className="h-4 w-4" />
            Invite via Telegram
          </Button>
        )}
      </DialogTrigger>

      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="h-5 w-5" />
            Invite Cashier via Telegram
          </DialogTitle>
          <DialogDescription>
            Send a cashier invitation directly through Telegram with QR code support
          </DialogDescription>
        </DialogHeader>

        {!invitationResult ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Cashier Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Cashier Details</CardTitle>
                <CardDescription>
                  Information about the person you&apos;re inviting to be a cashier
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="name">Full Name (Optional)</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Telegram Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Telegram Details</CardTitle>
                <CardDescription>
                  Send the invitation directly to their Telegram account
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="telegramChatId">Telegram Chat ID *</Label>
                  <div className="flex gap-2">
                    <Input
                      id="telegramChatId"
                      placeholder="e.g., *********"
                      value={formData.telegramChatId}
                      onChange={(e) => setFormData({ ...formData, telegramChatId: e.target.value })}
                      required
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      onClick={() => copyToClipboard(formData.telegramChatId, 'chatid')}
                      disabled={!formData.telegramChatId}
                    >
                      {copiedChatId ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Ask the person to send a message to @Loyal_ET_Bot to get their Chat ID
                  </p>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">How to get Telegram Chat ID:</p>
                      <ol className="text-sm text-blue-800 mt-1 ml-4 list-decimal">
                        <li>Ask the person to search for &quot;@Loyal_ET_Bot&quot; in Telegram</li>
                        <li>They should send any message to the bot (e.g., &quot;Hello&quot;)</li>
                        <li>The bot will reply with their Chat ID</li>
                        <li>They can share this ID with you to receive the invitation</li>
                      </ol>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Invitation
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          /* Invitation Result Display */
          <div className="space-y-6">
            {/* Success Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="h-5 w-5" />
                  Invitation Sent Successfully!
                </CardTitle>
                <CardDescription>
                  {invitationResult.telegram.sent
                    ? 'The cashier invitation has been sent via Telegram'
                    : 'Invitation created - manual sharing required'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Telegram Status */}
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <MessageCircle className="h-4 w-4" />
                      <span className="font-medium">Telegram Delivery</span>
                    </div>
                    <Badge variant={invitationResult.telegram.sent ? "default" : "destructive"}>
                      {invitationResult.telegram.sent ? "Sent" : "Failed"}
                    </Badge>
                  </div>

                  {invitationResult.telegram.error && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <p className="text-sm text-red-800">
                        <strong>Error:</strong> {invitationResult.telegram.error}
                      </p>
                    </div>
                  )}

                  {/* Invitation Link */}
                  <div className="space-y-2">
                    <Label>Invitation Link (Backup)</Label>
                    <div className="flex gap-2">
                      <Input
                        value={invitationResult.invitation.invitationLink}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(invitationResult.invitation.invitationLink, 'link')}
                      >
                        {copiedLink ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => window.open(invitationResult.invitation.invitationLink, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* QR Code Toggle */}
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowQRCode(!showQRCode)}
                      className="flex-1"
                    >
                      <QrCode className="h-4 w-4 mr-2" />
                      {showQRCode ? 'Hide' : 'Show'} QR Code
                    </Button>
                  </div>

                  {/* QR Code Display */}
                  {showQRCode && (
                    <div className="flex justify-center p-4 bg-white border rounded-lg">
                      <QRCodeSVG
                        value={invitationResult.invitation.invitationLink}
                        size={200}
                        level="M"
                        includeMargin
                      />
                    </div>
                  )}

                  {/* Expiration Info */}
                  <div className="text-sm text-muted-foreground">
                    <p>
                      <strong>Expires:</strong>{' '}
                      {new Date(invitationResult.invitation.expiresAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <DialogFooter>
              <Button onClick={handleClose} className="w-full">
                <CheckCircle className="h-4 w-4 mr-2" />
                Done
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}
