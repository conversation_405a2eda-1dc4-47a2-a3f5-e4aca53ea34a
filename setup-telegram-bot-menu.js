// Setup Telegram Bot Menu
// Run this script to configure the bot menu with all available commands

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN

if (!TELEGRAM_BOT_TOKEN) {
  console.error('❌ Error: TELEGRAM_BOT_TOKEN environment variable is not set')
  console.error('Please set your Telegram bot token in .env.local:')
  console.error('TELEGRAM_BOT_TOKEN=your_bot_token_here')
  process.exit(1)
}

const commands = [
  { command: 'start', description: 'Initialize bot and link account' },
  { command: 'link', description: 'Get account linking instructions' },
  { command: 'balance', description: 'Check your points balance' },
  { command: 'tier', description: 'View your tier status and benefits' },
  { command: 'rewards', description: 'Browse available rewards' },
  { command: 'history', description: 'View transaction history' },
  { command: 'profile', description: 'View your profile information' },
  { command: 'settings', description: 'Bot preferences' },
  { command: 'help', description: 'Show help message' },
  { command: 'unlink', description: 'Unlink your account' }
]

async function setupBotMenu() {
  try {
    console.log('🤖 Setting up Telegram Bot Menu...')

    const response = await fetch(`https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/setMyCommands`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ commands })
    })

    const result = await response.json()

    if (result.ok) {
      console.log('✅ Bot menu setup completed successfully!')
      console.log('')
      console.log('🎉 IMPORTANT: The menu is now active for ALL users!')
      console.log('')
      console.log('What happens next:')
      console.log('• NEW users will immediately see the menu when they start the bot')
      console.log('• EXISTING users will see the menu when they next open the chat')
      console.log('• Users may need to restart their Telegram app to see the menu')
      console.log('• The menu appears as a button (☰) next to the message input')
      console.log('')
      console.log('Available commands:')
      commands.forEach(cmd => {
        console.log(`  /${cmd.command} - ${cmd.description}`)
      })
      console.log('')
      console.log('📱 For existing users who don\'t see the menu:')
      console.log('• Tell them to type "/" to see all commands')
      console.log('• They can restart their Telegram app')
      console.log('• They can type /help to see all available commands')
    } else {
      console.error('❌ Failed to setup bot menu:', result)
      process.exit(1)
    }
  } catch (error) {
    console.error('❌ Error setting up bot menu:', error)
    process.exit(1)
  }
}

setupBotMenu()
