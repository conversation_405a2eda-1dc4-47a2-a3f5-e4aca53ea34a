-- Create dashboard configurations table
CREATE TABLE IF NOT EXISTS dashboard_configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  widget_id VARCHAR(100) NOT NULL,
  widget_name VA<PERSON>HAR(200) NOT NULL,
  is_visible BOOLEAN NOT NULL DEFAULT true,
  display_order INTEGER DEFAULT 0,
  custom_settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure one configuration per widget per company
  UNIQUE(company_id, widget_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_dashboard_configurations_company_id
ON dashboard_configurations(company_id);

-- Create index for ordering
CREATE INDEX IF NOT EXISTS idx_dashboard_configurations_company_order
ON dashboard_configurations(company_id, display_order);

-- Add RLS policies
ALTER TABLE dashboard_configurations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see configs for their company
CREATE POLICY "Users can view dashboard configurations for their company"
ON dashboard_configurations
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM companies
    WHERE companies.id = dashboard_configurations.company_id
    AND companies.administrator_id = auth.uid()
  )
);

-- Policy: Users can update configs for their company
CREATE POLICY "Users can update dashboard configurations for their company"
ON dashboard_configurations
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM companies
    WHERE companies.id = dashboard_configurations.company_id
    AND companies.administrator_id = auth.uid()
  )
);

-- Insert default widget configurations
INSERT INTO dashboard_configurations (company_id, widget_id, widget_name, is_visible, display_order)
SELECT
  c.id as company_id,
  widget.widget_id,
  widget.widget_name,
  true as is_visible,
  widget.display_order
FROM companies c
CROSS JOIN (
  VALUES
    ('summary_metrics', 'Summary Metrics', 1),
    ('recent_activity', 'Recent Activity', 2),
    ('top_items_quantity', 'Top Items by Quantity', 3),
    ('top_items_revenue', 'Top Items by Revenue', 4),
    ('category_performance', 'Category Performance', 5),
    ('weekly_item_trends', 'Weekly Item Trends', 6),
    ('weekly_spender_trends', 'Weekly Spender Trends', 7),
    ('monthly_trends', 'Monthly Trends', 8),
    ('member_analytics', 'Member Analytics', 9)
) AS widget(widget_id, widget_name, display_order)
ON CONFLICT (company_id, widget_id) DO NOTHING;

-- Create function to auto-add default configs for new companies
CREATE OR REPLACE FUNCTION add_default_dashboard_config()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO dashboard_configurations (company_id, widget_id, widget_name, is_visible, display_order)
  VALUES
    (NEW.id, 'summary_metrics', 'Summary Metrics', true, 1),
    (NEW.id, 'recent_activity', 'Recent Activity', true, 2),
    (NEW.id, 'top_items_quantity', 'Top Items by Quantity', true, 3),
    (NEW.id, 'top_items_revenue', 'Top Items by Revenue', true, 4),
    (NEW.id, 'category_performance', 'Category Performance', true, 5),
    (NEW.id, 'weekly_item_trends', 'Weekly Item Trends', true, 6),
    (NEW.id, 'weekly_spender_trends', 'Weekly Spender Trends', true, 7),
    (NEW.id, 'monthly_trends', 'Monthly Trends', true, 8),
    (NEW.id, 'member_analytics', 'Member Analytics', true, 9)
  ON CONFLICT (company_id, widget_id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new companies
DROP TRIGGER IF EXISTS trigger_add_default_dashboard_config ON companies;
CREATE TRIGGER trigger_add_default_dashboard_config
  AFTER INSERT ON companies
  FOR EACH ROW
  EXECUTE FUNCTION add_default_dashboard_config();
