-- Updated script to fix all Supabase performance warnings
-- This script addresses auth_rls_initplan warnings and multiple permissive policies

-- =============================================
-- FIX AUTH_RLS_INITPLAN WARNINGS
-- =============================================

-- Fix for item_matching_suggestions table
DROP POLICY IF EXISTS item_matching_suggestions_access ON public.item_matching_suggestions;
CREATE POLICY item_matching_suggestions_access ON public.item_matching_suggestions
USING (
  EXISTS (
    SELECT 1
    FROM receipt_items ri
    JOIN receipts r ON ri.receipt_id = r.id
    JOIN company_administrators ca ON r.company_id = ca.company_id
    WHERE ri.id = item_matching_suggestions.receipt_item_id 
    AND ca.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for business_items table
DROP POLICY IF EXISTS business_items_admin_access ON public.business_items;
CREATE POLICY business_items_admin_access ON public.business_items
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
)
WITH CHECK (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for loyalty_members table
DROP POLICY IF EXISTS loyalty_members_admin_access ON public.loyalty_members;
CREATE POLICY loyalty_members_admin_access ON public.loyalty_members
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for points_transactions table
DROP POLICY IF EXISTS points_transactions_admin_access ON public.points_transactions;
CREATE POLICY points_transactions_admin_access ON public.points_transactions
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for rewards table
DROP POLICY IF EXISTS rewards_admin_access ON public.rewards;
CREATE POLICY rewards_admin_access ON public.rewards
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for dashboard_metrics_history table
DROP POLICY IF EXISTS dashboard_metrics_admin_access ON public.dashboard_metrics_history;
CREATE POLICY dashboard_metrics_admin_access ON public.dashboard_metrics_history
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for member_notifications table
DROP POLICY IF EXISTS member_notifications_admin_access ON public.member_notifications;
CREATE POLICY member_notifications_admin_access ON public.member_notifications
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for program_rules table
DROP POLICY IF EXISTS program_rules_admin_access ON public.program_rules;
CREATE POLICY program_rules_admin_access ON public.program_rules
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for receipt_items table
DROP POLICY IF EXISTS receipt_items_admin_access ON public.receipt_items;
CREATE POLICY receipt_items_admin_access ON public.receipt_items
TO authenticated
USING (
  receipt_id IN (
    SELECT receipts.id
    FROM receipts
    WHERE receipts.company_id IN (
      SELECT company_administrators.company_id
      FROM company_administrators
      WHERE company_administrators.administrator_id = (SELECT auth.uid())
    )
  )
);

-- Fix for receipt_templates table
DROP POLICY IF EXISTS receipt_templates_admin_access ON public.receipt_templates;
CREATE POLICY receipt_templates_admin_access ON public.receipt_templates
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for receipts table
DROP POLICY IF EXISTS receipts_admin_access ON public.receipts;
CREATE POLICY receipts_admin_access ON public.receipts
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for reward_redemptions table
DROP POLICY IF EXISTS reward_redemptions_admin_access ON public.reward_redemptions;
CREATE POLICY reward_redemptions_admin_access ON public.reward_redemptions
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for reward_tier_eligibility table
DROP POLICY IF EXISTS reward_tier_eligibility_admin_access ON public.reward_tier_eligibility;
CREATE POLICY reward_tier_eligibility_admin_access ON public.reward_tier_eligibility
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for tier_definitions table
DROP POLICY IF EXISTS tier_definitions_admin_access ON public.tier_definitions;
CREATE POLICY tier_definitions_admin_access ON public.tier_definitions
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for marketing_campaigns table
DROP POLICY IF EXISTS marketing_campaigns_company_access ON public.marketing_campaigns;
CREATE POLICY marketing_campaigns_company_access ON public.marketing_campaigns
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for campaign_recipients table
DROP POLICY IF EXISTS campaign_recipients_company_access ON public.campaign_recipients;
CREATE POLICY campaign_recipients_company_access ON public.campaign_recipients
TO authenticated
USING (
  campaign_id IN (
    SELECT marketing_campaigns.id
    FROM marketing_campaigns
    WHERE marketing_campaigns.company_id IN (
      SELECT company_administrators.company_id
      FROM company_administrators
      WHERE company_administrators.administrator_id = (SELECT auth.uid())
    )
  )
);

-- Fix for cashier_invitations table
DROP POLICY IF EXISTS "Company admins can manage invitations for their company" ON public.cashier_invitations;
CREATE POLICY "Company admins can manage invitations for their company" ON public.cashier_invitations
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
    AND company_administrators.role IN ('OWNER', 'ADMIN')
  )
);

-- Fix for company_administrators table
DROP POLICY IF EXISTS company_administrators_access ON public.company_administrators;
CREATE POLICY company_administrators_access ON public.company_administrators
TO authenticated
USING (
  company_id IN (
    SELECT companies.id
    FROM companies
    WHERE companies.administrator_id = (SELECT auth.uid())
  )
  OR administrator_id = (SELECT auth.uid())
);

-- =============================================
-- FIX MULTIPLE PERMISSIVE POLICIES WARNINGS
-- =============================================

-- Fix for companies table - consolidate companies_direct_access and companies_insert_policy
DROP POLICY IF EXISTS companies_direct_access ON public.companies;
DROP POLICY IF EXISTS companies_insert_policy ON public.companies;
CREATE POLICY companies_consolidated_policy ON public.companies
FOR INSERT
TO authenticated
WITH CHECK (
  -- Combined logic from both policies
  administrator_id = (SELECT auth.uid())
  OR
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = (SELECT auth.uid())
  )
);

-- Fix for loyalty_members table - consolidate cashier_members_access and loyalty_members_admin_access
DROP POLICY IF EXISTS cashier_members_access ON public.loyalty_members;
-- loyalty_members_admin_access already dropped above
CREATE POLICY loyalty_members_consolidated_access ON public.loyalty_members
TO authenticated
USING (
  -- Combined logic from both policies
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
  OR
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role = ANY (ARRAY['OWNER', 'CASHIER'])
  )
);

-- Fix for points_transactions table - consolidate cashier_transactions_access and points_transactions_admin_access
DROP POLICY IF EXISTS cashier_transactions_access ON public.points_transactions;
-- points_transactions_admin_access already dropped above
CREATE POLICY points_transactions_consolidated_access ON public.points_transactions
TO authenticated
USING (
  -- Combined logic from both policies
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
  OR
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role = ANY (ARRAY['OWNER', 'CASHIER'])
  )
);

-- Fix for reward_redemptions table - consolidate cashier_redemptions_access and reward_redemptions_admin_access
DROP POLICY IF EXISTS cashier_redemptions_access ON public.reward_redemptions;
-- reward_redemptions_admin_access already dropped above
CREATE POLICY reward_redemptions_consolidated_access ON public.reward_redemptions
TO authenticated
USING (
  -- Combined logic from both policies
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
  OR
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role = ANY (ARRAY['OWNER', 'CASHIER'])
  )
);

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Run these after applying the fixes to verify changes

-- Verify auth_rls_initplan fixes
SELECT tablename, policyname, qual 
FROM pg_policies 
WHERE tablename IN (
  'item_matching_suggestions', 'business_items', 'loyalty_members',
  'points_transactions', 'rewards', 'dashboard_metrics_history',
  'member_notifications', 'program_rules', 'receipt_items',
  'receipt_templates', 'receipts', 'reward_redemptions',
  'reward_tier_eligibility', 'tier_definitions', 'marketing_campaigns',
  'campaign_recipients', 'cashier_invitations', 'company_administrators'
)
ORDER BY tablename, policyname;

-- Verify multiple_permissive_policies fixes
SELECT tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE tablename IN (
  'companies', 'loyalty_members', 'points_transactions', 'reward_redemptions'
)
ORDER BY tablename, policyname;
