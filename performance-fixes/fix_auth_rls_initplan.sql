-- Fix auth_rls_initplan warnings by wrapping auth.uid() calls in subqueries
-- This improves performance by ensuring auth functions are only called once per query instead of once per row

-- Fix for item_matching_suggestions table
DROP POLICY IF EXISTS item_matching_suggestions_access ON public.item_matching_suggestions;
CREATE POLICY item_matching_suggestions_access ON public.item_matching_suggestions
USING (
  EXISTS (
    SELECT 1
    FROM receipt_items ri
    JOIN receipts r ON ri.receipt_id = r.id
    JOIN company_administrators ca ON r.company_id = ca.company_id
    WHERE ri.id = item_matching_suggestions.receipt_item_id 
    AND ca.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for business_items table
DROP POLICY IF EXISTS business_items_admin_access ON public.business_items;
CREATE POLICY business_items_admin_access ON public.business_items
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
)
WITH CHECK (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for loyalty_members table
DROP POLICY IF EXISTS loyalty_members_admin_access ON public.loyalty_members;
CREATE POLICY loyalty_members_admin_access ON public.loyalty_members
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for points_transactions table
DROP POLICY IF EXISTS points_transactions_admin_access ON public.points_transactions;
CREATE POLICY points_transactions_admin_access ON public.points_transactions
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for rewards table
DROP POLICY IF EXISTS rewards_admin_access ON public.rewards;
CREATE POLICY rewards_admin_access ON public.rewards
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for dashboard_metrics_history table
DROP POLICY IF EXISTS dashboard_metrics_admin_access ON public.dashboard_metrics_history;
CREATE POLICY dashboard_metrics_admin_access ON public.dashboard_metrics_history
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for member_notifications table
DROP POLICY IF EXISTS member_notifications_admin_access ON public.member_notifications;
CREATE POLICY member_notifications_admin_access ON public.member_notifications
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for program_rules table
DROP POLICY IF EXISTS program_rules_admin_access ON public.program_rules;
CREATE POLICY program_rules_admin_access ON public.program_rules
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for receipt_items table
DROP POLICY IF EXISTS receipt_items_admin_access ON public.receipt_items;
CREATE POLICY receipt_items_admin_access ON public.receipt_items
TO authenticated
USING (
  receipt_id IN (
    SELECT receipts.id
    FROM receipts
    WHERE receipts.company_id IN (
      SELECT company_administrators.company_id
      FROM company_administrators
      WHERE company_administrators.administrator_id = (SELECT auth.uid())
    )
  )
);

-- Fix for receipt_templates table
DROP POLICY IF EXISTS receipt_templates_admin_access ON public.receipt_templates;
CREATE POLICY receipt_templates_admin_access ON public.receipt_templates
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for receipts table
DROP POLICY IF EXISTS receipts_admin_access ON public.receipts;
CREATE POLICY receipts_admin_access ON public.receipts
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for reward_redemptions table
DROP POLICY IF EXISTS reward_redemptions_admin_access ON public.reward_redemptions;
CREATE POLICY reward_redemptions_admin_access ON public.reward_redemptions
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for reward_tier_eligibility table
DROP POLICY IF EXISTS reward_tier_eligibility_admin_access ON public.reward_tier_eligibility;
CREATE POLICY reward_tier_eligibility_admin_access ON public.reward_tier_eligibility
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for tier_definitions table
DROP POLICY IF EXISTS tier_definitions_admin_access ON public.tier_definitions;
CREATE POLICY tier_definitions_admin_access ON public.tier_definitions
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for marketing_campaigns table
DROP POLICY IF EXISTS marketing_campaigns_company_access ON public.marketing_campaigns;
CREATE POLICY marketing_campaigns_company_access ON public.marketing_campaigns
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
);

-- Fix for campaign_recipients table
DROP POLICY IF EXISTS campaign_recipients_company_access ON public.campaign_recipients;
CREATE POLICY campaign_recipients_company_access ON public.campaign_recipients
TO authenticated
USING (
  campaign_id IN (
    SELECT marketing_campaigns.id
    FROM marketing_campaigns
    WHERE marketing_campaigns.company_id IN (
      SELECT company_administrators.company_id
      FROM company_administrators
      WHERE company_administrators.administrator_id = (SELECT auth.uid())
    )
  )
);

-- Fix for cashier_invitations table
DROP POLICY IF EXISTS "Company admins can manage invitations for their company" ON public.cashier_invitations;
CREATE POLICY "Company admins can manage invitations for their company" ON public.cashier_invitations
TO authenticated
USING (
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
    AND company_administrators.role IN ('OWNER', 'ADMIN')
  )
);

-- Fix for company_administrators table
DROP POLICY IF EXISTS company_administrators_access ON public.company_administrators;
CREATE POLICY company_administrators_access ON public.company_administrators
TO authenticated
USING (
  company_id IN (
    SELECT companies.id
    FROM companies
    WHERE companies.administrator_id = (SELECT auth.uid())
  )
  OR administrator_id = (SELECT auth.uid())
);
