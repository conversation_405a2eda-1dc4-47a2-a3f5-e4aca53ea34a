-- Fix multiple permissive policies warnings by consolidating policies with the same role and action
-- This improves performance by reducing the number of policy checks Postgres needs to perform

-- Fix for companies table - consolidate companies_direct_access and companies_insert_policy
DROP POLICY IF EXISTS companies_direct_access ON public.companies;
DROP POLICY IF EXISTS companies_insert_policy ON public.companies;
CREATE POLICY companies_consolidated_policy ON public.companies
FOR INSERT
TO authenticated
WITH CHECK (
  -- Combined logic from both policies
  administrator_id = (SELECT auth.uid())
  OR
  EXISTS (
    SELECT 1 FROM auth.users
    WHERE id = (SELECT auth.uid())
  )
);

-- Fix for loyalty_members table - consolidate cashier_members_access and loyalty_members_admin_access
DROP POLICY IF EXISTS cashier_members_access ON public.loyalty_members;
DROP POLICY IF EXISTS loyalty_members_admin_access ON public.loyalty_members;
CREATE POLICY loyalty_members_consolidated_access ON public.loyalty_members
TO authenticated
USING (
  -- Combined logic from both policies
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
  OR
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role = ANY (ARRAY['OWNER', 'CASHIER'])
  )
);

-- Fix for points_transactions table - consolidate cashier_transactions_access and points_transactions_admin_access
DROP POLICY IF EXISTS cashier_transactions_access ON public.points_transactions;
DROP POLICY IF EXISTS points_transactions_admin_access ON public.points_transactions;
CREATE POLICY points_transactions_consolidated_access ON public.points_transactions
TO authenticated
USING (
  -- Combined logic from both policies
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
  OR
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role = ANY (ARRAY['OWNER', 'CASHIER'])
  )
);

-- Fix for reward_redemptions table - consolidate cashier_redemptions_access and reward_redemptions_admin_access
DROP POLICY IF EXISTS cashier_redemptions_access ON public.reward_redemptions;
DROP POLICY IF EXISTS reward_redemptions_admin_access ON public.reward_redemptions;
CREATE POLICY reward_redemptions_consolidated_access ON public.reward_redemptions
TO authenticated
USING (
  -- Combined logic from both policies
  company_id IN (
    SELECT company_administrators.company_id
    FROM company_administrators
    WHERE company_administrators.administrator_id = (SELECT auth.uid())
  )
  OR
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role = ANY (ARRAY['OWNER', 'CASHIER'])
  )
);
