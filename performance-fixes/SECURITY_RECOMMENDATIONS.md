# Security Recommendations

## Extension in Public Schema Warning

**Issue**: The `pg_trgm` extension is installed in the `public` schema, which poses a security risk.

**Solution Options**:

### Option 1: Move Extension to Extensions Schema (Recommended)
```sql
-- Create extensions schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS extensions;

-- Move the extension
ALTER EXTENSION pg_trgm SET SCHEMA extensions;
```

### Option 2: Recreate Extension in Correct Schema (Use with Caution)
```sql
-- WARNING: Only use if no critical functionality depends on pg_trgm
DROP EXTENSION IF EXISTS pg_trgm;
CREATE SCHEMA IF NOT EXISTS extensions;
CREATE EXTENSION IF NOT EXISTS pg_trgm SCHEMA extensions;
```

## Leaked Password Protection

**Issue**: Supa<PERSON> Auth's leaked password protection is currently disabled.

**Recommendation**: Enable leaked password protection in your Supabase dashboard:
1. Go to Authentication → Settings
2. Enable "Password strength and leaked password protection"
3. This will check passwords against HaveIBeenPwned.org database

**Benefits**:
- Prevents users from using compromised passwords
- Enhances overall security posture
- No performance impact on your application
