// Test database connection with service role client
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://vqltspteqqllvhyiupkf.supabase.co';
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxbHRzcHRlcXFsbHZoeWl1cGtmIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjU2MDEwMSwiZXhwIjoyMDU4MTM2MTAxfQ.dLpfmGuitOpLQ3AB_Xlj-d-nwWx_pJOAbte5wT35h0Y';

async function testDatabaseConnection() {
  console.log('🧪 Testing Database Connection...\n');

  const supabase = createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  console.log('✅ Supabase client created');
  console.log('URL:', supabaseUrl);
  console.log('Service Role Key (first 20 chars):', serviceRoleKey.substring(0, 20) + '...');

  // Test 1: Basic connection
  try {
    const { data, error } = await supabase
      .from('cashier_invitations')
      .select('count')
      .limit(1);

    console.log('\n📊 Basic connection test:');
    console.log('Success:', !error);
    if (error) {
      console.log('Error:', error);
    } else {
      console.log('Data:', data);
    }
  } catch (err) {
    console.log('❌ Basic connection failed:', err.message);
  }

  // Test 2: Find specific invitation
  const testToken = 'fe910a087df736f6a964539546cef9c7';
  try {
    const { data: invitation, error } = await supabase
      .from('cashier_invitations')
      .select('*')
      .eq('invitation_token', testToken)
      .is('used_at', null)
      .gt('expires_at', new Date().toISOString())
      .single();

    console.log('\n🔍 Invitation lookup test:');
    console.log('Token:', testToken);
    console.log('Found invitation:', !!invitation);
    console.log('Error:', error?.message || 'None');

    if (invitation) {
      console.log('Invitation details:');
      console.log('- ID:', invitation.id);
      console.log('- Email:', invitation.email);
      console.log('- Expires:', invitation.expires_at);
      console.log('- Used:', invitation.used_at);
    }
  } catch (err) {
    console.log('❌ Invitation lookup failed:', err.message);
  }

  // Test 3: Update invitation (simulate acceptance)
  try {
    const { error: updateError } = await supabase
      .from('cashier_invitations')
      .update({
        telegram_chat_id: '999999',
        // Don't actually mark as used for this test
      })
      .eq('invitation_token', testToken);

    console.log('\n✏️ Update test:');
    console.log('Update success:', !updateError);
    if (updateError) {
      console.log('Update error:', updateError);
    }
  } catch (err) {
    console.log('❌ Update test failed:', err.message);
  }

  console.log('\n🎯 Test completed!');
}

testDatabaseConnection().catch(console.error);
