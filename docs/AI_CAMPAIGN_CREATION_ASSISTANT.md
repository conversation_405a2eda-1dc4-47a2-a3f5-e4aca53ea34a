# AI Campaign Creation Assistant Specification

## Overview
An intelligent, conversational AI assistant that helps marketing administrators create compelling marketing campaigns through natural language interaction. Built with Vercel AI SDK and powered by Google Gemini 2.5 Flash, this assistant provides an intuitive, chat-based interface for campaign creation with real-time streaming responses.

## Core Features

### 1. Conversational Campaign Builder
- **Natural Language Processing**: Users describe their campaign goals, target audience, and preferences in plain English
- **Interactive Guidance**: AI asks clarifying questions to understand campaign requirements
- **Real-time Streaming**: Responses stream in real-time for immediate feedback
- **Context Awareness**: Maintains conversation context throughout the campaign creation process

### 2. Campaign Generation Capabilities
- **Multi-format Content**: Generate email campaigns, SMS messages, push notifications, and social media posts
- **Personalization**: Create personalized content based on customer segments and behavior
- **A/B Testing Suggestions**: Propose multiple campaign variations for testing
- **Brand Consistency**: Maintain brand voice and messaging consistency across all generated content

### 3. Target Audience Intelligence
- **Audience Segmentation**: Suggest optimal customer segments based on campaign goals
- **Behavioral Insights**: Leverage customer data to recommend targeting strategies
- **Timing Optimization**: Recommend optimal send times based on audience behavior
- **Channel Selection**: Suggest best communication channels for different segments

### 4. Campaign Editing & Refinement
- **Live Editing**: Real-time editing of generated campaigns with AI assistance
- **Content Optimization**: AI-powered suggestions for improving engagement
- **Compliance Checking**: Ensure campaigns meet marketing regulations and best practices
- **Preview Generation**: Visual previews of how campaigns will appear to recipients

## Technical Architecture

### Frontend Components
```
/app/marketing/campaigns/create/ai/
├── page.tsx                 # Main campaign creation page
├── components/
│   ├── campaign-agent.tsx   # Main chat UI component with AI integration
│   ├── CampaignPreview.tsx  # Live campaign preview
│   └── AudienceSelector.tsx # Target audience selection
└── hooks/
    ├── use-campaign-chat.ts # Chat state management
    └── use-audience-data.ts # Audience data fetching
```

### Backend API Routes
```
/app/api/marketing/campaigns/create/ai/
├── agent-chat/route.ts     # Main chat endpoint with Gemini integration
├── generate/route.ts       # Campaign generation endpoint
└── audience/route.ts       # Audience data endpoint
```

### AI Integration Stack
- **Vercel AI SDK**: Latest version with streaming and tool support
- **Google Gemini 2.5 Flash**: Primary AI model for conversation and generation
- **Streaming Protocol**: Real-time response streaming for immediate feedback
- **Tool Integration**: Custom tools for campaign generation and audience analysis

## User Experience Flow

### 1. Initial Conversation
```
AI: "Hi! I'm your AI marketing assistant. I'll help you create an amazing campaign. 
    What kind of campaign are you looking to create today?"

User: "I want to create an email campaign to promote our new loyalty program features"

AI: "Great! A loyalty program promotion email sounds exciting. Let me ask a few questions 
    to create the perfect campaign for you:
    
    1. Who is your target audience? (new members, existing members, or specific segments?)
    2. What are the key new features you want to highlight?
    3. What's your main goal? (sign-ups, engagement, feature adoption?)
    4. Do you have any specific tone or style preferences?"
```

### 2. Campaign Generation
```
AI: "Perfect! Based on our conversation, I'm creating a personalized email campaign. 
    Here's what I'm generating:
    
    📧 **Email Campaign: 'Unlock New Rewards'**
    🎯 **Target**: Active members (last 30 days)
    📅 **Suggested Send Time**: Tuesday 10 AM
    
    [Streaming campaign content appears in real-time]
    
    Would you like me to create variations for A/B testing or adjust anything?"
```

### 3. Refinement & Editing
```
AI: "I notice you'd like to make the subject line more urgent. Here are some options:
    
    1. 'New Rewards Available - Limited Time!'
    2. 'Your Exclusive Rewards Expire Soon'
    3. 'Don't Miss: Enhanced Loyalty Benefits'
    
    Which style resonates with your brand voice?"
```

## Technical Implementation Details

### 1. Chat Interface Component
```typescript
// app/marketing/campaigns/create/ai/components/campaign-agent.tsx
'use client';

import { useChat } from 'ai/react';
import { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Sparkles, Send, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
}

interface Campaign {
  type: 'email' | 'sms' | 'push' | 'social';
  objective: string;
  targetAudience: string;
  content: {
    subject: string;
    body: string;
    cta: string;
  };
  status: 'draft' | 'review' | 'scheduled' | 'sent';
}

interface ConversationState {
  validation: {
    completeness: number;
    isComplete: boolean;
  };
}

export function CampaignAgent() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCampaign, setGeneratedCampaign] = useState<Campaign | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversationState, setConversationState] = useState<ConversationState>({ 
    validation: { completeness: 0, isComplete: false } 
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Handle campaign generation
  const handleCampaignGeneration = useCallback((requirements: Record<string, unknown>) => {
    setIsGenerating(true);

    // Safely extract nested properties with type checking
    const contentDetails = requirements.contentDetails as Record<string, unknown> || {};
    const targetAudience = requirements.targetAudience as Record<string, unknown> || {};
    
    // Convert agent requirements to campaign format
    const campaignType = requirements.type as string;
    const validType = ['email', 'sms', 'push', 'social'].includes(campaignType) ? 
      campaignType as 'email' | 'sms' | 'push' | 'social' : 'email';
      
    const campaign: Campaign = {
      type: validType,
      objective: (requirements.objective as string) || '',
      targetAudience: (targetAudience.description as string) || '',
      content: {
        subject: (contentDetails.subject as string) || '',
        body: (contentDetails.body as string) || '',
        cta: (contentDetails.cta as string) || '',
      },
      status: 'draft',
    };

    // Simulate API call to create campaign
    setTimeout(() => {
      setGeneratedCampaign(campaign);
      setIsGenerating(false);
    }, 1500);
  }, []);

  // Initialize chat with AI SDK
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/marketing/campaigns/create/ai/agent-chat',
    id: 'campaign-agent',
    body: {
      sessionId: 'campaign-session-' + Date.now(),
    },
    onResponse: async (response) => {
      // Extract conversation state from headers
      const stateHeader = response.headers.get('X-Conversation-State');
      if (stateHeader) {
        try {
          const state = JSON.parse(stateHeader);
          setConversationState(state);
          
          // If requirements are complete, generate campaign
          if (state.validation.isComplete && !isGenerating && !generatedCampaign) {
            // Fetch full conversation state
            const sessionId = 'campaign-session-' + Date.now();
            const stateResponse = await fetch(
              `/api/marketing/campaigns/create/ai/agent-chat?sessionId=${sessionId}`
            );
            
            if (stateResponse.ok) {
              const fullState = await stateResponse.json();
              if (fullState.requirements) {
                handleCampaignGeneration(fullState.requirements);
              }
            }
          }
        } catch (err) {
          console.error('Failed to parse conversation state:', err);
        }
      }
    },
    onError: (err) => {
      setError(err.message);
    },
  });

  // Auto-scroll to bottom of messages
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Handle textarea key events (Shift+Enter for new line, Enter to submit)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as unknown as React.FormEvent<HTMLFormElement>);
    }
  };

  return (
    <div className="flex flex-col h-[700px] border rounded-lg overflow-hidden bg-white shadow-sm">
      {/* Chat messages */}
      <div className="flex-1 overflow-auto p-4" ref={messagesEndRef}>
        <div className="space-y-4 pb-4">
          {/* Welcome message */}
          {messages.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm">
                <CardContent className="p-5">
                  <div className="flex items-start gap-4">
                    <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <Sparkles className="h-4 w-4 text-white" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="font-semibold text-blue-900">Campaign Assistant</h3>
                      <p className="text-blue-800">
                        Hello! I'm your campaign creation assistant powered by Gemini 2.5 Flash. 
                        I&apos;ll help you create effective marketing campaigns by asking a few questions.
                        Let&apos;s start by discussing what type of campaign you&apos;d like to create.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={cn(
                "flex",
                message.role === "user" ? "justify-end" : "justify-start"
              )}
            >
              <div
                className={cn(
                  "max-w-[80%] rounded-lg p-4",
                  message.role === "user"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-800"
                )}
              >
                {message.content}
              </div>
            </motion.div>
          ))}

          {/* Loading indicator */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center space-x-2 text-gray-500"
            >
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Thinking...</span>
            </motion.div>
          )}

          {/* Error message */}
          {error && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center space-x-2 text-red-500 bg-red-50 p-3 rounded-lg"
            >
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Campaign generation progress */}
      <div className="px-6 py-3 bg-blue-50 border-b border-blue-100">
        {conversationState && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-800">
                Campaign Requirements
              </span>
              <Badge variant={conversationState.validation.isComplete ? "default" : "outline"} className={`text-xs ${conversationState.validation.isComplete ? "bg-green-500 hover:bg-green-600" : ""}`}>
                {conversationState.validation.completeness}% Complete
              </Badge>
            </div>
            
            <Progress 
              value={conversationState.validation.completeness} 
              className="h-2 bg-blue-100" 
            />

            <AnimatePresence>
              {isGenerating && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="text-xs text-blue-700 flex items-center gap-2"
                >
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Generating campaign...
                </motion.div>
              )}

              {generatedCampaign && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  className="text-xs text-green-700 flex items-center gap-2"
                >
                  <CheckCircle className="h-3 w-3" />
                  Campaign generated successfully!
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Input area */}
      <div className="p-4 border-t">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <Textarea
            value={input}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type your message..."
            className="flex-1 min-h-[80px] resize-none"
            disabled={isLoading || isGenerating}
          />
          <Button 
            type="submit" 
            size="icon" 
            className="self-end" 
            disabled={isLoading || isGenerating || !input.trim()}
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}
```

### 2. API Route with Gemini Integration
```typescript
// app/api/marketing/campaigns/create/ai/agent-chat/route.ts
import { google } from '@ai-sdk/google';
import { streamText, convertToModelMessages } from 'ai';
import { NextRequest } from 'next/server';

export const maxDuration = 30;

const CAMPAIGN_ASSISTANT_PROMPT = `
You are an expert marketing campaign assistant for a loyalty program platform. 
Your role is to help create compelling, personalized marketing campaigns through conversation.

Key capabilities:
- Generate email campaigns, SMS, push notifications, and social media content
- Suggest target audiences and optimal timing
- Create A/B testing variations
- Ensure brand consistency and compliance
- Provide data-driven recommendations

Always be helpful, creative, and ask clarifying questions to understand user needs.
When generating campaigns, provide structured output with clear sections for:
- Campaign type and objective
- Target audience
- Subject line/headline
- Main content
- Call-to-action
- Suggested timing and frequency
`;

export async function POST(req: NextRequest) {
  try {
    const { messages, sessionId } = await req.json();
    
    // Validate input and retrieve conversation context
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid request: messages array required', { status: 400 });
    }
    
    const result = streamText({
      model: google('gemini-2.5-flash'),
      system: CAMPAIGN_ASSISTANT_PROMPT,
      messages: convertToModelMessages(messages),
      tools: {
        generateCampaign: {
          description: 'Generate a complete marketing campaign based on user requirements',
          parameters: {
            type: 'object',
            properties: {
              type: { type: 'string', enum: ['email', 'sms', 'push', 'social'] },
              objective: { type: 'string' },
              targetAudience: { 
                type: 'object',
                properties: {
                  description: { type: 'string' }
                }
              },
              contentDetails: {
                type: 'object',
                properties: {
                  subject: { type: 'string' },
                  body: { type: 'string' },
                  cta: { type: 'string' }
                }
              }
            }
          }
        }
      },
      onFinish: async ({ response }) => {
        // Update conversation context with assistant message and state
        // Store conversation state for GET requests
      }
    });

    // Return streaming response with custom headers for conversation state
    return result.toTextStreamResponse({
      headers: {
        'X-Conversation-State': JSON.stringify({
          validation: {
            completeness: 0,
            isComplete: false
          }
        })
      }
    });
  } catch (error) {
    console.error('Campaign chat error:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// GET handler to retrieve conversation state
export async function GET(req: NextRequest) {
  try {
    const sessionId = req.nextUrl.searchParams.get('sessionId');
    
    if (!sessionId) {
      return new Response('Session ID required', { status: 400 });
    }
    
    // Return conversation state with validation information
    return new Response(JSON.stringify({
      validation: {
        completeness: 100,
        isComplete: true
      }
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error retrieving conversation state:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
```

### 3. Campaign Preview Component
```typescript
// components/CampaignPreview.tsx
interface CampaignPreviewProps {
  campaign: Campaign | null;
  onEdit: (field: string, value: string) => void;
}

export function CampaignPreview({ campaign, onEdit }: CampaignPreviewProps) {
  if (!campaign) {
    return (
      <Card className="h-[600px] flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <Mail className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Campaign preview will appear here</p>
          <p className="text-sm">Start chatting to create your campaign</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="h-[600px]">
      <CardHeader>
        <CardTitle>Campaign Preview</CardTitle>
        <Badge variant="outline">{campaign.type}</Badge>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label>Subject Line</Label>
          <Input
            value={campaign.subject}
            onChange={(e) => onEdit('subject', e.target.value)}
          />
        </div>
        
        <div>
          <Label>Campaign Content</Label>
          <Textarea
            value={campaign.body}
            onChange={(e) => onEdit('body', e.target.value)}
            rows={10}
          />
        </div>
        
        <div>
          <Label>Call to Action</Label>
          <Input
            value={campaign.cta}
            onChange={(e) => onEdit('cta', e.target.value)}
          />
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" className="flex-1">
            Save Draft
          </Button>
          <Button className="flex-1">
            Continue to Audience
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
```

## Design System Integration

### Color Palette
- **Primary**: Purple/Violet tones for AI branding
- **Secondary**: Blue for trust and reliability  
- **Accent**: Green for success states
- **Neutral**: Gray scale for text and backgrounds

### Typography
- **Headers**: Inter/System font, bold weights
- **Body**: Inter/System font, regular weights
- **Code**: JetBrains Mono for technical content

### Component Library
- Consistent with existing shadcn/ui components
- Custom AI-specific components (typing indicators, streaming text)
- Responsive design for desktop and mobile

## Data Flow & State Management

### 1. Conversation State
```typescript
interface ConversationState {
  messages: Message[];
  currentCampaign: Campaign | null;
  isGenerating: boolean;
  audienceData: AudienceSegment[];
  campaignHistory: Campaign[];
}
```

### 2. Campaign State
```typescript
interface Campaign {
  id: string;
  type: 'email' | 'sms' | 'push' | 'social';
  objective: string;
  targetAudience: AudienceSegment;
  content: {
    subject: string;
    body: string;
    cta: string;
  };
  timing: {
    sendDate: Date;
    timezone: string;
  };
  variations: CampaignVariation[];
  status: 'draft' | 'review' | 'scheduled' | 'sent';
}
```

## Performance Considerations

### 1. Streaming Optimization
- Implement proper streaming with `useChat` hook
- Handle connection interruptions gracefully
- Optimize for mobile networks

### 2. Caching Strategy
- Cache audience data for quick access
- Store campaign templates for reuse
- Implement proper error boundaries

### 3. Rate Limiting
- Implement rate limiting for API calls
- Graceful degradation for high usage
- Queue management for campaign generation

## Security & Compliance

### 1. Data Protection
- Encrypt sensitive campaign data
- Implement proper access controls
- Audit trail for all campaign actions

### 2. Marketing Compliance
- GDPR compliance checks
- CAN-SPAM compliance validation
- Unsubscribe link validation

### 3. Content Safety
- Content filtering for inappropriate material
- Brand safety checks
- Legal compliance validation

## Testing Strategy

### 1. Unit Tests
- Component rendering tests
- API endpoint tests
- Utility function tests

### 2. Integration Tests
- End-to-end campaign creation flow
- AI response handling
- Database operations

### 3. User Testing
- Usability testing with marketing teams
- A/B testing of interface variations
- Performance testing under load

## Deployment & Monitoring

### 1. Deployment Pipeline
- Automated testing and deployment
- Feature flags for gradual rollout
- Rollback procedures

### 2. Monitoring
- AI response quality metrics
- User engagement analytics
- Performance monitoring
- Error tracking and alerting

## Future Enhancements

### 1. Advanced AI Features
- Image generation for campaigns
- Video content creation
- Voice message generation
- Multi-language support

### 2. Analytics Integration
- Campaign performance prediction
- Real-time optimization suggestions
- Advanced segmentation AI

### 3. Collaboration Features
- Team collaboration on campaigns
- Approval workflows
- Version control for campaigns

## Success Metrics

### 1. User Adoption
- Daily active users of AI assistant
- Campaign creation completion rate
- User satisfaction scores

### 2. Campaign Performance
- Improvement in campaign engagement rates
- Time saved in campaign creation
- Reduction in campaign creation errors

### 3. Technical Metrics
- API response times
- Streaming performance
- Error rates and uptime

This specification provides a comprehensive foundation for building an innovative AI-powered campaign creation assistant that will revolutionize how marketing teams create and manage campaigns.
