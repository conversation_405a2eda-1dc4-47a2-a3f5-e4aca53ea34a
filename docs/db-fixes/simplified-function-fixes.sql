-- =====================================================
-- SIMPLIFIED FUNCTION SEARCH PATH FIXES
-- =====================================================
-- This script uses a dynamic approach to fix all function search_path issues
-- without worrying about exact parameter signatures
-- =====================================================

BEGIN;

-- =====================================================
-- DYNAMIC FUNCTION SEARCH PATH FIXING
-- =====================================================
-- This approach finds all functions without search_path configured
-- and applies the fix regardless of parameter signatures

DO $$
DECLARE
    func_record RECORD;
    func_signature TEXT;
    success_count INTEGER := 0;
    error_count INTEGER := 0;
BEGIN
    RAISE NOTICE 'Starting dynamic function search_path fixes...';

    -- Get all functions in public schema that don't have search_path configured
    FOR func_record IN
        SELECT
            p.oid,
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args,
            n.nspname as schema_name
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND (p.proconfig IS NULL OR NOT (array_to_string(p.proconfig, ',') LIKE '%search_path=%'))
        AND p.proname NOT LIKE 'pg_%'  -- Skip system functions
        ORDER BY p.proname, p.oid
    LOOP
        func_signature := func_record.proname || '(' || COALESCE(func_record.args, '') || ')';

        BEGIN
            EXECUTE format('ALTER FUNCTION %I.%s SET search_path = ''''',
                          func_record.schema_name, func_signature);
            success_count := success_count + 1;
            RAISE NOTICE 'SUCCESS: Fixed search_path for function: %', func_signature;

        EXCEPTION
            WHEN OTHERS THEN
                error_count := error_count + 1;
                RAISE NOTICE 'ERROR: Could not fix search_path for function: %. Error: %',
                           func_signature, SQLERRM;
        END;
    END LOOP;

    RAISE NOTICE 'Function search_path fixes completed. Success: %, Errors: %',
                success_count, error_count;
END $$;

COMMIT;

-- =====================================================
-- MANUAL FIXES FOR SPECIFIC FUNCTIONS
-- =====================================================
-- Handle functions that need complete recreation due to signature changes

BEGIN;

-- Only recreate functions that absolutely need it due to signature/logic changes
-- These are the functions we identified as having different implementations

-- 1. match_business_item - Keep existing logic and signature
DROP FUNCTION IF EXISTS public.match_business_item(uuid, text, numeric);

CREATE OR REPLACE FUNCTION public.match_business_item(
    p_company_id UUID,
    p_description TEXT,
    p_price NUMERIC DEFAULT NULL::numeric
)
RETURNS TABLE(
    business_item_id UUID,
    item_name TEXT,
    confidence_score NUMERIC,
    matching_reason TEXT
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bi.id as business_item_id,
    bi.item_name,
    CASE
      -- Exact match gets highest score
      WHEN LOWER(bi.item_name) = LOWER(p_description) THEN 1.0
      -- Check variations array if column exists
      WHEN bi.common_variations IS NOT NULL
           AND LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))) THEN 0.95
      -- Fuzzy text similarity using trigrams (if extension available)
      WHEN similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.6 THEN
           similarity(LOWER(bi.item_name), LOWER(p_description))
      -- Price match (if provided and within 10% of standard price)
      WHEN p_price IS NOT NULL AND bi.standard_price IS NOT NULL
           AND ABS(p_price - bi.standard_price) / NULLIF(bi.standard_price, 0) <= 0.1 THEN 0.7
      -- Fallback to basic text matching
      WHEN LOWER(bi.item_name) LIKE '%' || LOWER(p_description) || '%' THEN 0.6
      ELSE 0.0
    END::NUMERIC as confidence_score,
    CASE
      WHEN LOWER(bi.item_name) = LOWER(p_description) THEN 'exact_match'
      WHEN bi.common_variations IS NOT NULL
           AND LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))) THEN 'variation_match'
      WHEN similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.6 THEN 'fuzzy_match'
      WHEN p_price IS NOT NULL AND bi.standard_price IS NOT NULL
           AND ABS(p_price - bi.standard_price) / NULLIF(bi.standard_price, 0) <= 0.1 THEN 'price_match'
      WHEN LOWER(bi.item_name) LIKE '%' || LOWER(p_description) || '%' THEN 'text_match'
      ELSE 'no_match'
    END as matching_reason
  FROM public.business_items bi
  WHERE bi.company_id = p_company_id
    AND bi.is_active = true
    AND (
      LOWER(bi.item_name) = LOWER(p_description)
      OR (bi.common_variations IS NOT NULL
          AND LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))))
      OR similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.3
      OR LOWER(bi.item_name) LIKE '%' || LOWER(p_description) || '%'
      OR (p_price IS NOT NULL AND bi.standard_price IS NOT NULL
          AND ABS(p_price - bi.standard_price) / NULLIF(bi.standard_price, 0) <= 0.1)
    )
  ORDER BY confidence_score DESC
  LIMIT 5;
END;
$$;

-- 2. update_business_item_stats - Keep existing logic and signature
DROP FUNCTION IF EXISTS public.update_business_item_stats(uuid, numeric, numeric);

CREATE OR REPLACE FUNCTION public.update_business_item_stats(
    p_business_item_id UUID,
    p_sale_price NUMERIC,
    p_quantity NUMERIC DEFAULT 1
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  UPDATE public.business_items
  SET
    total_sales_count = total_sales_count + p_quantity::INTEGER,
    total_revenue = COALESCE(total_revenue, 0) + (p_sale_price * p_quantity),
    avg_selling_price = CASE
      WHEN (total_sales_count + p_quantity::INTEGER) > 0
      THEN (COALESCE(total_revenue, 0) + (p_sale_price * p_quantity)) / (total_sales_count + p_quantity::INTEGER)
      ELSE p_sale_price
    END,
    last_sold_date = NOW(),
    updated_at = NOW()
  WHERE id = p_business_item_id;
END;
$$;

-- 3. get_service_bundling_patterns - Keep existing logic and signature
DROP FUNCTION IF EXISTS public.get_service_bundling_patterns(uuid, integer);

CREATE OR REPLACE FUNCTION public.get_service_bundling_patterns(
    p_company_id UUID,
    p_min_occurrences INTEGER DEFAULT 2
)
RETURNS TABLE(
    item1_name TEXT,
    item2_name TEXT,
    bundle_frequency INTEGER,
    avg_bundle_value NUMERIC,
    bundle_score NUMERIC
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi1.item_name as item1_name,
        bi2.item_name as item2_name,
        COUNT(*)::INTEGER as bundle_frequency,
        AVG(COALESCE(ri1.total_price, 0) + COALESCE(ri2.total_price, 0)) as avg_bundle_value,
        -- Bundle score considers frequency and value
        (COUNT(*)::NUMERIC * AVG(COALESCE(ri1.total_price, 0) + COALESCE(ri2.total_price, 0)) / 100) as bundle_score
    FROM public.receipt_items ri1
    JOIN public.receipt_items ri2 ON ri1.receipt_id = ri2.receipt_id AND ri1.id != ri2.id
    JOIN public.business_items bi1 ON bi1.id = ri1.business_item_id
    JOIN public.business_items bi2 ON bi2.id = ri2.business_item_id
    JOIN public.receipts r ON r.id = ri1.receipt_id
    WHERE bi1.company_id = p_company_id
    AND bi2.company_id = p_company_id
    AND bi1.id < bi2.id -- Avoid duplicates
    GROUP BY bi1.item_name, bi2.item_name
    HAVING COUNT(*) >= p_min_occurrences
    ORDER BY bundle_score DESC;
END;
$$;

COMMIT;

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Check which functions still need search_path fixes
SELECT
  'Functions still needing search_path fix' as status,
  COUNT(*) as function_count
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND (p.proconfig IS NULL OR NOT (array_to_string(p.proconfig, ',') LIKE '%search_path=%'))
AND p.proname NOT LIKE 'pg_%';

-- Show functions that now have search_path configured
SELECT
  'Functions with search_path configured' as check_type,
  p.proname as function_name,
  pg_get_function_identity_arguments(p.oid) as parameters,
  '✓ search_path configured' as status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND p.proconfig IS NOT NULL
AND array_to_string(p.proconfig, ',') LIKE '%search_path=%'
AND p.proname IN ('match_business_item', 'update_business_item_stats', 'add_cashier', 'get_user_role')
ORDER BY p.proname, parameters;

-- Success message
SELECT 'Function search_path fixes completed successfully!' as message;
