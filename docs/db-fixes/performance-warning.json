[{"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.loyalty_members\\` has a row level security policy \\`cashier_view_members\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_loyalty_members_cashier_view_members"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.loyalty_members\\` has a row level security policy \\`cashier_insert_members\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_loyalty_members_cashier_insert_members"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.loyalty_members\\` has a row level security policy \\`cashier_update_members\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_loyalty_members_cashier_update_members"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.points_transactions\\` has a row level security policy \\`cashier_view_transactions\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "points_transactions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_points_transactions_cashier_view_transactions"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.points_transactions\\` has a row level security policy \\`cashier_insert_transactions\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "points_transactions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_points_transactions_cashier_insert_transactions"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.rewards\\` has a row level security policy \\`cashier_view_rewards\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "rewards", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_rewards_cashier_view_rewards"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.reward_redemptions\\` has a row level security policy \\`cashier_insert_redemptions\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "reward_redemptions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_reward_redemptions_cashier_insert_redemptions"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.reward_redemptions\\` has a row level security policy \\`cashier_view_redemptions\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "reward_redemptions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_reward_redemptions_cashier_view_redemptions"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.item_matching_suggestions\\` has a row level security policy \\`item_matching_suggestions_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "item_matching_suggestions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_item_matching_suggestions_item_matching_suggestions_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.business_items\\` has a row level security policy \\`business_items_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "business_items", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_business_items_business_items_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.loyalty_members\\` has a row level security policy \\`loyalty_members_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_loyalty_members_loyalty_members_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.points_transactions\\` has a row level security policy \\`points_transactions_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "points_transactions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_points_transactions_points_transactions_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.rewards\\` has a row level security policy \\`rewards_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "rewards", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_rewards_rewards_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.companies\\` has a row level security policy \\`companies_direct_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "companies", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_companies_companies_direct_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.dashboard_metrics_history\\` has a row level security policy \\`dashboard_metrics_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "dashboard_metrics_history", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_dashboard_metrics_history_dashboard_metrics_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.member_notifications\\` has a row level security policy \\`member_notifications_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "member_notifications", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_member_notifications_member_notifications_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.program_rules\\` has a row level security policy \\`program_rules_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "program_rules", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_program_rules_program_rules_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.receipt_items\\` has a row level security policy \\`receipt_items_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "receipt_items", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_receipt_items_receipt_items_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.receipt_templates\\` has a row level security policy \\`receipt_templates_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "receipt_templates", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_receipt_templates_receipt_templates_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.receipts\\` has a row level security policy \\`receipts_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "receipts", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_receipts_receipts_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.reward_redemptions\\` has a row level security policy \\`reward_redemptions_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "reward_redemptions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_reward_redemptions_reward_redemptions_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.reward_tier_eligibility\\` has a row level security policy \\`reward_tier_eligibility_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "reward_tier_eligibility", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_reward_tier_eligibility_reward_tier_eligibility_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.tier_definitions\\` has a row level security policy \\`tier_definitions_admin_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "tier_definitions", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_tier_definitions_tier_definitions_admin_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.marketing_campaigns\\` has a row level security policy \\`marketing_campaigns_company_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "marketing_campaigns", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_marketing_campaigns_marketing_campaigns_company_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.campaign_recipients\\` has a row level security policy \\`campaign_recipients_company_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "campaign_recipients", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_campaign_recipients_campaign_recipients_company_access"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.dashboard_configurations\\` has a row level security policy \\`Users can view dashboard configurations for their company\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_dashboard_configurations_Users can view dashboard configurations for their company"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.dashboard_configurations\\` has a row level security policy \\`Users can update dashboard configurations for their company\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_dashboard_configurations_Users can update dashboard configurations for their company"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.cashier_invitations\\` has a row level security policy \\`Company admins can manage invitations for their company\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "cashier_invitations", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_cashier_invitations_Company admins can manage invitations for their company"}, {"name": "auth_rls_initplan", "title": "Auth RLS Initialization Plan", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if calls to \\`current_setting()\\` and \\`auth.<function>()\\` in RLS policies are being unnecessarily re-evaluated for each row", "detail": "Table \\`public.company_administrators\\` has a row level security policy \\`company_administrators_access\\` that re-evaluates current_setting() or auth.<function>() for each row. This produces suboptimal query performance at scale. Resolve the issue by replacing \\`auth.<function>()\\` with \\`(select auth.<function>())\\`. See [docs](https://supabase.com/docs/guides/database/postgres/row-level-security#call-functions-with-select) for more info.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan", "metadata": {"name": "company_administrators", "type": "table", "schema": "public"}, "cache_key": "auth_rls_init_plan_public_company_administrators_company_administrators_access"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.companies\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{companies_direct_access,companies_insert_policy}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "companies", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_companies_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`anon\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_anon_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`authenticator\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_authenticator_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`company_admin\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_company_admin_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`dashboard_user\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_dashboard_user_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`loyalty_admin\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_loyalty_admin_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.dashboard_configurations\\` has multiple permissive policies for role \\`readonly_user\\` for action \\`SELECT\\`. Policies include \\`{\"Users can update dashboard configurations for their company\",\"Users can view dashboard configurations for their company\"}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "dashboard_configurations", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_dashboard_configurations_readonly_user_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.loyalty_members\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{cashier_insert_members,loyalty_members_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_loyalty_members_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.loyalty_members\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{cashier_view_members,loyalty_members_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_loyalty_members_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.loyalty_members\\` has multiple permissive policies for role \\`authenticated\\` for action \\`UPDATE\\`. Policies include \\`{cashier_update_members,loyalty_members_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_loyalty_members_authenticated_UPDATE"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.points_transactions\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{cashier_insert_transactions,points_transactions_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "points_transactions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_points_transactions_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.points_transactions\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{cashier_view_transactions,points_transactions_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "points_transactions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_points_transactions_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.reward_redemptions\\` has multiple permissive policies for role \\`authenticated\\` for action \\`INSERT\\`. Policies include \\`{cashier_insert_redemptions,reward_redemptions_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "reward_redemptions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_reward_redemptions_authenticated_INSERT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.reward_redemptions\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{cashier_view_redemptions,reward_redemptions_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "reward_redemptions", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_reward_redemptions_authenticated_SELECT"}, {"name": "multiple_permissive_policies", "title": "Multiple Permissive Policies", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects if multiple permissive row level security policies are present on a table for the same \\`role\\` and \\`action\\` (e.g. insert). Multiple permissive policies are suboptimal for performance as each policy must be executed for every relevant query.", "detail": "Table \\`public.rewards\\` has multiple permissive policies for role \\`authenticated\\` for action \\`SELECT\\`. Policies include \\`{cashier_view_rewards,rewards_admin_access}\\`", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0006_multiple_permissive_policies", "metadata": {"name": "rewards", "type": "table", "schema": "public"}, "cache_key": "multiple_permissive_policies_public_rewards_authenticated_SELECT"}, {"name": "duplicate_index", "title": "Duplicate Index", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects cases where two ore more identical indexes exist.", "detail": "Table \\`public.loyalty_members\\` has identical indexes {idx_loyalty_members_company_id,idx_member_points_company_id}. Drop all except one of them", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0009_duplicate_index", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public", "indexes": ["idx_loyalty_members_company_id", "idx_member_points_company_id"]}, "cache_key": "duplicate_index_public_loyalty_members_{idx_loyalty_members_company_id,idx_member_points_company_id}"}, {"name": "duplicate_index", "title": "Duplicate Index", "level": "WARN", "facing": "EXTERNAL", "categories": ["PERFORMANCE"], "description": "Detects cases where two ore more identical indexes exist.", "detail": "Table \\`public.loyalty_members\\` has identical indexes {idx_loyalty_members_company_date_range,idx_loyalty_members_company_reg}. Drop all except one of them", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0009_duplicate_index", "metadata": {"name": "loyalty_members", "type": "table", "schema": "public", "indexes": ["idx_loyalty_members_company_date_range", "idx_loyalty_members_company_reg"]}, "cache_key": "duplicate_index_public_loyalty_members_{idx_loyalty_members_company_date_range,idx_loyalty_members_company_reg}"}]