-- =====================================================
-- FUNCTION SEARCH PATH FIXES
-- =====================================================
-- This script fixes all function search_path mutable warnings
-- Run this after the main database fixes
-- =====================================================

BEGIN;

-- =====================================================
-- FIX FUNCTIONS WITH SEARCH_PATH MUTABLE WARNINGS
-- =====================================================
-- Note: We DROP functions first to avoid parameter name/signature conflicts,
-- then recreate them with the exact same signature and logic but with SET search_path = ''

-- Drop and recreate the function with correct signature and existing logic
DROP FUNCTION IF EXISTS public.match_business_item(uuid, text, numeric);

CREATE OR REPLACE FUNCTION public.match_business_item(
    p_company_id UUID,
    p_description TEXT,
    p_price NUMERIC DEFAULT NULL::numeric
)
RETURNS TABLE(
    business_item_id UUID,
    item_name TEXT,
    confidence_score NUMERIC,
    matching_reason TEXT
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bi.id as business_item_id,
    bi.item_name,
    CASE
      -- Exact match gets highest score
      WHEN LOWER(bi.item_name) = LOWER(p_description) THEN 1.0
      -- Check variations array
      WHEN LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))) THEN 0.95
      -- Fuzzy text similarity using trigrams
      WHEN similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.6 THEN similarity(LOWER(bi.item_name), LOWER(p_description))
      -- Price match (if provided and within 10% of standard price)
      WHEN p_price IS NOT NULL AND bi.standard_price IS NOT NULL
           AND ABS(p_price - bi.standard_price) / bi.standard_price <= 0.1 THEN 0.7
      ELSE 0.0
    END::DECIMAL(3,2) as confidence_score,
    CASE
      WHEN LOWER(bi.item_name) = LOWER(p_description) THEN 'exact_match'
      WHEN LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations))) THEN 'variation_match'
      WHEN similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.6 THEN 'fuzzy_match'
      WHEN p_price IS NOT NULL AND bi.standard_price IS NOT NULL
           AND ABS(p_price - bi.standard_price) / bi.standard_price <= 0.1 THEN 'price_match'
      ELSE 'no_match'
    END as matching_reason
  FROM public.business_items bi
  WHERE bi.company_id = p_company_id
    AND bi.is_active = true
    AND (
      LOWER(bi.item_name) = LOWER(p_description)
      OR LOWER(p_description) = ANY(SELECT LOWER(unnest(bi.common_variations)))
      OR similarity(LOWER(bi.item_name), LOWER(p_description)) > 0.3
      OR (p_price IS NOT NULL AND bi.standard_price IS NOT NULL
          AND ABS(p_price - bi.standard_price) / bi.standard_price <= 0.1)
    )
  ORDER BY confidence_score DESC
  LIMIT 5;
END;
$$;

-- Drop and recreate the function with correct signature
DROP FUNCTION IF EXISTS public.update_business_item_stats(uuid, numeric, numeric);

CREATE OR REPLACE FUNCTION public.update_business_item_stats(
    p_business_item_id UUID,
    p_sale_price NUMERIC,
    p_quantity NUMERIC DEFAULT 1
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  UPDATE public.business_items
  SET
    total_sales_count = total_sales_count + p_quantity::INTEGER,
    total_revenue = total_revenue + (p_sale_price * p_quantity),
    avg_selling_price = (total_revenue + (p_sale_price * p_quantity)) / (total_sales_count + p_quantity::INTEGER),
    last_sold_date = NOW(),
    updated_at = NOW()
  WHERE id = p_business_item_id;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_customer_seasonal_preferences(
    p_member_id UUID,
    p_months_back INTEGER DEFAULT 12
)
RETURNS TABLE(
    item_name TEXT,
    item_category TEXT,
    spring_purchases INTEGER,
    summer_purchases INTEGER,
    fall_purchases INTEGER,
    winter_purchases INTEGER,
    total_purchases INTEGER,
    seasonal_preference TEXT
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi.item_name,
        bi.item_category,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END)::INTEGER as spring_purchases,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END)::INTEGER as summer_purchases,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END)::INTEGER as fall_purchases,
        COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)::INTEGER as winter_purchases,
        COUNT(ri.id)::INTEGER as total_purchases,
        CASE
            WHEN COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END) = GREATEST(
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)
            ) THEN 'Spring'
            WHEN COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END) = GREATEST(
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)
            ) THEN 'Summer'
            WHEN COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END) = GREATEST(
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (3,4,5) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (6,7,8) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (9,10,11) THEN 1 END),
                COUNT(CASE WHEN EXTRACT(MONTH FROM r.purchase_date) IN (12,1,2) THEN 1 END)
            ) THEN 'Fall'
            ELSE 'Winter'
        END as seasonal_preference
    FROM public.receipts r
    JOIN public.receipt_items ri ON ri.receipt_id = r.id
    JOIN public.business_items bi ON bi.id = ri.business_item_id
    WHERE r.member_id = p_member_id
    AND r.purchase_date >= (NOW() - (p_months_back || ' months')::INTERVAL)
    GROUP BY bi.item_name, bi.item_category
    ORDER BY COUNT(ri.id) DESC;
END;
$$;

-- Drop and recreate the function with correct signature and logic
DROP FUNCTION IF EXISTS public.get_service_bundling_patterns(uuid, integer);

CREATE OR REPLACE FUNCTION public.get_service_bundling_patterns(
    p_company_id UUID,
    p_min_occurrences INTEGER DEFAULT 2
)
RETURNS TABLE(
    item1_name TEXT,
    item2_name TEXT,
    bundle_frequency INTEGER,
    avg_bundle_value NUMERIC,
    bundle_score NUMERIC
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi1.item_name as item1_name,
        bi2.item_name as item2_name,
        COUNT(*) as bundle_frequency,
        AVG(ri1.total_price + ri2.total_price) as avg_bundle_value,
        -- Bundle score considers frequency and value
        (COUNT(*)::NUMERIC * AVG(ri1.total_price + ri2.total_price) / 100) as bundle_score
    FROM public.receipt_items ri1
    JOIN public.receipt_items ri2 ON ri1.receipt_id = ri2.receipt_id AND ri1.id != ri2.id
    JOIN public.business_items bi1 ON bi1.id = ri1.business_item_id
    JOIN public.business_items bi2 ON bi2.id = ri2.business_item_id
    JOIN public.receipts r ON r.id = ri1.receipt_id
    WHERE bi1.company_id = p_company_id
    AND bi2.company_id = p_company_id
    AND bi1.id < bi2.id -- Avoid duplicates
    GROUP BY bi1.item_name, bi2.item_name
    HAVING COUNT(*) >= p_min_occurrences
    ORDER BY bundle_score DESC;
END;
$$;

CREATE OR REPLACE FUNCTION public.get_tiers_with_member_counts(company_id_param UUID)
RETURNS TABLE(
    id UUID,
    tier_name TEXT,
    minimum_points INTEGER,
    color_hex TEXT,
    benefits_description TEXT,
    member_count BIGINT,
    percentage_of_members NUMERIC,
    member_stats JSONB
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    tr.id,
    tr.tier_name,
    tr.minimum_points,
    tr.color_hex,
    tr.benefits_description,
    COUNT(lm.id) as member_count,
    CASE
      WHEN (SELECT COUNT(*) FROM public.loyalty_members WHERE company_id = company_id_param) > 0
      THEN ROUND((COUNT(lm.id)::NUMERIC / (SELECT COUNT(*) FROM public.loyalty_members WHERE company_id = company_id_param)::NUMERIC) * 100, 2)
      ELSE 0
    END as percentage_of_members,
    jsonb_build_object(
      'total_members', COUNT(lm.id),
      'avg_lifetime_points', COALESCE(AVG(lm.lifetime_points), 0),
      'total_lifetime_points', COALESCE(SUM(lm.lifetime_points), 0),
      'newest_member_date', MAX(lm.registration_date),
      'oldest_member_date', MIN(lm.registration_date)
    ) as member_stats
  FROM public.loyalty_tiers tr
  LEFT JOIN public.loyalty_members lm ON lm.loyalty_tier = tr.tier_name AND lm.company_id = company_id_param
  WHERE tr.company_id = company_id_param
  GROUP BY tr.id, tr.tier_name, tr.minimum_points, tr.color_hex, tr.benefits_description
  ORDER BY tr.minimum_points;
END;
$$;

COMMIT;

-- =====================================================
-- FIX EXISTING FUNCTIONS WITH ALTER FUNCTION
-- =====================================================
-- Apply SET search_path = '' to existing functions

BEGIN;

-- Single and multiple parameter functions - use correct signatures
ALTER FUNCTION public.add_cashier(UUID, TEXT, TEXT, TEXT) SET search_path = '';
ALTER FUNCTION public.is_cashier(UUID) SET search_path = '';
ALTER FUNCTION public.get_user_role(UUID) SET search_path = '';
ALTER FUNCTION public.get_birthday_eligible_members(UUID) SET search_path = '';
ALTER FUNCTION public.add_default_dashboard_config() SET search_path = '';
ALTER FUNCTION public.cleanup_expired_invitations() SET search_path = '';
ALTER FUNCTION public.generate_invitation_token() SET search_path = '';
ALTER FUNCTION public.update_updated_at_column() SET search_path = '';
ALTER FUNCTION public.get_campaign_eligible_members(UUID, CHARACTER VARYING, JSONB) SET search_path = '';
ALTER FUNCTION public.send_telegram_notification_for_transaction() SET search_path = '';
ALTER FUNCTION public.calculate_member_tier(UUID) SET search_path = '';
ALTER FUNCTION public.get_company_config(UUID) SET search_path = '';
ALTER FUNCTION public.get_member_growth(UUID) SET search_path = '';
ALTER FUNCTION public.get_onboarding_status_optimized(UUID) SET search_path = '';
ALTER FUNCTION public.get_detailed_customer_preferences(UUID) SET search_path = '';
ALTER FUNCTION public.refresh_dashboard_metrics() SET search_path = '';
ALTER FUNCTION public.update_member_point_balances() SET search_path = '';
ALTER FUNCTION public.get_customer_insights(UUID) SET search_path = '';
ALTER FUNCTION public.get_business_metrics_optimized(UUID) SET search_path = '';
ALTER FUNCTION public.is_member_birthday_eligible(UUID) SET search_path = '';
ALTER FUNCTION public.get_business_performance(UUID) SET search_path = '';
ALTER FUNCTION public.get_template_analytics(UUID) SET search_path = '';
ALTER FUNCTION public.get_recent_activity(UUID, INTEGER) SET search_path = '';
ALTER FUNCTION public.analyze_template_errors(UUID) SET search_path = '';

-- Handle get_top_members function overloads specifically
ALTER FUNCTION public.get_top_members(INTEGER) SET search_path = '';
ALTER FUNCTION public.get_top_members(INTEGER, TEXT[]) SET search_path = '';
ALTER FUNCTION public.get_top_members(UUID, TEXT, INTEGER) SET search_path = '';
ALTER FUNCTION public.get_top_members_live(UUID, TEXT, INTEGER) SET search_path = '';
ALTER FUNCTION public.get_top_members_optimized(UUID, TEXT, INTEGER) SET search_path = '';-- Handle multiple overloaded functions
DO $$
DECLARE
    func_record RECORD;
    func_signature TEXT;
BEGIN
    -- Fix all create_points_transaction function overloads
    FOR func_record IN
        SELECT
            p.oid,
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname = 'create_points_transaction'
    LOOP
        func_signature := func_record.proname || '(' || func_record.args || ')';
        BEGIN
            EXECUTE format('ALTER FUNCTION public.%s SET search_path = ''''', func_signature);
            RAISE NOTICE 'Fixed search_path for function: %', func_signature;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not fix search_path for function: %, Error: %', func_signature, SQLERRM;
        END;
    END LOOP;

    -- Handle other potentially overloaded functions
    FOR func_record IN
        SELECT
            p.oid,
            p.proname,
            pg_get_function_identity_arguments(p.oid) as args
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public'
        AND p.proname IN ('get_customer_seasonal_preferences', 'get_service_bundling_patterns')
        AND p.proconfig IS NULL
    LOOP
        func_signature := func_record.proname || '(' || func_record.args || ')';
        BEGIN
            EXECUTE format('ALTER FUNCTION public.%s SET search_path = ''''', func_signature);
            RAISE NOTICE 'Fixed search_path for function: %', func_signature;
        EXCEPTION
            WHEN OTHERS THEN
                RAISE NOTICE 'Could not fix search_path for function: %, Error: %', func_signature, SQLERRM;
        END;
    END LOOP;
END $$;

COMMIT;

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Check function search_path configuration
SELECT
  'Function Search Path Check' as check_type,
  proname as function_name,
  pg_get_function_identity_arguments(oid) as parameters,
  CASE
    WHEN proconfig IS NOT NULL AND array_to_string(proconfig, ',') LIKE '%search_path=%' THEN '✓ search_path configured'
    ELSE '✗ search_path not configured'
  END as status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND proname IN ('match_business_item', 'add_cashier', 'get_user_role', 'create_points_transaction', 'update_business_item_stats')
ORDER BY proname, parameters;

-- Success message
SELECT 'Function search_path fixes applied successfully!' as message;
