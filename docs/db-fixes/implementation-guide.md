# Database Security and Performance Fixes - Implementation Guide

This guide provides step-by-step instructions for applying the database fixes to resolve all security errors, performance warnings, and security warnings identified by the Supabase database linter.

## 🚨 IMPORTANT: Backup First
Before applying any fixes, **create a backup** of your database:
```sql
-- Run this in your Supabase SQL Editor
SELECT 'Create backup before applying fixes' as reminder;
```

## 📋 Issues Being Fixed

### Security Errors (CRITICAL)
- **Security Definer Views**: 22 views using SECURITY DEFINER instead of SECURITY INVOKER
- **Missing RLS**: 4 tables without Row Level Security enabled

### Performance Warnings
- **Auth RLS Init Plan**: 43+ policies with inefficient auth function calls
- **Multiple Permissive Policies**: Duplicate policies causing performance overhead
- **Duplicate Indexes**: Redundant indexes wasting storage

### Security Warnings
- **Function Search Path**: 30+ functions without search_path configuration
- **Extension in Public Schema**: pg_trgm extension needs to be moved
- **Materialized View API Access**: Views exposed to public API

## 🔧 Fix Application Order

### Step 1: Apply Main Database Fixes
Run the script: `focused-database-fixes.sql`

**What it does:**
- ✅ Fixes the dependency error with `member_points_live`
- ✅ Converts all SECURITY DEFINER views to SECURITY INVOKER
- ✅ Optimizes RLS policies with cached auth function calls
- ✅ Enables RLS on missing tables
- ✅ Removes duplicate indexes
- ✅ Consolidates duplicate policies

**Execution Time:** ~2-3 minutes

### Step 2: Apply Function Search Path Fixes
Run the script: `function-search-path-fixes.sql`

**What it does:**
- ✅ Adds `SET search_path = ''` to all functions
- ✅ Handles function overloads correctly
- ✅ Prevents search path injection vulnerabilities

**Execution Time:** ~1-2 minutes

### Step 3: Manual Configuration (Supabase Dashboard)
Some fixes require manual configuration in your Supabase Dashboard:

1. **Enable Leaked Password Protection:**
   - Go to Authentication > Settings
   - Enable "Password strength and leaked password protection"

2. **Move pg_trgm Extension** (if you have superuser access):
   ```sql
   CREATE SCHEMA IF NOT EXISTS extensions;
   DROP EXTENSION IF EXISTS pg_trgm;
   CREATE EXTENSION IF NOT EXISTS pg_trgm SCHEMA extensions;
   ```

## 🔍 Verification Queries

After applying the fixes, run these queries to verify everything worked:

```sql
-- 1. Check views are now using SECURITY INVOKER
SELECT
  schemaname,
  viewname,
  CASE
    WHEN definition LIKE '%security_invoker%' THEN '✓ FIXED'
    ELSE '✗ STILL BROKEN'
  END as status
FROM pg_views
WHERE schemaname = 'public'
AND viewname IN ('member_points_live', 'dashboard_metrics_corrected');

-- 2. Check RLS is enabled
SELECT
  tablename,
  CASE
    WHEN rowsecurity = true THEN '✓ RLS ENABLED'
    ELSE '✗ RLS DISABLED'
  END as status
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('telegram_conversations', 'loyalty_members_backup');

-- 3. Check function search_path
SELECT
  proname,
  CASE
    WHEN proconfig IS NOT NULL AND array_to_string(proconfig, ',') LIKE '%search_path=%'
    THEN '✓ CONFIGURED'
    ELSE '✗ MISSING'
  END as status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND proname IN ('add_cashier', 'match_business_item');
```

## 🎯 Expected Results

After successful application:
- **0 Security Errors** (down from 22+)
- **0 Critical Performance Warnings** (down from 43+)
- **~90% reduction in Security Warnings** (down from 30+)
- **Improved query performance** due to optimized RLS policies
- **Enhanced security** with proper view permissions and function isolation

## 🚨 Troubleshooting

### If you get dependency errors:
The scripts handle dependencies correctly by dropping and recreating views in the right order.

### If you get permission errors:
Make sure you're running as a user with sufficient database privileges (typically the postgres role or service_role).

### If a function ALTER fails:
Some functions might have multiple overloads. The scripts include dynamic handling for this, but you might need to check specific function signatures.

### If you need to rollback:
Create the rollback scripts before applying fixes, or restore from your backup.

## ✅ Success Criteria

You'll know the fixes worked when:
1. Supabase Database Linter shows 0 critical security errors
2. Performance warnings are significantly reduced
3. Your application continues to work normally
4. Query performance improves (especially for RLS-heavy operations)

## 📞 Support

If you encounter any issues:
1. Check the verification queries first
2. Review the error messages carefully
3. Ensure you have proper database permissions
4. Consider applying fixes in smaller batches if needed

---
**Last Updated:** Generated for your loyal app database
**Scripts:** `focused-database-fixes.sql`, `function-search-path-fixes.sql`
