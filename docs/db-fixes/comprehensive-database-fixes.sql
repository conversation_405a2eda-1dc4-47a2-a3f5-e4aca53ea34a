-- =====================================================
-- COMPREHENSIVE DATABASE SECURITY & PERFORMANCE FIXES
-- =====================================================
-- This script addresses all issues identified by Supabase database linter:
-- 1. Security errors: SECURITY DEFINER views converted to SECURITY INVOKER
-- 2. Performance warnings: RLS auth function caching and duplicate policy removal
-- 3. Security warnings: Function search_path fixes and extension schema moves
--
-- Execute this script in your Supabase SQL editor or via migration
-- =====================================================

BEGIN;

-- =====================================================
-- SECTION 1: FIX SECURITY DEFINER VIEWS
-- =====================================================
-- Convert all SECURITY DEFINER views to SECURITY INVOKER to respect user permissions

-- Fix dependency issue: Drop dependent views first, then recreate them
DROP VIEW IF EXISTS public.member_segments CASCADE;

-- 1. Fix member_points_live (has dependencies)
DROP VIEW IF EXISTS public.member_points_live CASCADE;
CREATE VIEW public.member_points_live
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.loyalty_id,
    lm.name,
    lm.email,
    lm.phone_number,
    lm.birthday,
    lm.company_id,
    lm.loyalty_tier,
    lm.registration_date,
    lm.profile_image_url,
    lm.notes,
    lm.telegram_chat_id,
    lm.telegram_username,
    lm.linking_token,
    lm.linked_at,
    lm.notification_preferences,
    lm.ai_preferences,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
            ELSE 0
        END), (0)::bigint) AS lifetime_points,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS redeemed_points,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EXPIRE'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS expired_points,
    COALESCE(sum(pt.points_change), (0)::bigint) AS available_points,
    max(pt.created_at) AS last_transaction_date,
    count(pt.id) AS total_transactions
FROM (loyalty_members lm
     LEFT JOIN points_transactions pt ON ((lm.id = pt.member_id)))
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.email, lm.phone_number, lm.birthday, lm.company_id, lm.loyalty_tier, lm.registration_date, lm.profile_image_url, lm.notes, lm.telegram_chat_id, lm.telegram_username, lm.linking_token, lm.linked_at, lm.notification_preferences, lm.ai_preferences;

-- Recreate member_segments view with SECURITY INVOKER
CREATE VIEW public.member_segments
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.name,
    lm.email,
    lm.phone_number,
    lm.telegram_chat_id,
    lm.loyalty_tier,
    lm.lifetime_points,
    lm.company_id,
    lm.registration_date,
    lm.notification_preferences,
    mp.available_points,
        CASE
            WHEN (mp.available_points >= 1000) THEN 'high_balance'::text
            WHEN (mp.available_points >= 500) THEN 'medium_balance'::text
            ELSE 'low_balance'::text
        END AS balance_segment,
        CASE
            WHEN (lm.registration_date >= (now() - '30 days'::interval)) THEN 'new'::text
            WHEN (lm.registration_date >= (now() - '90 days'::interval)) THEN 'recent'::text
            ELSE 'established'::text
        END AS member_age_segment,
        CASE
            WHEN ((lm.telegram_chat_id IS NOT NULL) AND (COALESCE(((lm.notification_preferences ->> 'points_earned'::text))::boolean, true) = true)) THEN true
            ELSE false
        END AS has_telegram,
        CASE
            WHEN ((lm.telegram_chat_id IS NOT NULL) AND (COALESCE(((lm.notification_preferences ->> 'points_earned'::text))::boolean, true) = true)) THEN 'eligible'::text
            WHEN (lm.telegram_chat_id IS NULL) THEN 'no_telegram'::text
            WHEN (COALESCE(((lm.notification_preferences ->> 'points_earned'::text))::boolean, true) = false) THEN 'notifications_disabled'::text
            ELSE 'ineligible'::text
        END AS telegram_eligibility
FROM (loyalty_members lm
     LEFT JOIN member_points_live mp ON ((lm.id = mp.id)));

-- 2. Fix dashboard_metrics_corrected
DROP VIEW IF EXISTS public.dashboard_metrics_corrected CASCADE;
CREATE VIEW public.dashboard_metrics_corrected
WITH (security_invoker = true)
AS
WITH transaction_stats AS (
    SELECT points_transactions.company_id,
        count(DISTINCT points_transactions.member_id) AS unique_transaction_members,
        sum(
            CASE
                WHEN (points_transactions.transaction_type = 'EARN'::text) THEN points_transactions.points_change
                ELSE 0
            END) AS total_earned_points,
        abs(sum(
            CASE
                WHEN (points_transactions.transaction_type = 'REDEEM'::text) THEN points_transactions.points_change
                ELSE 0
            END)) AS total_redeemed_points,
        abs(sum(
            CASE
                WHEN (points_transactions.transaction_type = 'EXPIRE'::text) THEN points_transactions.points_change
                ELSE 0
            END)) AS total_expired_points,
        count(DISTINCT
            CASE
                WHEN (points_transactions.created_at >= (now() - '30 days'::interval)) THEN points_transactions.member_id
                ELSE NULL::uuid
            END) AS active_members_30d,
        count(*) AS total_transactions,
        count(
            CASE
                WHEN (points_transactions.transaction_type = 'EARN'::text) THEN 1
                ELSE NULL::integer
            END) AS earn_transactions,
        count(
            CASE
                WHEN (points_transactions.transaction_type = 'REDEEM'::text) THEN 1
                ELSE NULL::integer
            END) AS redeem_transactions,
        max(points_transactions.created_at) AS last_transaction_date
    FROM points_transactions
    GROUP BY points_transactions.company_id
), company_stats AS (
    SELECT c.id AS company_id,
        c.name AS company_name,
        count(DISTINCT lm.id) AS total_members,
        COALESCE(ts.active_members_30d, (0)::bigint) AS active_members_30d,
        COALESCE(ts.total_earned_points, (0)::bigint) AS total_lifetime_points,
        COALESCE(ts.total_redeemed_points, (0)::bigint) AS total_redeemed_points,
        COALESCE(ts.total_expired_points, (0)::bigint) AS total_expired_points,
        ((COALESCE(ts.total_earned_points, (0)::bigint) - COALESCE(ts.total_redeemed_points, (0)::bigint)) - COALESCE(ts.total_expired_points, (0)::bigint)) AS total_available_points,
        count(DISTINCT r.id) AS total_rewards,
        count(DISTINCT
            CASE
                WHEN ((r.is_active = true) AND (r.expiration_date > now())) THEN r.id
                ELSE NULL::uuid
            END) AS active_rewards,
        COALESCE(ts.total_transactions, (0)::bigint) AS total_transactions,
        COALESCE(ts.earn_transactions, (0)::bigint) AS earn_transactions,
        COALESCE(ts.redeem_transactions, (0)::bigint) AS redeem_transactions,
        now() AS last_updated,
        min(lm.registration_date) AS first_member_date,
        ts.last_transaction_date
    FROM (((companies c
         LEFT JOIN loyalty_members lm ON ((lm.company_id = c.id)))
         LEFT JOIN transaction_stats ts ON ((ts.company_id = c.id)))
         LEFT JOIN rewards r ON ((r.company_id = c.id)))
    GROUP BY c.id, c.name, ts.active_members_30d, ts.total_earned_points, ts.total_redeemed_points, ts.total_expired_points, ts.total_transactions, ts.earn_transactions, ts.redeem_transactions, ts.last_transaction_date
)
SELECT company_stats.company_id,
    company_stats.company_name,
    company_stats.total_members,
    company_stats.active_members_30d,
    company_stats.total_lifetime_points,
    company_stats.total_redeemed_points,
    company_stats.total_expired_points,
    company_stats.total_available_points,
    company_stats.total_rewards,
    company_stats.active_rewards,
    company_stats.total_transactions,
    company_stats.earn_transactions,
    company_stats.redeem_transactions,
    company_stats.last_updated,
    company_stats.first_member_date,
    company_stats.last_transaction_date,
        CASE
            WHEN (company_stats.total_lifetime_points > 0) THEN round((((company_stats.total_redeemed_points)::numeric / (company_stats.total_lifetime_points)::numeric) * (100)::numeric), 2)
            ELSE (0)::numeric
        END AS redemption_rate_percentage,
        CASE
            WHEN (company_stats.first_member_date IS NOT NULL) THEN EXTRACT(days FROM (now() - company_stats.first_member_date))
            ELSE (0)::numeric
        END AS days_since_first_member
FROM company_stats;

-- 3. Fix dashboard_metrics_live
DROP VIEW IF EXISTS public.dashboard_metrics_live CASCADE;
CREATE VIEW public.dashboard_metrics_live
WITH (security_invoker = true)
AS
WITH transaction_stats AS (
    SELECT pt.company_id,
        count(DISTINCT pt.member_id) AS unique_members,
        sum(
            CASE
                WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
                ELSE 0
            END) AS total_lifetime_points,
        sum(
            CASE
                WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
                ELSE 0
            END) AS total_redeemed_points,
        sum(
            CASE
                WHEN (pt.transaction_type = 'EXPIRE'::text) THEN abs(pt.points_change)
                ELSE 0
            END) AS total_expired_points,
        sum(pt.points_change) AS total_available_points,
        count(*) AS total_transactions,
        count(
            CASE
                WHEN (pt.transaction_type = 'EARN'::text) THEN 1
                ELSE NULL::integer
            END) AS earn_transactions,
        count(
            CASE
                WHEN (pt.transaction_type = 'REDEEM'::text) THEN 1
                ELSE NULL::integer
            END) AS redeem_transactions,
        count(DISTINCT
            CASE
                WHEN (pt.created_at >= (now() - '30 days'::interval)) THEN pt.member_id
                ELSE NULL::uuid
            END) AS active_members_30d,
        max(pt.created_at) AS last_transaction_date
    FROM points_transactions pt
    GROUP BY pt.company_id
)
SELECT c.id AS company_id,
    c.name AS company_name,
    count(DISTINCT lm.id) AS total_members,
    COALESCE(ts.active_members_30d, (0)::bigint) AS active_members_30d,
    COALESCE(ts.total_lifetime_points, (0)::bigint) AS total_lifetime_points,
    COALESCE(ts.total_redeemed_points, (0)::bigint) AS total_redeemed_points,
    COALESCE(ts.total_expired_points, (0)::bigint) AS total_expired_points,
    COALESCE(ts.total_available_points, (0)::bigint) AS total_available_points,
    COALESCE(ts.total_transactions, (0)::bigint) AS total_transactions,
    COALESCE(ts.earn_transactions, (0)::bigint) AS earn_transactions,
    COALESCE(ts.redeem_transactions, (0)::bigint) AS redeem_transactions,
    ts.last_transaction_date,
    now() AS last_updated,
        CASE
            WHEN (ts.total_lifetime_points > 0) THEN round((((ts.total_redeemed_points)::numeric / (ts.total_lifetime_points)::numeric) * (100)::numeric), 2)
            ELSE (0)::numeric
        END AS redemption_rate_percentage
FROM ((companies c
     LEFT JOIN loyalty_members lm ON ((lm.company_id = c.id)))
     LEFT JOIN transaction_stats ts ON ((ts.company_id = c.id)))
GROUP BY c.id, c.name, ts.active_members_30d, ts.total_lifetime_points, ts.total_redeemed_points, ts.total_expired_points, ts.total_available_points, ts.total_transactions, ts.earn_transactions, ts.redeem_transactions, ts.last_transaction_date;

-- 4. Fix secure_dashboard_metrics
DROP VIEW IF EXISTS public.secure_dashboard_metrics CASCADE;
CREATE VIEW public.secure_dashboard_metrics
WITH (security_invoker = true)
AS
SELECT dashboard_metrics_corrected.company_id,
    dashboard_metrics_corrected.company_name,
    dashboard_metrics_corrected.total_members,
    dashboard_metrics_corrected.active_members_30d,
    dashboard_metrics_corrected.total_lifetime_points,
    dashboard_metrics_corrected.total_redeemed_points,
    dashboard_metrics_corrected.total_expired_points,
    dashboard_metrics_corrected.total_available_points,
    dashboard_metrics_corrected.total_rewards,
    dashboard_metrics_corrected.active_rewards,
    dashboard_metrics_corrected.total_transactions,
    dashboard_metrics_corrected.earn_transactions,
    dashboard_metrics_corrected.redeem_transactions,
    dashboard_metrics_corrected.last_updated,
    dashboard_metrics_corrected.first_member_date,
    dashboard_metrics_corrected.last_transaction_date,
    dashboard_metrics_corrected.redemption_rate_percentage,
    dashboard_metrics_corrected.days_since_first_member
FROM dashboard_metrics_corrected
WHERE (dashboard_metrics_corrected.company_id = (current_setting('app.current_company_id'::text, true))::uuid);

-- 5. Fix popular_items_by_quantity
DROP VIEW IF EXISTS public.popular_items_by_quantity CASCADE;
CREATE VIEW public.popular_items_by_quantity
WITH (security_invoker = true)
AS
SELECT bi.id,
    bi.item_name,
    bi.item_category,
    bi.item_subcategory,
    bi.standard_price,
    COALESCE(sum(ri.quantity), (0)::numeric) AS total_quantity_sold,
    count(DISTINCT ri.receipt_id) AS number_of_orders,
    COALESCE(sum(ri.total_price), (0)::numeric) AS total_revenue,
    COALESCE(avg(ri.unit_price), (0)::numeric) AS avg_selling_price,
    max(r.purchase_date) AS last_sold_date,
    bi.company_id
FROM ((business_items bi
     LEFT JOIN receipt_items ri ON ((bi.id = ri.business_item_id)))
     LEFT JOIN receipts r ON ((ri.receipt_id = r.id)))
WHERE (bi.is_active = true)
GROUP BY bi.id, bi.item_name, bi.item_category, bi.item_subcategory, bi.standard_price, bi.company_id
ORDER BY COALESCE(sum(ri.quantity), (0)::numeric) DESC;

-- 6. Fix popular_items_by_revenue
DROP VIEW IF EXISTS public.popular_items_by_revenue CASCADE;
CREATE VIEW public.popular_items_by_revenue
WITH (security_invoker = true)
AS
SELECT bi.id,
    bi.item_name,
    bi.item_category,
    bi.item_subcategory,
    bi.standard_price,
    COALESCE(sum(ri.total_price), (0)::numeric) AS total_revenue,
    COALESCE(sum(ri.quantity), (0)::numeric) AS total_quantity_sold,
    count(DISTINCT ri.receipt_id) AS number_of_orders,
    COALESCE(avg(ri.unit_price), (0)::numeric) AS avg_selling_price,
    max(r.purchase_date) AS last_sold_date,
    bi.company_id
FROM ((business_items bi
     LEFT JOIN receipt_items ri ON ((bi.id = ri.business_item_id)))
     LEFT JOIN receipts r ON ((ri.receipt_id = r.id)))
WHERE (bi.is_active = true)
GROUP BY bi.id, bi.item_name, bi.item_category, bi.item_subcategory, bi.standard_price, bi.company_id
ORDER BY COALESCE(sum(ri.total_price), (0)::numeric) DESC;

-- 7. Fix most_active_members
DROP VIEW IF EXISTS public.most_active_members CASCADE;
CREATE VIEW public.most_active_members
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.name,
    lm.loyalty_id,
    lm.phone_number,
    lm.email,
    lm.loyalty_tier,
    lm.registration_date,
    count(DISTINCT pt.id) AS total_transactions,
    count(DISTINCT r.id) AS total_purchases,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
            ELSE 0
        END), (0)::bigint) AS total_points_earned,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS total_points_redeemed,
    COALESCE(lm.lifetime_points, 0) AS lifetime_points,
    COALESCE(mp.available_points, 0) AS available_points,
    COALESCE(sum(r.total_amount), (0)::numeric) AS total_spent,
    COALESCE(avg(r.total_amount), (0)::numeric) AS avg_order_value,
    max(pt.transaction_date) AS last_activity_date,
    lm.company_id
FROM (((loyalty_members lm
     LEFT JOIN points_transactions pt ON ((lm.id = pt.member_id)))
     LEFT JOIN receipts r ON ((pt.receipt_id = r.id)))
     LEFT JOIN member_points_live mp ON ((lm.id = mp.id)))
GROUP BY lm.id, lm.name, lm.loyalty_id, lm.phone_number, lm.email, lm.loyalty_tier, lm.registration_date, lm.lifetime_points, mp.available_points, lm.company_id
ORDER BY (count(DISTINCT pt.id)) DESC;

-- 8. Fix most_valuable_members
DROP VIEW IF EXISTS public.most_valuable_members CASCADE;
CREATE VIEW public.most_valuable_members
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.name,
    lm.loyalty_id,
    lm.phone_number,
    lm.email,
    lm.loyalty_tier,
    lm.registration_date,
    count(DISTINCT pt.id) AS total_transactions,
    count(DISTINCT r.id) AS total_purchases,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
            ELSE 0
        END), (0)::bigint) AS total_points_earned,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS total_points_redeemed,
    COALESCE(lm.lifetime_points, 0) AS lifetime_points,
    COALESCE(mp.available_points, 0) AS available_points,
    COALESCE(sum(r.total_amount), (0)::numeric) AS total_spent,
    COALESCE(avg(r.total_amount), (0)::numeric) AS avg_order_value,
    max(pt.transaction_date) AS last_activity_date,
    lm.company_id
FROM (((loyalty_members lm
     LEFT JOIN points_transactions pt ON ((lm.id = pt.member_id)))
     LEFT JOIN receipts r ON ((pt.receipt_id = r.id)))
     LEFT JOIN member_points_live mp ON ((lm.id = mp.id)))
GROUP BY lm.id, lm.name, lm.loyalty_id, lm.phone_number, lm.email, lm.loyalty_tier, lm.registration_date, lm.lifetime_points, mp.available_points, lm.company_id
ORDER BY COALESCE(sum(r.total_amount), (0)::numeric) DESC;

-- =====================================================
-- SECTION 2: FIX RLS AUTH FUNCTION RE-EVALUATION ISSUES
-- =====================================================
-- Replace direct auth.uid() calls with (SELECT auth.uid()) to cache results

-- Fix loyalty_members policies - consolidate and optimize
DROP POLICY IF EXISTS "cashier_view_members" ON public.loyalty_members;
DROP POLICY IF EXISTS "cashier_insert_members" ON public.loyalty_members;
DROP POLICY IF EXISTS "cashier_update_members" ON public.loyalty_members;

-- Create optimized cashier policy with cached auth function
CREATE POLICY "cashier_members_access" ON public.loyalty_members
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix points_transactions policies
DROP POLICY IF EXISTS "cashier_view_transactions" ON public.points_transactions;
DROP POLICY IF EXISTS "cashier_insert_transactions" ON public.points_transactions;

-- Create optimized cashier policy with cached auth function
CREATE POLICY "cashier_transactions_access" ON public.points_transactions
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix reward_redemptions policies
DROP POLICY IF EXISTS "cashier_view_redemptions" ON public.reward_redemptions;
DROP POLICY IF EXISTS "cashier_insert_redemptions" ON public.reward_redemptions;

-- Create optimized cashier policy with cached auth function
CREATE POLICY "cashier_redemptions_access" ON public.reward_redemptions
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix rewards policies
DROP POLICY IF EXISTS "cashier_view_rewards" ON public.rewards;

-- Create optimized cashier policy with cached auth function
CREATE POLICY "cashier_rewards_access" ON public.rewards
FOR SELECT TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix admin policies to use cached auth function calls
-- business_items
DROP POLICY IF EXISTS "business_items_admin_access" ON public.business_items;
CREATE POLICY "business_items_admin_access" ON public.business_items
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

-- loyalty_members admin access
DROP POLICY IF EXISTS "loyalty_members_admin_access" ON public.loyalty_members;
CREATE POLICY "loyalty_members_admin_access" ON public.loyalty_members
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

-- points_transactions admin access - update existing policy
DROP POLICY IF EXISTS "points_transactions_admin_access" ON public.points_transactions;
CREATE POLICY "points_transactions_admin_access" ON public.points_transactions
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

-- rewards admin access
DROP POLICY IF EXISTS "rewards_admin_access" ON public.rewards;
CREATE POLICY "rewards_admin_access" ON public.rewards
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

-- companies direct access policy
DROP POLICY IF EXISTS "companies_direct_access" ON public.companies;
CREATE POLICY "companies_direct_access" ON public.companies
FOR ALL TO authenticated
USING (administrator_id = (SELECT auth.uid()));

-- Fix dashboard_configurations multiple policies
DROP POLICY IF EXISTS "Users can update dashboard configurations for their company" ON public.dashboard_configurations;
DROP POLICY IF EXISTS "Users can view dashboard configurations for their company" ON public.dashboard_configurations;

-- Create single consolidated policy with cached auth function
CREATE POLICY "dashboard_configurations_admin_access" ON public.dashboard_configurations
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT c.id
    FROM companies c
    WHERE c.administrator_id = (SELECT auth.uid())
  )
);

-- =====================================================
-- SECTION 3: REMOVE DUPLICATE INDEXES
-- =====================================================
-- Remove duplicate indexes to save storage and improve performance

-- Remove duplicate loyalty_members indexes
DROP INDEX IF EXISTS public.idx_member_points_company_id;
DROP INDEX IF EXISTS public.idx_loyalty_members_company_reg;

-- =====================================================
-- SECTION 4: FIX FUNCTION SEARCH PATH MUTABLE WARNINGS
-- =====================================================
-- Add SET search_path = '' to all functions to prevent security issues

-- Functions that need complete recreation
CREATE OR REPLACE FUNCTION public.match_business_item(
    p_company_id UUID,
    p_description TEXT,
    p_price DECIMAL DEFAULT NULL
)
RETURNS TABLE(
    business_item_id UUID,
    item_name TEXT,
    confidence_score DECIMAL(3,2),
    matching_reason TEXT
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bi.id as business_item_id,
    bi.item_name,
    CASE
      WHEN bi.item_name ILIKE '%' || p_description || '%' THEN 0.90
      WHEN p_price IS NOT NULL AND ABS(bi.standard_price - p_price) <= (bi.standard_price * 0.1) THEN 0.75
      ELSE 0.50
    END as confidence_score,
    CASE
      WHEN bi.item_name ILIKE '%' || p_description || '%' THEN 'Name match'
      WHEN p_price IS NOT NULL AND ABS(bi.standard_price - p_price) <= (bi.standard_price * 0.1) THEN 'Price match'
      ELSE 'Partial match'
    END as matching_reason
  FROM public.business_items bi
  WHERE bi.company_id = p_company_id
    AND bi.is_active = true
    AND (
      bi.item_name ILIKE '%' || p_description || '%'
      OR (p_price IS NOT NULL AND ABS(bi.standard_price - p_price) <= (bi.standard_price * 0.1))
    )
  ORDER BY confidence_score DESC
  LIMIT 5;
END;
$$;

CREATE OR REPLACE FUNCTION public.update_business_item_stats(
    p_business_item_id UUID,
    p_quantity NUMERIC,
    p_sale_price NUMERIC
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
  UPDATE public.business_items
  SET
    total_sales_count = total_sales_count + p_quantity::INTEGER,
    updated_at = NOW()
  WHERE id = p_business_item_id;
END;
$$;

-- Apply SET search_path = '' to existing functions using ALTER FUNCTION
ALTER FUNCTION public.add_cashier SET search_path = '';
ALTER FUNCTION public.is_cashier SET search_path = '';
ALTER FUNCTION public.get_user_role SET search_path = '';
ALTER FUNCTION public.get_birthday_eligible_members SET search_path = '';
ALTER FUNCTION public.add_default_dashboard_config SET search_path = '';
ALTER FUNCTION public.cleanup_expired_invitations SET search_path = '';
ALTER FUNCTION public.generate_invitation_token SET search_path = '';
ALTER FUNCTION public.update_updated_at_column SET search_path = '';
ALTER FUNCTION public.get_campaign_eligible_members SET search_path = '';
ALTER FUNCTION public.send_telegram_notification_for_transaction SET search_path = '';
ALTER FUNCTION public.create_points_transaction SET search_path = '';
ALTER FUNCTION public.calculate_member_tier SET search_path = '';
ALTER FUNCTION public.get_company_config SET search_path = '';
ALTER FUNCTION public.get_member_growth SET search_path = '';
ALTER FUNCTION public.get_onboarding_status_optimized SET search_path = '';
ALTER FUNCTION public.get_detailed_customer_preferences SET search_path = '';
ALTER FUNCTION public.refresh_dashboard_metrics SET search_path = '';
ALTER FUNCTION public.update_member_point_balances SET search_path = '';
ALTER FUNCTION public.get_customer_insights SET search_path = '';
ALTER FUNCTION public.get_business_metrics_optimized SET search_path = '';
ALTER FUNCTION public.get_top_members SET search_path = '';
ALTER FUNCTION public.get_top_members_live SET search_path = '';
ALTER FUNCTION public.get_top_members_optimized SET search_path = '';
ALTER FUNCTION public.is_member_birthday_eligible SET search_path = '';
ALTER FUNCTION public.get_business_performance SET search_path = '';
ALTER FUNCTION public.get_template_analytics SET search_path = '';
ALTER FUNCTION public.get_recent_activity SET search_path = '';

-- Handle functions that might have multiple overloads
DO $$
DECLARE
    func_record RECORD;
BEGIN
    -- Fix all create_points_transaction function overloads
    FOR func_record IN
        SELECT oid, proname
        FROM pg_proc p
        JOIN pg_namespace n ON p.pronamespace = n.oid
        WHERE n.nspname = 'public' AND proname = 'create_points_transaction'
    LOOP
        EXECUTE format('ALTER FUNCTION %s SET search_path = ''''', func_record.oid::regprocedure);
    END LOOP;
END $$;

-- =====================================================
-- SECTION 5: ENABLE RLS ON TABLES MISSING IT
-- =====================================================
-- Enable Row Level Security on tables that don't have it

-- Check and enable RLS on backup tables
DO $$
DECLARE
    table_name text;
    tables text[] := ARRAY['points_transactions_backup', 'reward_redemptions_backup', 'loyalty_members_backup', 'telegram_conversations'];
BEGIN
    FOREACH table_name IN ARRAY tables
    LOOP
        BEGIN
            EXECUTE format('ALTER TABLE public.%I ENABLE ROW LEVEL SECURITY', table_name);

            -- Add basic RLS policy for admin access
            EXECUTE format('
                CREATE POLICY "%s_admin_access" ON public.%I
                FOR ALL TO authenticated
                USING (
                  EXISTS (
                    SELECT 1 FROM public.company_administrators ca
                    WHERE ca.administrator_id = (SELECT auth.uid())
                  )
                )', table_name, table_name);

        EXCEPTION WHEN undefined_table THEN
            -- Table doesn't exist, skip
            CONTINUE;
        WHEN duplicate_object THEN
            -- Policy already exists, skip
            CONTINUE;
        END;
    END LOOP;
END $$;

-- =====================================================
-- SECTION 6: MOVE EXTENSION FROM PUBLIC SCHEMA
-- =====================================================
-- Move pg_trgm extension from public to extensions schema

-- Create extensions schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS extensions;

-- Move extension (this might require superuser privileges)
-- Note: This section might need to be run separately with superuser privileges
-- DROP EXTENSION IF EXISTS pg_trgm;
-- CREATE EXTENSION IF NOT EXISTS pg_trgm SCHEMA extensions;

-- =====================================================
-- SECTION 7: RESTRICT MATERIALIZED VIEW ACCESS
-- =====================================================
-- Remove public API access to materialized views

-- Revoke public access from materialized views
DO $$
DECLARE
    mv_name text;
    views text[] := ARRAY['dashboard_metrics_mv', 'dashboard_metrics'];
BEGIN
    FOREACH mv_name IN ARRAY views
    LOOP
        BEGIN
            EXECUTE format('REVOKE ALL ON public.%I FROM anon, authenticated', mv_name);
            EXECUTE format('GRANT SELECT ON public.%I TO service_role', mv_name);
        EXCEPTION WHEN undefined_table THEN
            -- Materialized view doesn't exist, skip
            CONTINUE;
        END;
    END LOOP;
END $$;

-- Create secure function to access dashboard metrics
CREATE OR REPLACE FUNCTION public.get_dashboard_metrics_secure(p_company_id UUID)
RETURNS TABLE(
    company_id UUID,
    company_name TEXT,
    total_members BIGINT,
    active_members_30d BIGINT,
    total_lifetime_points BIGINT,
    total_redeemed_points BIGINT,
    total_expired_points BIGINT,
    total_available_points BIGINT,
    redemption_rate_percentage NUMERIC,
    days_since_first_member NUMERIC
)
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = ''
AS $$
BEGIN
    -- Verify user has access to this company
    IF NOT EXISTS (
        SELECT 1 FROM public.company_administrators ca
        WHERE ca.company_id = p_company_id
        AND ca.administrator_id = (SELECT auth.uid())
    ) THEN
        RAISE EXCEPTION 'Access denied to company data';
    END IF;

    RETURN QUERY
    SELECT
        dm.company_id,
        dm.company_name,
        dm.total_members,
        dm.active_members_30d,
        dm.total_lifetime_points,
        dm.total_redeemed_points,
        dm.total_expired_points,
        dm.total_available_points,
        dm.redemption_rate_percentage,
        dm.days_since_first_member
    FROM public.dashboard_metrics_corrected dm
    WHERE dm.company_id = p_company_id;
END;
$$;

-- Grant access to the secure function
GRANT EXECUTE ON FUNCTION public.get_dashboard_metrics_secure TO authenticated;

COMMIT;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these queries to verify the fixes have been applied correctly

-- Check that views are now using SECURITY INVOKER
SELECT
    schemaname,
    viewname,
    -- Check if view definition contains 'WITH (security_invoker = true)'
    CASE
        WHEN definition LIKE '%security_invoker%' THEN 'SECURITY INVOKER'
        ELSE 'SECURITY DEFINER (needs manual check)'
    END as security_type
FROM pg_views
WHERE schemaname = 'public'
AND viewname IN ('member_points_live', 'dashboard_metrics_corrected', 'popular_items_by_quantity')
ORDER BY viewname;

-- Check RLS policies are properly configured with cached auth functions
SELECT
    schemaname,
    tablename,
    policyname,
    -- Check if policy uses (SELECT auth.uid()) instead of direct auth.uid()
    CASE
        WHEN qual LIKE '%(SELECT auth.uid())%' THEN 'OPTIMIZED'
        WHEN qual LIKE '%auth.uid()%' THEN 'NEEDS FIX'
        ELSE 'NO AUTH FUNCTION'
    END as auth_optimization_status
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('loyalty_members', 'points_transactions', 'rewards')
ORDER BY tablename, policyname;

-- Check for remaining duplicate policies
SELECT
    schemaname,
    tablename,
    cmd,
    COUNT(*) as policy_count
FROM pg_policies
WHERE schemaname = 'public'
GROUP BY schemaname, tablename, cmd
HAVING COUNT(*) > 2  -- Allow up to 2 policies per command (admin + cashier)
ORDER BY tablename, cmd;

-- Check function search_path configuration
SELECT
    proname as function_name,
    CASE
        WHEN proconfig IS NOT NULL AND array_to_string(proconfig, ',') LIKE '%search_path=%' THEN 'CONFIGURED'
        ELSE 'NEEDS FIX'
    END as search_path_status
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
AND proname IN ('match_business_item', 'add_cashier', 'get_user_role')
ORDER BY proname;

-- Check remaining duplicate indexes
SELECT
    schemaname,
    tablename,
    COUNT(*) as index_count,
    string_agg(indexname, ', ' ORDER BY indexname) as index_names
FROM pg_indexes
WHERE schemaname = 'public'
AND tablename = 'loyalty_members'
AND indexname LIKE '%company_id%'
GROUP BY schemaname, tablename
ORDER BY tablename;

COMMENT ON SCHEMA public IS
'Database security and performance fixes applied:
- Views converted to SECURITY INVOKER
- RLS policies optimized with cached auth functions
- Duplicate policies consolidated
- Function search_path configured
- Duplicate indexes removed
- Materialized view access restricted
- RLS enabled on all tables
Applied on: ' || now()::text;
