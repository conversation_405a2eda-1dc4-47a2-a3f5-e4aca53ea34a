[{"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.dashboard_metrics_corrected\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "dashboard_metrics_corrected", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_dashboard_metrics_corrected"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.dashboard_metrics_live\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "dashboard_metrics_live", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_dashboard_metrics_live"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.secure_dashboard_metrics\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "secure_dashboard_metrics", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_secure_dashboard_metrics"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.member_points_live\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "member_points_live", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_member_points_live"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.popular_items_by_quantity\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "popular_items_by_quantity", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_popular_items_by_quantity"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.popular_items_by_revenue\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "popular_items_by_revenue", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_popular_items_by_revenue"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.most_active_members\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "most_active_members", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_most_active_members"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.most_valuable_members\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "most_valuable_members", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_most_valuable_members"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.business_performance_summary\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "business_performance_summary", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_business_performance_summary"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.category_performance\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "category_performance", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_category_performance"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.recent_activity_summary\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "recent_activity_summary", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_recent_activity_summary"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.monthly_trends\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "monthly_trends", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_monthly_trends"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.customer_item_preferences\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "customer_item_preferences", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_customer_item_preferences"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.customer_favorite_items\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "customer_favorite_items", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_customer_favorite_items"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.business_item_performance\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "business_item_performance", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_business_item_performance"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.category_revenue_analysis\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "category_revenue_analysis", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_category_revenue_analysis"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.template_performance_metrics\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "template_performance_metrics", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_template_performance_metrics"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.system_analytics_summary\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "system_analytics_summary", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_system_analytics_summary"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.analytics_summary_dashboard\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "analytics_summary_dashboard", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_analytics_summary_dashboard"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.analytics_customer_insights\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "analytics_customer_insights", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_analytics_customer_insights"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.analytics_business_performance\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "analytics_business_performance", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_analytics_business_performance"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.analytics_template_metrics\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "analytics_template_metrics", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_analytics_template_metrics"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.member_segments\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "member_segments", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_member_segments"}, {"name": "security_definer_view", "title": "Security Definer View", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects views defined with the SECURITY DEFINER property. These views enforce Postgres permissions and row level security policies (RLS) of the view creator, rather than that of the querying user", "detail": "View \\`public.campaign_analytics\\` is defined with the SECURITY DEFINER property", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0010_security_definer_view", "metadata": {"name": "campaign_analytics", "type": "view", "schema": "public"}, "cache_key": "security_definer_view_public_campaign_analytics"}, {"name": "rls_disabled_in_public", "title": "RLS Disabled in Public", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST", "detail": "Table \\`public.points_transactions_backup\\` is public, but RLS has not been enabled.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public", "metadata": {"name": "points_transactions_backup", "type": "table", "schema": "public"}, "cache_key": "rls_disabled_in_public_public_points_transactions_backup"}, {"name": "rls_disabled_in_public", "title": "RLS Disabled in Public", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST", "detail": "Table \\`public.reward_redemptions_backup\\` is public, but RLS has not been enabled.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public", "metadata": {"name": "reward_redemptions_backup", "type": "table", "schema": "public"}, "cache_key": "rls_disabled_in_public_public_reward_redemptions_backup"}, {"name": "rls_disabled_in_public", "title": "RLS Disabled in Public", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST", "detail": "Table \\`public.telegram_conversations\\` is public, but RLS has not been enabled.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public", "metadata": {"name": "telegram_conversations", "type": "table", "schema": "public"}, "cache_key": "rls_disabled_in_public_public_telegram_conversations"}, {"name": "rls_disabled_in_public", "title": "RLS Disabled in Public", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST", "detail": "Table \\`public.loyalty_members_backup\\` is public, but RLS has not been enabled.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public", "metadata": {"name": "loyalty_members_backup", "type": "table", "schema": "public"}, "cache_key": "rls_disabled_in_public_public_loyalty_members_backup"}, {"name": "rls_disabled_in_public", "title": "RLS Disabled in Public", "level": "ERROR", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects cases where row level security (RLS) has not been enabled on tables in schemas exposed to PostgREST", "detail": "Table \\`public.telegram_notifications\\` is public, but RLS has not been enabled.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0013_rls_disabled_in_public", "metadata": {"name": "telegram_notifications", "type": "table", "schema": "public"}, "cache_key": "rls_disabled_in_public_public_telegram_notifications"}]