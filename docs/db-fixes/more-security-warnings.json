[{"name": "extension_in_public", "title": "Extension in Public", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects extensions installed in the \\`public\\` schema.", "detail": "Extension \\`pg_trgm\\` is installed in the public schema. Move it to another schema.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0014_extension_in_public", "metadata": {"name": "pg_trgm", "type": "extension", "schema": "public"}, "cache_key": "extension_in_public_pg_trgm"}, {"name": "materialized_view_in_api", "title": "Materialized View in API", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects materialized views that are accessible over the Data APIs.", "detail": "Materialized view \\`public.dashboard_metrics_mv\\` is selectable by anon or authenticated roles", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0016_materialized_view_in_api", "metadata": {"name": "dashboard_metrics_mv", "type": "materialized view", "schema": "public"}, "cache_key": "materialized_view_in_api_public_dashboard_metrics_mv"}, {"name": "materialized_view_in_api", "title": "Materialized View in API", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects materialized views that are accessible over the Data APIs.", "detail": "Materialized view \\`public.dashboard_metrics\\` is selectable by anon or authenticated roles", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0016_materialized_view_in_api", "metadata": {"name": "dashboard_metrics", "type": "materialized view", "schema": "public"}, "cache_key": "materialized_view_in_api_public_dashboard_metrics"}, {"name": "auth_leaked_password_protection", "title": "Leaked Password Protection Disabled", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Leaked password protection is currently disabled.", "detail": "Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security.", "cache_key": "auth_leaked_password_protection", "remediation": "https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection", "metadata": {"type": "auth", "entity": "<PERSON><PERSON>"}}]