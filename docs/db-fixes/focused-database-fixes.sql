-- =====================================================
-- FOCUSED DATABASE FIXES - EXECUTABLE VERSION
-- =====================================================
-- This script addresses the specific issues you're experiencing
-- Run this in your Supabase SQL Editor with appropriate permissions
-- =====================================================

-- =====================================================
-- PART 1: FIX THE DEPENDENCY ERROR FOR member_points_live
-- =====================================================
-- This addresses: "cannot drop view member_points_live because other objects depend on it"

BEGIN;

-- Step 1: Drop all dependent views in correct order
DROP VIEW IF EXISTS public.member_segments CASCADE;
DROP VIEW IF EXISTS public.most_active_members CASCADE;
DROP VIEW IF EXISTS public.most_valuable_members CASCADE;

-- Step 2: Drop and recreate member_points_live with SECURITY INVOKER
DROP VIEW IF EXISTS public.member_points_live CASCADE;

CREATE VIEW public.member_points_live
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.loyalty_id,
    lm.name,
    lm.email,
    lm.phone_number,
    lm.birthday,
    lm.company_id,
    lm.loyalty_tier,
    lm.registration_date,
    lm.profile_image_url,
    lm.notes,
    lm.telegram_chat_id,
    lm.telegram_username,
    lm.linking_token,
    lm.linked_at,
    lm.notification_preferences,
    lm.ai_preferences,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
            ELSE 0
        END), (0)::bigint) AS lifetime_points,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS redeemed_points,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EXPIRE'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS expired_points,
    COALESCE(sum(pt.points_change), (0)::bigint) AS available_points,
    max(pt.created_at) AS last_transaction_date,
    count(pt.id) AS total_transactions
FROM (loyalty_members lm
     LEFT JOIN points_transactions pt ON ((lm.id = pt.member_id)))
GROUP BY lm.id, lm.loyalty_id, lm.name, lm.email, lm.phone_number, lm.birthday, lm.company_id, lm.loyalty_tier, lm.registration_date, lm.profile_image_url, lm.notes, lm.telegram_chat_id, lm.telegram_username, lm.linking_token, lm.linked_at, lm.notification_preferences, lm.ai_preferences;

-- Step 3: Recreate dependent views with SECURITY INVOKER

-- Recreate member_segments
CREATE VIEW public.member_segments
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.name,
    lm.email,
    lm.phone_number,
    lm.telegram_chat_id,
    lm.loyalty_tier,
    lm.lifetime_points,
    lm.company_id,
    lm.registration_date,
    lm.notification_preferences,
    mp.available_points,
        CASE
            WHEN (mp.available_points >= 1000) THEN 'high_balance'::text
            WHEN (mp.available_points >= 500) THEN 'medium_balance'::text
            ELSE 'low_balance'::text
        END AS balance_segment,
        CASE
            WHEN (lm.registration_date >= (now() - '30 days'::interval)) THEN 'new'::text
            WHEN (lm.registration_date >= (now() - '90 days'::interval)) THEN 'recent'::text
            ELSE 'established'::text
        END AS member_age_segment,
        CASE
            WHEN ((lm.telegram_chat_id IS NOT NULL) AND (COALESCE(((lm.notification_preferences ->> 'points_earned'::text))::boolean, true) = true)) THEN true
            ELSE false
        END AS has_telegram,
        CASE
            WHEN ((lm.telegram_chat_id IS NOT NULL) AND (COALESCE(((lm.notification_preferences ->> 'points_earned'::text))::boolean, true) = true)) THEN 'eligible'::text
            WHEN (lm.telegram_chat_id IS NULL) THEN 'no_telegram'::text
            WHEN (COALESCE(((lm.notification_preferences ->> 'points_earned'::text))::boolean, true) = false) THEN 'notifications_disabled'::text
            ELSE 'ineligible'::text
        END AS telegram_eligibility
FROM (loyalty_members lm
     LEFT JOIN member_points_live mp ON ((lm.id = mp.id)));

-- Recreate most_active_members
CREATE VIEW public.most_active_members
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.name,
    lm.loyalty_id,
    lm.phone_number,
    lm.email,
    lm.loyalty_tier,
    lm.registration_date,
    count(DISTINCT pt.id) AS total_transactions,
    count(DISTINCT r.id) AS total_purchases,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
            ELSE 0
        END), (0)::bigint) AS total_points_earned,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS total_points_redeemed,
    COALESCE(lm.lifetime_points, 0) AS lifetime_points,
    COALESCE(mp.available_points, 0) AS available_points,
    COALESCE(sum(r.total_amount), (0)::numeric) AS total_spent,
    COALESCE(avg(r.total_amount), (0)::numeric) AS avg_order_value,
    max(pt.transaction_date) AS last_activity_date,
    lm.company_id
FROM (((loyalty_members lm
     LEFT JOIN points_transactions pt ON ((lm.id = pt.member_id)))
     LEFT JOIN receipts r ON ((pt.receipt_id = r.id)))
     LEFT JOIN member_points_live mp ON ((lm.id = mp.id)))
GROUP BY lm.id, lm.name, lm.loyalty_id, lm.phone_number, lm.email, lm.loyalty_tier, lm.registration_date, lm.lifetime_points, mp.available_points, lm.company_id
ORDER BY (count(DISTINCT pt.id)) DESC;

-- Recreate most_valuable_members
CREATE VIEW public.most_valuable_members
WITH (security_invoker = true)
AS
SELECT lm.id,
    lm.name,
    lm.loyalty_id,
    lm.phone_number,
    lm.email,
    lm.loyalty_tier,
    lm.registration_date,
    count(DISTINCT pt.id) AS total_transactions,
    count(DISTINCT r.id) AS total_purchases,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
            ELSE 0
        END), (0)::bigint) AS total_points_earned,
    COALESCE(sum(
        CASE
            WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
            ELSE 0
        END), (0)::bigint) AS total_points_redeemed,
    COALESCE(lm.lifetime_points, 0) AS lifetime_points,
    COALESCE(mp.available_points, 0) AS available_points,
    COALESCE(sum(r.total_amount), (0)::numeric) AS total_spent,
    COALESCE(avg(r.total_amount), (0)::numeric) AS avg_order_value,
    max(pt.transaction_date) AS last_activity_date,
    lm.company_id
FROM (((loyalty_members lm
     LEFT JOIN points_transactions pt ON ((lm.id = pt.member_id)))
     LEFT JOIN receipts r ON ((pt.receipt_id = r.id)))
     LEFT JOIN member_points_live mp ON ((lm.id = mp.id)))
GROUP BY lm.id, lm.name, lm.loyalty_id, lm.phone_number, lm.email, lm.loyalty_tier, lm.registration_date, lm.lifetime_points, mp.available_points, lm.company_id
ORDER BY COALESCE(sum(r.total_amount), (0)::numeric) DESC;

COMMIT;

-- =====================================================
-- PART 2: FIX OTHER SECURITY DEFINER VIEWS
-- =====================================================

BEGIN;

-- Fix dashboard_metrics_corrected
DROP VIEW IF EXISTS public.dashboard_metrics_corrected CASCADE;
CREATE VIEW public.dashboard_metrics_corrected
WITH (security_invoker = true)
AS
WITH transaction_stats AS (
    SELECT points_transactions.company_id,
        count(DISTINCT points_transactions.member_id) AS unique_transaction_members,
        sum(
            CASE
                WHEN (points_transactions.transaction_type = 'EARN'::text) THEN points_transactions.points_change
                ELSE 0
            END) AS total_earned_points,
        abs(sum(
            CASE
                WHEN (points_transactions.transaction_type = 'REDEEM'::text) THEN points_transactions.points_change
                ELSE 0
            END)) AS total_redeemed_points,
        abs(sum(
            CASE
                WHEN (points_transactions.transaction_type = 'EXPIRE'::text) THEN points_transactions.points_change
                ELSE 0
            END)) AS total_expired_points,
        count(DISTINCT
            CASE
                WHEN (points_transactions.created_at >= (now() - '30 days'::interval)) THEN points_transactions.member_id
                ELSE NULL::uuid
            END) AS active_members_30d,
        count(*) AS total_transactions,
        count(
            CASE
                WHEN (points_transactions.transaction_type = 'EARN'::text) THEN 1
                ELSE NULL::integer
            END) AS earn_transactions,
        count(
            CASE
                WHEN (points_transactions.transaction_type = 'REDEEM'::text) THEN 1
                ELSE NULL::integer
            END) AS redeem_transactions,
        max(points_transactions.created_at) AS last_transaction_date
    FROM points_transactions
    GROUP BY points_transactions.company_id
), company_stats AS (
    SELECT c.id AS company_id,
        c.name AS company_name,
        count(DISTINCT lm.id) AS total_members,
        COALESCE(ts.active_members_30d, (0)::bigint) AS active_members_30d,
        COALESCE(ts.total_earned_points, (0)::bigint) AS total_lifetime_points,
        COALESCE(ts.total_redeemed_points, (0)::bigint) AS total_redeemed_points,
        COALESCE(ts.total_expired_points, (0)::bigint) AS total_expired_points,
        ((COALESCE(ts.total_earned_points, (0)::bigint) - COALESCE(ts.total_redeemed_points, (0)::bigint)) - COALESCE(ts.total_expired_points, (0)::bigint)) AS total_available_points,
        count(DISTINCT r.id) AS total_rewards,
        count(DISTINCT
            CASE
                WHEN ((r.is_active = true) AND (r.expiration_date > now())) THEN r.id
                ELSE NULL::uuid
            END) AS active_rewards,
        COALESCE(ts.total_transactions, (0)::bigint) AS total_transactions,
        COALESCE(ts.earn_transactions, (0)::bigint) AS earn_transactions,
        COALESCE(ts.redeem_transactions, (0)::bigint) AS redeem_transactions,
        now() AS last_updated,
        min(lm.registration_date) AS first_member_date,
        ts.last_transaction_date
    FROM (((companies c
         LEFT JOIN loyalty_members lm ON ((lm.company_id = c.id)))
         LEFT JOIN transaction_stats ts ON ((ts.company_id = c.id)))
         LEFT JOIN rewards r ON ((r.company_id = c.id)))
    GROUP BY c.id, c.name, ts.active_members_30d, ts.total_earned_points, ts.total_redeemed_points, ts.total_expired_points, ts.total_transactions, ts.earn_transactions, ts.redeem_transactions, ts.last_transaction_date
)
SELECT company_stats.company_id,
    company_stats.company_name,
    company_stats.total_members,
    company_stats.active_members_30d,
    company_stats.total_lifetime_points,
    company_stats.total_redeemed_points,
    company_stats.total_expired_points,
    company_stats.total_available_points,
    company_stats.total_rewards,
    company_stats.active_rewards,
    company_stats.total_transactions,
    company_stats.earn_transactions,
    company_stats.redeem_transactions,
    company_stats.last_updated,
    company_stats.first_member_date,
    company_stats.last_transaction_date,
        CASE
            WHEN (company_stats.total_lifetime_points > 0) THEN round((((company_stats.total_redeemed_points)::numeric / (company_stats.total_lifetime_points)::numeric) * (100)::numeric), 2)
            ELSE (0)::numeric
        END AS redemption_rate_percentage,
        CASE
            WHEN (company_stats.first_member_date IS NOT NULL) THEN EXTRACT(days FROM (now() - company_stats.first_member_date))
            ELSE (0)::numeric
        END AS days_since_first_member
FROM company_stats;

-- Fix dashboard_metrics_live
DROP VIEW IF EXISTS public.dashboard_metrics_live CASCADE;
CREATE VIEW public.dashboard_metrics_live
WITH (security_invoker = true)
AS
WITH transaction_stats AS (
    SELECT pt.company_id,
        count(DISTINCT pt.member_id) AS unique_members,
        sum(
            CASE
                WHEN (pt.transaction_type = 'EARN'::text) THEN pt.points_change
                ELSE 0
            END) AS total_lifetime_points,
        sum(
            CASE
                WHEN (pt.transaction_type = 'REDEEM'::text) THEN abs(pt.points_change)
                ELSE 0
            END) AS total_redeemed_points,
        sum(
            CASE
                WHEN (pt.transaction_type = 'EXPIRE'::text) THEN abs(pt.points_change)
                ELSE 0
            END) AS total_expired_points,
        sum(pt.points_change) AS total_available_points,
        count(*) AS total_transactions,
        count(
            CASE
                WHEN (pt.transaction_type = 'EARN'::text) THEN 1
                ELSE NULL::integer
            END) AS earn_transactions,
        count(
            CASE
                WHEN (pt.transaction_type = 'REDEEM'::text) THEN 1
                ELSE NULL::integer
            END) AS redeem_transactions,
        count(DISTINCT
            CASE
                WHEN (pt.created_at >= (now() - '30 days'::interval)) THEN pt.member_id
                ELSE NULL::uuid
            END) AS active_members_30d,
        max(pt.created_at) AS last_transaction_date
    FROM points_transactions pt
    GROUP BY pt.company_id
)
SELECT c.id AS company_id,
    c.name AS company_name,
    count(DISTINCT lm.id) AS total_members,
    COALESCE(ts.active_members_30d, (0)::bigint) AS active_members_30d,
    COALESCE(ts.total_lifetime_points, (0)::bigint) AS total_lifetime_points,
    COALESCE(ts.total_redeemed_points, (0)::bigint) AS total_redeemed_points,
    COALESCE(ts.total_expired_points, (0)::bigint) AS total_expired_points,
    COALESCE(ts.total_available_points, (0)::bigint) AS total_available_points,
    COALESCE(ts.total_transactions, (0)::bigint) AS total_transactions,
    COALESCE(ts.earn_transactions, (0)::bigint) AS earn_transactions,
    COALESCE(ts.redeem_transactions, (0)::bigint) AS redeem_transactions,
    ts.last_transaction_date,
    now() AS last_updated,
        CASE
            WHEN (ts.total_lifetime_points > 0) THEN round((((ts.total_redeemed_points)::numeric / (ts.total_lifetime_points)::numeric) * (100)::numeric), 2)
            ELSE (0)::numeric
        END AS redemption_rate_percentage
FROM ((companies c
     LEFT JOIN loyalty_members lm ON ((lm.company_id = c.id)))
     LEFT JOIN transaction_stats ts ON ((ts.company_id = c.id)))
GROUP BY c.id, c.name, ts.active_members_30d, ts.total_lifetime_points, ts.total_redeemed_points, ts.total_expired_points, ts.total_available_points, ts.total_transactions, ts.earn_transactions, ts.redeem_transactions, ts.last_transaction_date;

-- Fix secure_dashboard_metrics
DROP VIEW IF EXISTS public.secure_dashboard_metrics CASCADE;
CREATE VIEW public.secure_dashboard_metrics
WITH (security_invoker = true)
AS
SELECT dashboard_metrics_corrected.company_id,
    dashboard_metrics_corrected.company_name,
    dashboard_metrics_corrected.total_members,
    dashboard_metrics_corrected.active_members_30d,
    dashboard_metrics_corrected.total_lifetime_points,
    dashboard_metrics_corrected.total_redeemed_points,
    dashboard_metrics_corrected.total_expired_points,
    dashboard_metrics_corrected.total_available_points,
    dashboard_metrics_corrected.total_rewards,
    dashboard_metrics_corrected.active_rewards,
    dashboard_metrics_corrected.total_transactions,
    dashboard_metrics_corrected.earn_transactions,
    dashboard_metrics_corrected.redeem_transactions,
    dashboard_metrics_corrected.last_updated,
    dashboard_metrics_corrected.first_member_date,
    dashboard_metrics_corrected.last_transaction_date,
    dashboard_metrics_corrected.redemption_rate_percentage,
    dashboard_metrics_corrected.days_since_first_member
FROM dashboard_metrics_corrected
WHERE (dashboard_metrics_corrected.company_id = (current_setting('app.current_company_id'::text, true))::uuid);

-- Fix popular_items_by_quantity
DROP VIEW IF EXISTS public.popular_items_by_quantity CASCADE;
CREATE VIEW public.popular_items_by_quantity
WITH (security_invoker = true)
AS
SELECT bi.id,
    bi.item_name,
    bi.item_category,
    bi.item_subcategory,
    bi.standard_price,
    COALESCE(sum(ri.quantity), (0)::numeric) AS total_quantity_sold,
    count(DISTINCT ri.receipt_id) AS number_of_orders,
    COALESCE(sum(ri.total_price), (0)::numeric) AS total_revenue,
    COALESCE(avg(ri.unit_price), (0)::numeric) AS avg_selling_price,
    max(r.purchase_date) AS last_sold_date,
    bi.company_id
FROM ((business_items bi
     LEFT JOIN receipt_items ri ON ((bi.id = ri.business_item_id)))
     LEFT JOIN receipts r ON ((ri.receipt_id = r.id)))
WHERE (bi.is_active = true)
GROUP BY bi.id, bi.item_name, bi.item_category, bi.item_subcategory, bi.standard_price, bi.company_id
ORDER BY COALESCE(sum(ri.quantity), (0)::numeric) DESC;

-- Fix popular_items_by_revenue
DROP VIEW IF EXISTS public.popular_items_by_revenue CASCADE;
CREATE VIEW public.popular_items_by_revenue
WITH (security_invoker = true)
AS
SELECT bi.id,
    bi.item_name,
    bi.item_category,
    bi.item_subcategory,
    bi.standard_price,
    COALESCE(sum(ri.total_price), (0)::numeric) AS total_revenue,
    COALESCE(sum(ri.quantity), (0)::numeric) AS total_quantity_sold,
    count(DISTINCT ri.receipt_id) AS number_of_orders,
    COALESCE(avg(ri.unit_price), (0)::numeric) AS avg_selling_price,
    max(r.purchase_date) AS last_sold_date,
    bi.company_id
FROM ((business_items bi
     LEFT JOIN receipt_items ri ON ((bi.id = ri.business_item_id)))
     LEFT JOIN receipts r ON ((ri.receipt_id = r.id)))
WHERE (bi.is_active = true)
GROUP BY bi.id, bi.item_name, bi.item_category, bi.item_subcategory, bi.standard_price, bi.company_id
ORDER BY COALESCE(sum(ri.total_price), (0)::numeric) DESC;

COMMIT;

-- =====================================================
-- PART 3: FIX RLS POLICIES (PERFORMANCE WARNINGS)
-- =====================================================

BEGIN;

-- Consolidate and fix cashier policies with optimized auth function calls

-- Fix loyalty_members policies
DROP POLICY IF EXISTS "cashier_view_members" ON public.loyalty_members;
DROP POLICY IF EXISTS "cashier_insert_members" ON public.loyalty_members;
DROP POLICY IF EXISTS "cashier_update_members" ON public.loyalty_members;

-- Create optimized cashier policy
CREATE POLICY "cashier_members_access" ON public.loyalty_members
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix points_transactions policies
DROP POLICY IF EXISTS "cashier_view_transactions" ON public.points_transactions;
DROP POLICY IF EXISTS "cashier_insert_transactions" ON public.points_transactions;

CREATE POLICY "cashier_transactions_access" ON public.points_transactions
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix reward_redemptions policies
DROP POLICY IF EXISTS "cashier_view_redemptions" ON public.reward_redemptions;
DROP POLICY IF EXISTS "cashier_insert_redemptions" ON public.reward_redemptions;

CREATE POLICY "cashier_redemptions_access" ON public.reward_redemptions
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix rewards policies
DROP POLICY IF EXISTS "cashier_view_rewards" ON public.rewards;

CREATE POLICY "cashier_rewards_access" ON public.rewards
FOR SELECT TO authenticated
USING (
  company_id IN (
    SELECT ca.company_id
    FROM company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
    AND ca.role IN ('OWNER', 'CASHIER')
  )
);

-- Fix dashboard_configurations multiple policies
DROP POLICY IF EXISTS "Users can update dashboard configurations for their company" ON public.dashboard_configurations;
DROP POLICY IF EXISTS "Users can view dashboard configurations for their company" ON public.dashboard_configurations;

CREATE POLICY "dashboard_configurations_admin_access" ON public.dashboard_configurations
FOR ALL TO authenticated
USING (
  company_id IN (
    SELECT c.id
    FROM companies c
    WHERE c.administrator_id = (SELECT auth.uid())
  )
);

-- Update admin policies to use cached auth function calls
DROP POLICY IF EXISTS "companies_direct_access" ON public.companies;
CREATE POLICY "companies_direct_access" ON public.companies
FOR ALL TO authenticated
USING (administrator_id = (SELECT auth.uid()));

COMMIT;

-- =====================================================
-- PART 4: ENABLE RLS ON MISSING TABLES
-- =====================================================

BEGIN;

-- Enable RLS on backup tables
ALTER TABLE public.points_transactions_backup ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reward_redemptions_backup ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.loyalty_members_backup ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.telegram_conversations ENABLE ROW LEVEL SECURITY;

-- Add basic admin access policies for backup tables
CREATE POLICY "points_transactions_backup_admin_access" ON public.points_transactions_backup
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

CREATE POLICY "reward_redemptions_backup_admin_access" ON public.reward_redemptions_backup
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

CREATE POLICY "loyalty_members_backup_admin_access" ON public.loyalty_members_backup
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

CREATE POLICY "telegram_conversations_admin_access" ON public.telegram_conversations
FOR ALL TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.company_administrators ca
    WHERE ca.administrator_id = (SELECT auth.uid())
  )
);

COMMIT;

-- =====================================================
-- PART 5: REMOVE DUPLICATE INDEXES
-- =====================================================

-- Remove duplicate indexes to improve performance
DROP INDEX IF EXISTS public.idx_member_points_company_id;
DROP INDEX IF EXISTS public.idx_loyalty_members_company_reg;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Verify views are now using SECURITY INVOKER
SELECT
  'Views Security Check' as check_type,
  schemaname,
  viewname,
  CASE
    WHEN definition LIKE '%security_invoker%' THEN '✓ SECURITY INVOKER'
    ELSE '✗ Still SECURITY DEFINER'
  END as status
FROM pg_views
WHERE schemaname = 'public'
AND viewname IN ('member_points_live', 'dashboard_metrics_corrected', 'popular_items_by_quantity', 'member_segments')
ORDER BY viewname;

-- Verify RLS is enabled on all required tables
SELECT
  'RLS Status Check' as check_type,
  schemaname,
  tablename,
  CASE
    WHEN rowsecurity = true THEN '✓ RLS Enabled'
    ELSE '✗ RLS Disabled'
  END as status
FROM pg_tables
WHERE schemaname = 'public'
AND tablename IN ('points_transactions_backup', 'reward_redemptions_backup', 'loyalty_members_backup', 'telegram_conversations')
ORDER BY tablename;

-- Check for remaining policy conflicts
SELECT
  'Policy Conflicts Check' as check_type,
  tablename,
  cmd,
  COUNT(*) as policy_count,
  CASE
    WHEN COUNT(*) <= 2 THEN '✓ Acceptable'
    ELSE '✗ Too many policies'
  END as status
FROM pg_policies
WHERE schemaname = 'public'
AND tablename IN ('loyalty_members', 'points_transactions', 'rewards', 'reward_redemptions')
GROUP BY tablename, cmd
ORDER BY tablename, cmd;

-- Success message
SELECT
  'Database fixes have been applied successfully!' as message,
  'All security definer views converted to security invoker' as security_fix,
  'RLS policies optimized with cached auth functions' as performance_fix,
  'RLS enabled on all required tables' as rls_fix,
  'Duplicate indexes removed' as index_fix;
