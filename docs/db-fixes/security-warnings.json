[{"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.match_business_item\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "match_business_item", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_match_business_item_3c079a502602bad18a80b479fe05c705"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_business_item_stats\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_business_item_stats", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_business_item_stats_df65389232019269c9cd090d974967f2"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_customer_seasonal_preferences\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_customer_seasonal_preferences", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_customer_seasonal_preferences_4c95dea397c265b6f517009450a78fec"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_service_bundling_patterns\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_service_bundling_patterns", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_service_bundling_patterns_b741b3225d425514284ddf29bfbd1a0a"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_tiers_with_member_counts\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_tiers_with_member_counts", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_tiers_with_member_counts_054be273d6ed0b5ef53e9161603a8cd9"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.analyze_template_errors\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "analyze_template_errors", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_analyze_template_errors_321bf7aed87b50683160ef25add39f31"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.add_cashier\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "add_cashier", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_add_cashier_be2e2d5adf4660694fc8ccab72f8f14f"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.is_cashier\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "is_cashier", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_is_cashier_4a4d329dba43e64a2baf05d6140651d3"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_user_role\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_user_role", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_user_role_2d8a7c1af087b8387b61da919c02e962"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_birthday_eligible_members\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_birthday_eligible_members", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_birthday_eligible_members_1df7c91c87ea7f75b71d68fdaa3fbce9"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.add_default_dashboard_config\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "add_default_dashboard_config", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_add_default_dashboard_config_63a958da9a6b4e6cab4dea855d80ff7e"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.cleanup_expired_invitations\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "cleanup_expired_invitations", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_cleanup_expired_invitations_9e5f7aeb1f37550610494040d9058ec9"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.generate_invitation_token\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "generate_invitation_token", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_generate_invitation_token_5234e78511fd1aba7ffb261f01cc57f7"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_updated_at_column\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_updated_at_column", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_updated_at_column_06bcf30ac3d0a7f279a54cbf228a7bec"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_campaign_eligible_members\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_campaign_eligible_members", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_campaign_eligible_members_283055ee7e818a255a0f198a9f5088b2"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.send_telegram_notification_for_transaction\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "send_telegram_notification_for_transaction", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_send_telegram_notification_for_transaction_5332987976a7647fc876d8001fa3381a"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_points_transaction\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_points_transaction", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_points_transaction_58e047a854090cc90b27f291c5f4f00d"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.calculate_member_tier\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "calculate_member_tier", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_calculate_member_tier_d76aa04798917bd66320545399dac82c"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_company_config\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_company_config", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_company_config_0d9ae0c8ece01a667cf5953ecb1d51fc"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_member_growth\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_member_growth", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_member_growth_95e4f5646e9a5a059fd277e7858f2f90"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_onboarding_status_optimized\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_onboarding_status_optimized", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_onboarding_status_optimized_5c2969cb50fcb4f4606acb3b4821b52f"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_detailed_customer_preferences\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_detailed_customer_preferences", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_detailed_customer_preferences_6a1f9b6322732a58a4604833c7f583a5"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.refresh_dashboard_metrics\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "refresh_dashboard_metrics", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_refresh_dashboard_metrics_2f9553d035b85418f1a71198e79124f6"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.update_member_point_balances\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "update_member_point_balances", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_update_member_point_balances_dbf5842f2aa6373d7c0b2c29d77ba929"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_customer_insights\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_customer_insights", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_customer_insights_01892abb53c036e02667495d703e383e"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.create_points_transaction\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "create_points_transaction", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_create_points_transaction_4a41578798d89bc8086fcecf50a1c216"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_business_metrics_optimized\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_business_metrics_optimized", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_business_metrics_optimized_45f73799adee827e77100bd78ce6cb69"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_top_members\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_top_members", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_top_members_15085016a081d8cc7d10ceded6b0278d"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_top_members_live\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_top_members_live", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_top_members_live_199f97dd4f72c06596c6f5c5bb6b5dca"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_top_members_optimized\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_top_members_optimized", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_top_members_optimized_daf8624b37e119e5c098387602f3d832"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.is_member_birthday_eligible\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "is_member_birthday_eligible", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_is_member_birthday_eligible_949fe6a9278b09e91d9545aa02a40224"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_business_performance\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_business_performance", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_business_performance_854e087dc4a8b5f39fa8c4bf0eaf5db7"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_template_analytics\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_template_analytics", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_template_analytics_b53ea3e32eaddbd8db9f384b63c64aa5"}, {"name": "function_search_path_mutable", "title": "Function Search Path Mutable", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects functions where the search_path parameter is not set.", "detail": "Function \\`public.get_recent_activity\\` has a role mutable search_path", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable", "metadata": {"name": "get_recent_activity", "type": "function", "schema": "public"}, "cache_key": "function_search_path_mutable_public_get_recent_activity_b04b20fd8d82e0e4c15469b64f882e3c"}, {"name": "extension_in_public", "title": "Extension in Public", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects extensions installed in the \\`public\\` schema.", "detail": "Extension \\`pg_trgm\\` is installed in the public schema. Move it to another schema.", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0014_extension_in_public", "metadata": {"name": "pg_trgm", "type": "extension", "schema": "public"}, "cache_key": "extension_in_public_pg_trgm"}, {"name": "materialized_view_in_api", "title": "Materialized View in API", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects materialized views that are accessible over the Data APIs.", "detail": "Materialized view \\`public.dashboard_metrics_mv\\` is selectable by anon or authenticated roles", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0016_materialized_view_in_api", "metadata": {"name": "dashboard_metrics_mv", "type": "materialized view", "schema": "public"}, "cache_key": "materialized_view_in_api_public_dashboard_metrics_mv"}, {"name": "materialized_view_in_api", "title": "Materialized View in API", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Detects materialized views that are accessible over the Data APIs.", "detail": "Materialized view \\`public.dashboard_metrics\\` is selectable by anon or authenticated roles", "remediation": "https://supabase.com/docs/guides/database/database-linter?lint=0016_materialized_view_in_api", "metadata": {"name": "dashboard_metrics", "type": "materialized view", "schema": "public"}, "cache_key": "materialized_view_in_api_public_dashboard_metrics"}, {"name": "auth_leaked_password_protection", "title": "Leaked Password Protection Disabled", "level": "WARN", "facing": "EXTERNAL", "categories": ["SECURITY"], "description": "Leaked password protection is currently disabled.", "detail": "Supabase Auth prevents the use of compromised passwords by checking against HaveIBeenPwned.org. Enable this feature to enhance security.", "cache_key": "auth_leaked_password_protection", "remediation": "https://supabase.com/docs/guides/auth/password-security#password-strength-and-leaked-password-protection", "metadata": {"type": "auth", "entity": "<PERSON><PERSON>"}}]