# Database tables and views — summary

This file lists schemas, tables and views in the database with a short description and a recommended safety marker indicating whether the object is safe to delete.

Legend:
- MUST KEEP: critical to platform or Supabase-managed (do not delete)
- DANGEROUS: likely used by application or analytics; verify before deleting
- SAFE TO DELETE: can usually be removed (back up first)

> Notes & assumptions
- Descriptions are concise and inferred from common naming conventions and repository context. For any object marked anything other than SAFE TO DELETE, confirm usage in code, triggers, policies and ACLs before removing.

## auth schema
- `auth.audit_log_entries` — audit records for auth events. MUST KEEP
- `auth.flow_state` — auth flow state storage (SSO/OAuth). MUST KEEP
- `auth.identities` — external identity mappings (OAuth, SSO). MUST KEEP
- `auth.instances` — auth instance metadata. MUST KEEP
- `auth.mfa_amr_claims` — MFA claim records. MUST KEEP
- `auth.mfa_challenges` — MFA challenge records. MUST KEEP
- `auth.mfa_factors` — MFA registered factors. MUST KEEP
- `auth.oauth_clients` — OAuth client registrations. MUST KEEP
- `auth.one_time_tokens` — one-time login tokens. MUST KEEP
- `auth.refresh_tokens` — refresh tokens for sessions. MUST KEEP
- `auth.saml_providers` — SAML provider configs. MUST KEEP
- `auth.saml_relay_states` — SAML relay state records. MUST KEEP
- `auth.schema_migrations` — auth schema migration tracking. MUST KEEP
- `auth.sessions` — active user sessions. MUST KEEP
- `auth.sso_domains` — SSO domain config. MUST KEEP
- `auth.sso_providers` — SSO provider config. MUST KEEP
- `auth.users` — user accounts managed by Supabase. MUST KEEP

## cron schema
- `cron.job` — scheduled job definitions. DANGEROUS (verify job usage)
- `cron.job_run_details` — execution logs/results for cron jobs. DANGEROUS

## extensions schema
- `extensions.pg_stat_statements` — extension view for query stats. SAFE TO DELETE (view only; dropping extension affects monitoring)
- `extensions.pg_stat_statements_info` — supplementary view for pg_stat_statements. SAFE TO DELETE

## pgsodium schema
- `pgsodium.decrypted_key` — view used by encryption utilities. DANGEROUS
- `pgsodium.key` — key storage for pgsodium. DANGEROUS (sensitive)
- `pgsodium.mask_columns` — masking helper view. DANGEROUS
- `pgsodium.masking_rule` — masking rules view. DANGEROUS
- `pgsodium.valid_key` — view indicating valid keys. DANGEROUS

## public schema
- `public.administrators` — app admin users and metadata. DANGEROUS
- `public.analytics_business_performance` — analytics view. SAFE TO DELETE (recreatable)
- `public.analytics_customer_insights` — analytics view. SAFE TO DELETE
- `public.analytics_summary_dashboard` — analytics view. SAFE TO DELETE
- `public.analytics_template_metrics` — analytics view. SAFE TO DELETE
- `public.audit_log` — application audit trail. DANGEROUS (may be needed for compliance)
- `public.business_item_performance` — analytics view. SAFE TO DELETE
- `public.business_items` — product/catalog items. DANGEROUS
- `public.business_performance_summary` — analytics view. SAFE TO DELETE
- `public.campaign_analytics` — analytics view. SAFE TO DELETE
- `public.campaign_recipients` — marketing campaign recipients. DANGEROUS (used by campaigns)
- `public.cashier_invitations` — invites for cashier users. DANGEROUS (active onboarding)
- `public.category_performance` — analytics view. SAFE TO DELETE
- `public.category_revenue_analysis` — analytics view. SAFE TO DELETE
- `public.companies` — tenant/company records. MUST KEEP
- `public.company_administrators` — relations between companies and admins. DANGEROUS
- `public.customer_favorite_items` — analytics view. SAFE TO DELETE
- `public.customer_item_preferences` — analytics view. SAFE TO DELETE
- `public.dashboard_configurations` — saved dashboard settings. DANGEROUS (user data)
- `public.dashboard_metrics_corrected` — view (analytics). SAFE TO DELETE
- `public.dashboard_metrics_history` — stored metric history. DANGEROUS (important for audits)
- `public.dashboard_metrics_live` — live metrics view. SAFE TO DELETE
- `public.item_matching_suggestions` — item match suggestions (UX). DANGEROUS
- `public.loyalty_members` — member accounts for loyalty program. MUST KEEP
- `public.loyalty_members_backup` — backup table for members. SAFE TO DELETE (if backup exists elsewhere)
- `public.marketing_campaigns` — campaign definitions. DANGEROUS
- `public.member_notifications` — queued/sent notifications to members. DANGEROUS
- `public.member_points` — view for member points (derived). SAFE TO DELETE (view)
- `public.member_points_live` — live points view. SAFE TO DELETE
- `public.member_segments` — segmentation view. SAFE TO DELETE
- `public.member_summary` — member summary view. SAFE TO DELETE
- `public.monthly_trends` — analytics view. SAFE TO DELETE
- `public.most_active_members` — analytics view. SAFE TO DELETE
- `public.most_valuable_members` — analytics view. SAFE TO DELETE
- `public.points_transactions` — points transactions ledger. MUST KEEP (core data)
- `public.points_transactions_backup` — backup of transactions. SAFE TO DELETE (if redundancy exists)
- `public.popular_items_by_quantity` — analytics view. SAFE TO DELETE
- `public.popular_items_by_revenue` — analytics view. SAFE TO DELETE
- `public.program_rules` — loyalty program rule definitions. DANGEROUS
- `public.receipt_items` — line items for receipts. DANGEROUS
- `public.receipt_templates` — templates used to render receipts. DANGEROUS
- `public.receipts` — receipt records. DANGEROUS (business-critical)
- `public.recent_activity_summary` — analytics view. SAFE TO DELETE
- `public.reward_redemptions` — reward redemption records. DANGEROUS (financial/audit)
- `public.reward_redemptions_backup` — backup of redemptions. SAFE TO DELETE
- `public.reward_tier_eligibility` — eligibility table for tiers. DANGEROUS
- `public.rewards` — reward definitions. DANGEROUS
- `public.secure_dashboard_metrics` — secured metrics view. DANGEROUS
- `public.system_analytics_summary` — system analytics view. SAFE TO DELETE
- `public.telegram_conversations` — telegram conversation logs. DANGEROUS (customer comms)
- `public.telegram_notifications` — telegram notification queue. DANGEROUS
- `public.template_performance_metrics` — analytics view. SAFE TO DELETE
- `public.tier_definitions` — loyalty tier definitions. DANGEROUS

## realtime schema
- `realtime.messages` — realtime engine messages. DANGEROUS (depends on realtime usage)
- `realtime.schema_migrations` — migration tracking for realtime. DANGEROUS
- `realtime.subscription` — realtime subscription records. DANGEROUS

## storage schema
- `storage.buckets` — storage buckets metadata. MUST KEEP
- `storage.buckets_analytics` — analytics for buckets. SAFE TO DELETE
- `storage.migrations` — migration tracking for storage. DANGEROUS
- `storage.objects` — stored objects metadata. DANGEROUS (contains pointers to files)
- `storage.prefixes` — storage prefixes metadata. DANGEROUS
- `storage.s3_multipart_uploads` — multipart uploads in progress. DANGEROUS
- `storage.s3_multipart_uploads_parts` — multipart upload parts. DANGEROUS

## vault schema
- `vault.decrypted_secrets` — view for secret management. DANGEROUS (sensitive)
- `vault.secrets` — secrets storage. MUST KEEP (sensitive)

---

If you'd like, I can:
- add row counts next to each table
- export this summary as CSV or update a README
- fetch column-level schemas for any subset of tables

When ready tell me which follow-up you want and I'll proceed.
