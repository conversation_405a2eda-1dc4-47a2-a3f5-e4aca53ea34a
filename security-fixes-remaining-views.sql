-- REMAINING SECURITY DEFINER VIEW FIXES
-- This completes the fixes for all 15 views identified in the security audit

-- 6. Fix customer_favorite_items view
DROP VIEW IF EXISTS public.customer_favorite_items CASCADE;
CREATE VIEW public.customer_favorite_items
WITH (security_invoker = true)
AS
SELECT DISTINCT ON (customer_item_preferences.member_id) customer_item_preferences.member_id,
    customer_item_preferences.member_name,
    customer_item_preferences.item_name AS favorite_item,
    customer_item_preferences.item_category AS favorite_category,
    customer_item_preferences.purchase_count,
    customer_item_preferences.total_spent,
    customer_item_preferences.preference_score
FROM customer_item_preferences
ORDER BY customer_item_preferences.member_id, customer_item_preferences.preference_score DESC;

-- 7. Fix business_item_performance view
DROP VIEW IF EXISTS public.business_item_performance CASCADE;
CREATE VIEW public.business_item_performance
WITH (security_invoker = true)
AS
SELECT c.id AS company_id,
    c.name AS company_name,
    bi.id AS business_item_id,
    bi.item_name,
    bi.item_category,
    bi.standard_price,
    count(ri.id) AS total_sales,
    sum(ri.total_price) AS total_revenue,
    avg(ri.unit_price) AS avg_selling_price,
    max(r.purchase_date) AS last_sold_date,
    (sum(ri.total_price) / (NULLIF(count(DISTINCT r.member_id), 0))::numeric) AS revenue_per_customer,
    count(DISTINCT r.member_id) AS unique_customers,
    CASE
        WHEN (avg(ri.unit_price) > (bi.standard_price * 1.1)) THEN 'Premium Pricing'::text
        WHEN (avg(ri.unit_price) < (bi.standard_price * 0.9)) THEN 'Discounted'::text
        ELSE 'Standard Pricing'::text
    END AS pricing_strategy,
    (((count(ri.id))::numeric * 0.6) + ((count(DISTINCT r.member_id))::numeric * 0.4)) AS popularity_score
FROM (((companies c
     JOIN business_items bi ON ((bi.company_id = c.id)))
     LEFT JOIN receipt_items ri ON ((ri.business_item_id = bi.id)))
     LEFT JOIN receipts r ON ((r.id = ri.receipt_id)))
WHERE (bi.is_active = true)
GROUP BY c.id, c.name, bi.id, bi.item_name, bi.item_category, bi.standard_price
ORDER BY (((count(ri.id))::numeric * 0.6) + ((count(DISTINCT r.member_id))::numeric * 0.4)) DESC;

-- 8. Fix category_revenue_analysis view
DROP VIEW IF EXISTS public.category_revenue_analysis CASCADE;
CREATE VIEW public.category_revenue_analysis
WITH (security_invoker = true)
AS
SELECT c.id AS company_id,
    c.name AS company_name,
    bi.item_category,
    count(DISTINCT bi.id) AS items_in_category,
    count(ri.id) AS total_transactions,
    sum(ri.total_price) AS total_revenue,
    avg(ri.unit_price) AS avg_price,
    count(DISTINCT r.member_id) AS unique_customers,
    round(((sum(ri.total_price) / NULLIF(sum(sum(ri.total_price)) OVER (PARTITION BY c.id), (0)::numeric)) * (100)::numeric), 2) AS revenue_percentage,
    CASE
        WHEN (count(CASE WHEN (r.purchase_date >= (now() - '3 mons'::interval)) THEN 1 ELSE NULL::integer END) > 
              count(CASE WHEN ((r.purchase_date >= (now() - '6 mons'::interval)) AND (r.purchase_date < (now() - '3 mons'::interval))) THEN 1 ELSE NULL::integer END)) THEN 'Growing'::text
        WHEN (count(CASE WHEN (r.purchase_date >= (now() - '3 mons'::interval)) THEN 1 ELSE NULL::integer END) < 
              count(CASE WHEN ((r.purchase_date >= (now() - '6 mons'::interval)) AND (r.purchase_date < (now() - '3 mons'::interval))) THEN 1 ELSE NULL::integer END)) THEN 'Declining'::text
        ELSE 'Stable'::text
    END AS trend
FROM (((companies c
     JOIN business_items bi ON ((bi.company_id = c.id)))
     LEFT JOIN receipt_items ri ON ((ri.business_item_id = bi.id)))
     LEFT JOIN receipts r ON ((r.id = ri.receipt_id)))
WHERE (bi.is_active = true)
GROUP BY c.id, c.name, bi.item_category
HAVING (count(ri.id) > 0)
ORDER BY (sum(ri.total_price)) DESC;

-- 9. Fix template_performance_metrics view
DROP VIEW IF EXISTS public.template_performance_metrics CASCADE;
CREATE VIEW public.template_performance_metrics
WITH (security_invoker = true)
AS
SELECT rt.id AS template_id,
    rt.template_name,
    c.name AS company_name,
    rt.confidence_threshold,
    rt.total_extractions,
    rt.successful_extractions,
    rt.avg_confidence_score,
    CASE
        WHEN (rt.total_extractions > 0) THEN round((((rt.successful_extractions)::numeric / (rt.total_extractions)::numeric) * (100)::numeric), 2)
        ELSE (0)::numeric
    END AS success_rate_percentage,
    count(CASE WHEN (r.created_at >= (now() - '30 days'::interval)) THEN 1 ELSE NULL::integer END) AS recent_extractions,
    avg(CASE WHEN (r.created_at >= (now() - '30 days'::interval)) THEN r.extraction_confidence ELSE NULL::numeric END) AS recent_avg_confidence,
    CASE
        WHEN ((rt.total_extractions > 0) AND (rt.avg_confidence_score IS NOT NULL)) THEN ((((rt.successful_extractions)::numeric / (rt.total_extractions)::numeric) * 0.7) + (rt.avg_confidence_score * 0.3))
        ELSE (0)::numeric
    END AS effectiveness_score
FROM ((receipt_templates rt
     JOIN companies c ON ((c.id = rt.company_id)))
     LEFT JOIN receipts r ON ((r.template_id = rt.id)))
WHERE (rt.is_active = true)
GROUP BY rt.id, rt.template_name, c.name, rt.confidence_threshold, rt.total_extractions, rt.successful_extractions, rt.avg_confidence_score
ORDER BY CASE
    WHEN ((rt.total_extractions > 0) AND (rt.avg_confidence_score IS NOT NULL)) THEN ((((rt.successful_extractions)::numeric / (rt.total_extractions)::numeric) * 0.7) + (rt.avg_confidence_score * 0.3))
    ELSE (0)::numeric
END DESC;

-- 10. Fix system_analytics_summary view
DROP VIEW IF EXISTS public.system_analytics_summary CASCADE;
CREATE VIEW public.system_analytics_summary
WITH (security_invoker = true)
AS
SELECT count(DISTINCT lm.id) AS total_customers,
    count(DISTINCT c.id) AS total_businesses,
    count(DISTINCT bi.id) AS total_business_items,
    count(DISTINCT rt.id) AS total_templates,
    count(r.id) AS total_receipts_processed,
    count(ri.id) AS total_items_extracted,
    sum(r.total_amount) AS total_transaction_value,
    avg(r.extraction_confidence) AS avg_extraction_confidence,
    count(CASE WHEN (r.template_id IS NOT NULL) THEN 1 ELSE NULL::integer END) AS template_enhanced_receipts,
    round((((count(CASE WHEN (r.template_id IS NOT NULL) THEN 1 ELSE NULL::integer END))::numeric / (NULLIF(count(r.id), 0))::numeric) * (100)::numeric), 2) AS template_usage_percentage
FROM (((((loyalty_members lm
     CROSS JOIN companies c)
     LEFT JOIN business_items bi ON ((bi.company_id = c.id)))
     LEFT JOIN receipt_templates rt ON ((rt.company_id = c.id)))
     LEFT JOIN receipts r ON (((r.member_id = lm.id) OR (r.company_id = c.id))))
     LEFT JOIN receipt_items ri ON ((ri.receipt_id = r.id)));

-- 11. Fix analytics_summary_dashboard view
DROP VIEW IF EXISTS public.analytics_summary_dashboard CASCADE;
CREATE VIEW public.analytics_summary_dashboard
WITH (security_invoker = true)
AS
SELECT c.id AS company_id,
    c.name AS company_name,
    c.business_type,
    count(DISTINCT lm.id) AS total_customers,
    count(DISTINCT lm.id) FILTER (WHERE (lm.registration_date >= (now() - '30 days'::interval))) AS new_customers_30d,
    count(DISTINCT lm.id) FILTER (WHERE (lm.lifetime_points > 0)) AS active_customers,
    count(DISTINCT r.id) AS total_receipts,
    count(DISTINCT r.id) FILTER (WHERE (r.created_at >= (now() - '30 days'::interval))) AS receipts_30d,
    COALESCE(sum(r.total_amount), (0)::numeric) AS total_revenue,
    COALESCE(sum(r.total_amount) FILTER (WHERE (r.created_at >= (now() - '30 days'::interval))), (0)::numeric) AS revenue_30d,
    CASE
        WHEN (count(DISTINCT r.id) > 0) THEN round((sum(r.total_amount) / (count(DISTINCT r.id))::numeric), 2)
        ELSE (0)::numeric
    END AS avg_transaction_value,
    count(DISTINCT bi.id) AS total_business_items,
    count(DISTINCT bi.id) FILTER (WHERE (bi.is_active = true)) AS active_business_items,
    count(DISTINCT rt.id) AS total_templates,
    count(DISTINCT r.id) FILTER (WHERE (r.template_id IS NOT NULL)) AS template_enhanced_receipts,
    CASE
        WHEN (count(DISTINCT r.id) > 0) THEN round((((count(DISTINCT r.id) FILTER (WHERE (r.template_id IS NOT NULL)))::numeric / (count(DISTINCT r.id))::numeric) * (100)::numeric), 2)
        ELSE (0)::numeric
    END AS template_usage_percentage,
    avg(r.extraction_confidence) AS avg_extraction_confidence,
    now() AS calculated_at
FROM ((((companies c
     LEFT JOIN loyalty_members lm ON ((c.id = lm.company_id)))
     LEFT JOIN receipts r ON ((c.id = r.company_id)))
     LEFT JOIN business_items bi ON ((c.id = bi.company_id)))
     LEFT JOIN receipt_templates rt ON (((c.id = rt.company_id) AND (rt.is_active = true))))
GROUP BY c.id, c.name, c.business_type;

-- 12. Fix analytics_customer_insights view (already provided in main file)

-- 13. Fix analytics_business_performance view (already provided in main file)

-- 14. Fix analytics_template_metrics view
DROP VIEW IF EXISTS public.analytics_template_metrics CASCADE;
CREATE VIEW public.analytics_template_metrics
WITH (security_invoker = true)
AS
SELECT rt.id,
    rt.company_id,
    rt.template_name,
    COALESCE(rt.confidence_threshold, 0.8) AS confidence_threshold,
    COALESCE(rt.total_extractions, 0) AS total_extractions,
    COALESCE(rt.successful_extractions, 0) AS successful_extractions,
    COALESCE(rt.avg_confidence_score, (0)::numeric) AS avg_confidence_score,
    CASE
        WHEN (COALESCE(rt.total_extractions, 0) > 0) THEN round((((COALESCE(rt.successful_extractions, 0))::numeric / (rt.total_extractions)::numeric) * (100)::numeric), 2)
        ELSE (0)::numeric
    END AS success_rate_percentage,
    count(r.id) FILTER (WHERE (r.created_at >= (now() - '30 days'::interval))) AS recent_extractions,
    avg(r.extraction_confidence) FILTER (WHERE (r.created_at >= (now() - '30 days'::interval))) AS recent_avg_confidence,
    CASE
        WHEN ((COALESCE(rt.total_extractions, 0) > 0) AND (rt.avg_confidence_score IS NOT NULL)) THEN ((((COALESCE(rt.successful_extractions, 0))::numeric / (rt.total_extractions)::numeric) * 0.7) + (rt.avg_confidence_score * 0.3))
        ELSE (0)::numeric
    END AS effectiveness_score,
    rt.created_at,
    rt.updated_at
FROM (receipt_templates rt
     LEFT JOIN receipts r ON ((rt.id = r.template_id)))
WHERE (rt.is_active = true)
GROUP BY rt.id, rt.company_id, rt.template_name, rt.confidence_threshold, rt.total_extractions, rt.successful_extractions, rt.avg_confidence_score, rt.created_at, rt.updated_at;

-- 15. Fix campaign_analytics view
DROP VIEW IF EXISTS public.campaign_analytics CASCADE;
CREATE VIEW public.campaign_analytics
WITH (security_invoker = true)
AS
SELECT mc.id,
    mc.name,
    mc.status,
    mc.total_recipients,
    mc.successful_sends,
    mc.failed_sends,
    round(
        CASE
            WHEN (mc.total_recipients > 0) THEN (((mc.successful_sends)::numeric / (mc.total_recipients)::numeric) * (100)::numeric)
            ELSE (0)::numeric
        END, 2) AS delivery_rate_percentage,
    mc.sent_at,
    mc.created_at,
    mc.company_id,
    count(cr.id) AS tracked_recipients,
    count(CASE WHEN ((cr.status)::text = 'sent'::text) THEN 1 ELSE NULL::integer END) AS confirmed_delivered,
    count(CASE WHEN ((cr.status)::text = 'failed'::text) THEN 1 ELSE NULL::integer END) AS confirmed_failed,
    count(CASE WHEN ((cr.status)::text = 'pending'::text) THEN 1 ELSE NULL::integer END) AS pending_delivery
FROM (marketing_campaigns mc
     LEFT JOIN campaign_recipients cr ON ((mc.id = cr.campaign_id)))
GROUP BY mc.id, mc.name, mc.status, mc.total_recipients, mc.successful_sends, mc.failed_sends, mc.sent_at, mc.created_at, mc.company_id;
