-- Create bot_configurations table for premium bots
CREATE TABLE IF NOT EXISTS bot_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE UNIQUE NOT NULL,
  
  -- Bot Identity
  bot_token VARCHAR(255) UNIQUE NOT NULL,
  bot_username VA<PERSON>HA<PERSON>(255) UNIQUE NOT NULL,
  bot_name VA<PERSON>HAR(255) NOT NULL,
  bot_description TEXT,
  
  -- Configuration
  webhook_url TEXT,
  webhook_token VARCHAR(255),
  webhook_secret VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  
  -- Status Tracking
  creation_status VARCHAR(50) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  activated_at TIMESTAMP WITH TIME ZONE,
  last_health_check TIMESTAMP WITH TIME ZONE,
  last_activity TIMESTAMP WITH TIME ZONE,
  message_count INTEGER DEFAULT 0,
  
  -- Custom Settings
  custom_settings JSONB DEFAULT '{}',
  branding_config <PERSON><PERSON><PERSON><PERSON> DEFAULT '{}',
  
  -- Constraints
  CONSTRAINT valid_creation_status CHECK (creation_status IN ('pending', 'created', 'configured', 'active', 'failed'))
);

-- Add bot tier columns to companies table (safe defaults)
ALTER TABLE companies ADD COLUMN IF NOT EXISTS bot_tier VARCHAR(50) DEFAULT 'standard';
ALTER TABLE companies ADD COLUMN IF NOT EXISTS bot_configuration_id UUID REFERENCES bot_configurations(id);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_bot_configurations_company_id ON bot_configurations(company_id);
CREATE INDEX IF NOT EXISTS idx_bot_configurations_token ON bot_configurations(bot_token);
CREATE INDEX IF NOT EXISTS idx_bot_configurations_username ON bot_configurations(bot_username);
CREATE INDEX IF NOT EXISTS idx_bot_configurations_status ON bot_configurations(creation_status);
CREATE INDEX IF NOT EXISTS idx_companies_bot_tier ON companies(bot_tier);

-- Add RLS policies for security
ALTER TABLE bot_configurations ENABLE ROW LEVEL SECURITY;

-- Allow company administrators to access their bot configurations
CREATE POLICY "bot_configurations_company_access" ON bot_configurations
  FOR ALL USING (
    company_id IN (
      SELECT company_id FROM company_administrators 
      WHERE administrator_id = auth.uid()
      UNION
      SELECT id FROM companies
      WHERE administrator_id = auth.uid()
    )
  );

-- Add webhook verification function for security
CREATE OR REPLACE FUNCTION verify_bot_webhook_signature(webhook_token TEXT, signature TEXT, body TEXT) 
RETURNS BOOLEAN AS $$
DECLARE
  expected_signature TEXT;
BEGIN
  -- Generate HMAC-SHA256 signature
  expected_signature := encode(
    hmac(body, webhook_token, 'sha256'),
    'hex'
  );
  
  -- Compare with provided signature
  RETURN expected_signature = signature;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Verify existing companies are set to standard tier
-- SELECT name, bot_tier FROM companies WHERE is_active = true;
