import { useMemo } from 'react';
import { useCompanyAdminQuery } from './use-company-admin-query';

export interface UserRole {
  role: 'OWNER' | 'CASHIER' | null;
  isOwner: boolean;
  isCashier: boolean;
  hasRole: boolean;
}

/**
 * Custom hook that provides role-based permissions and utilities
 */
export function useUserRole(): UserRole {
  const { adminData, isAdmin, isLoading } = useCompanyAdminQuery();

  const roleInfo = useMemo(() => {
    // If still loading, return null state
    if (isLoading) {
      return {
        role: null,
        isOwner: false,
        isCashier: false,
        hasRole: false,
      } as UserRole;
    }

    // If not admin, no role
    if (!isAdmin || !adminData?.role) {
      return {
        role: null,
        isOwner: false,
        isCashier: false,
        hasRole: false,
      } as UserRole;
    }

    const role = adminData.role as 'OWNER' | 'CASHIER';

    return {
      role,
      isOwner: role === 'OWNER',
      isCashier: role === 'CASHIER',
      hasRole: true,
    } as UserRole;
  }, [isAdmin, adminData, isLoading]);

  return roleInfo;
}

/**
 * Navigation items for different roles
 */
export const getNavigationForRole = (userRole: UserRole) => {
  const cashierItems = [
    { name: 'Members', href: '/members', icon: 'Users', requiredRoles: ['CASHIER'] },
    { name: 'Transactions', href: '/transactions', icon: 'CreditCard', requiredRoles: ['CASHIER'] },
  ];

  const ownerItems = [
    { name: 'Dashboard', href: '/dashboard', icon: 'LayoutDashboard', requiredRoles: ['OWNER'] },
    { name: 'Members', href: '/members', icon: 'Users', requiredRoles: ['OWNER'] },
    { name: 'Transactions', href: '/transactions', icon: 'CreditCard', requiredRoles: ['OWNER'] },
    { name: 'Rewards', href: '/rewards', icon: 'Gift', requiredRoles: ['OWNER'] },
    { name: 'Tiers', href: '/tiers', icon: 'Award', requiredRoles: ['OWNER'] },
    { name: 'Settings', href: '/settings', icon: 'Settings', requiredRoles: ['OWNER'] },
  ];

  // If no role, return empty array
  if (!userRole.hasRole || !userRole.role) {
    return [];
  }

  // Return appropriate items based on role
  if (userRole.isCashier) {
    return cashierItems;
  } else if (userRole.isOwner) {
    return ownerItems;
  }

  return [];
};
