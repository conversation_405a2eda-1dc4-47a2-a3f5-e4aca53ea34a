import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useCompany } from "@/contexts/company-context";
import { queryKeys, CACHE_TIMES, COMMON_QUERY_OPTIONS } from "@/lib/query-config";
import { getRewardStatusLabel } from "@/lib/reward-calculations";

export interface CreateRewardData {
  title: string;
  description: string;
  code: string;
  reward_code: string;
  reward_type: string;
  reward_value_type: string;
  reward_value: number;
  points_required: number;
  is_active: boolean;
  start_date: string;
  expiration_date: string;
}

export interface Reward {
  id: string;
  title: string;
  name?: string; // Keep for backwards compatibility
  description?: string;
  points_required: number;
  points_cost?: number; // Keep for backwards compatibility
  reward_value_type: 'PERCENTAGE' | 'FIXED_AMOUNT' | 'FREE_SERVICE' | 'POINTS_BONUS' | 'PRODUCT_GIFT' | 'DOUBLE_POINTS';
  reward_value: number;
  is_active: boolean;
  active?: boolean; // Keep for backwards compatibility
  image_url?: string; // Keep for backwards compatibility
  reward_image_url?: string; // New field
  company_id: string;
  created_at?: string;
  expiration_date?: string | null;
  // Expiration status fields (added by API)
  is_expired?: boolean;
  expires_soon?: boolean;
  status_label?: {
    status: 'active' | 'expires_soon' | 'expired';
    label: string;
    variant: 'default' | 'warning' | 'destructive';
  };
}

export interface Redemption {
  id: string;
  member_id: string;
  reward_id: string;
  points_used: number;
  created_at?: string;
  company_id: string;
  reward?: Reward;
}

/**
 * Hook to fetch all rewards with redemption counts
 */
export function useRewards() {
  const { company } = useCompany();
  const companyId = company?.id;

  return useQuery({
    queryKey: queryKeys.rewards(companyId || ''),
    queryFn: async () => {
      if (!companyId) {
        return { data: [] };
      }

      // Fetch rewards
      const rewardsResponse = await fetch(`/api/rewards?companyId=${companyId}`);
      if (!rewardsResponse.ok) {
        throw new Error("Failed to fetch rewards");
      }
      const rewardsData = await rewardsResponse.json();
      
      // Fetch redemptions to calculate counts
      const redemptionsResponse = await fetch(`/api/redemptions?companyId=${companyId}`);
      if (!redemptionsResponse.ok) {
        console.error("Failed to fetch redemptions for count calculation");
        return rewardsData; // Return rewards without counts if redemptions fetch fails
      }
      
      const redemptionsData = await redemptionsResponse.json();
      
      // Calculate redemption counts for each reward
      const redemptionCounts: Record<string, number> = {};
      if (redemptionsData && redemptionsData.data) {
        redemptionsData.data.forEach((redemption: { reward_id: string }) => {
          const rewardId = redemption.reward_id;
          if (!redemptionCounts[rewardId]) {
            redemptionCounts[rewardId] = 0;
          }
          redemptionCounts[rewardId]++;
        });
      }
      
      // Add redemption counts and expiration status to rewards data
      if (rewardsData && rewardsData.data) {
        rewardsData.data = rewardsData.data.map((reward: { id: string; expiration_date?: string }) => ({
          ...reward,
          redemption_count: redemptionCounts[reward.id] || 0,
          status_label: reward.expiration_date ? getRewardStatusLabel(reward.expiration_date) : undefined
        }));
      }
      
      return rewardsData;
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.NORMAL,
    gcTime: CACHE_TIMES.NORMAL * 2,
    ...COMMON_QUERY_OPTIONS,
  });
}

/**
 * Hook to fetch all redemptions
 */
export function useRedemptions(limit?: number) {
  const { company } = useCompany();
  const companyId = company?.id;

  return useQuery({
    queryKey: queryKeys.redemptions(companyId || '', limit),
    queryFn: async () => {
      if (!companyId) {
        return { data: [] };
      }

      const url = limit
        ? `/api/redemptions?companyId=${companyId}&limit=${limit}`
        : `/api/redemptions?companyId=${companyId}`;

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error("Failed to fetch redemptions");
      }
      return response.json();
    },
    enabled: !!companyId,
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * Hook to fetch a single reward by ID
 */
export function useReward(rewardId: string) {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ['reward', companyId || '', rewardId] as const,
    queryFn: async () => {
      if (!companyId || !rewardId) {
        return null;
      }

      // First try to use the existing rewards query cache
      const rewardsData = queryClient.getQueryData<{ data: Reward[] }>(queryKeys.rewards(companyId));

      if (rewardsData) {
        const cachedReward = rewardsData.data.find(r => r.id === rewardId);
        if (cachedReward) {
          return cachedReward;
        }
      }

      // If not found in cache, fetch directly
      const response = await fetch(`/api/rewards/${rewardId}?companyId=${companyId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch reward");
      }
      return response.json();
    },
    enabled: !!companyId && !!rewardId,
    staleTime: 60 * 1000, // 1 minute
  });
}

/**
 * Hook to create a new reward
 */
export function useCreateReward() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (rewardData: CreateRewardData) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch("/api/rewards", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ...rewardData,
          companyId: companyId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create reward");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: queryKeys.rewards(companyId || '') });
    },
  });
}

/**
 * Hook to create a new redemption
 */
export function useCreateRedemption() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (redemptionData: Omit<Redemption, "id" | "created_at" | "company_id">) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch("/api/redemptions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...redemptionData,
          company_id: companyId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to create redemption");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      queryClient.invalidateQueries({ queryKey: queryKeys.redemptions(companyId || '') });
      // Also invalidate member data as points may have changed
      queryClient.invalidateQueries({ queryKey: queryKeys.members(companyId || '') });
    },
  });
}

/**
 * Hook to update an existing reward
 */
export function useUpdateReward() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ rewardId, rewardData }: {
      rewardId: string;
      rewardData: Partial<Omit<Reward, "id" | "created_at" | "company_id">>
    }) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch(`/api/rewards/${rewardId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...rewardData,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update reward");
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch rewards list
      queryClient.invalidateQueries({ queryKey: queryKeys.rewards(companyId || '') });
      // Update the specific reward in cache if it exists
      queryClient.invalidateQueries({ queryKey: ['reward', companyId || '', variables.rewardId] });
    },
  });
}

/**
 * Hook to check if a reward code exists
 */
export function useCheckRewardCode() {
  const { company } = useCompany();
  const companyId = company?.id;

  return useMutation({
    mutationFn: async (code: string) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      if (!code) {
        return { exists: false };
      }

      const response = await fetch(`/api/rewards/check-code?code=${encodeURIComponent(code)}&companyId=${companyId}`);

      if (!response.ok) {
        // Don't throw error for code checking - just assume code doesn't exist
        console.warn('Failed to check reward code:', response.statusText);
        return { exists: false };
      }

      return response.json();
    },
  });
}

/**
 * Custom error type for reward deletion
 */
export type RewardDeleteError = {
  code?: string
  message?: string
  status?: number
}

/**
 * Hook for deleting a reward
 */
export function useDeleteReward() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation<
    { success: boolean; message: string },
    RewardDeleteError,
    { rewardId: string; force?: boolean }
  >({
    mutationFn: async ({ rewardId, force = false }) => {
      if (!companyId) {
        throw { message: "Company ID is required", status: 400 };
      }

      const response = await fetch(
        `/api/rewards/${rewardId}?companyId=${companyId}${force ? '&force=true' : ''}`,
        {
          method: 'DELETE',
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw {
          code: errorData.code,
          message: errorData.message || errorData.error || 'Failed to delete reward',
          status: response.status
        };
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries to refetch data
      if (companyId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.rewards(companyId) });
      }
    },
  });
}
