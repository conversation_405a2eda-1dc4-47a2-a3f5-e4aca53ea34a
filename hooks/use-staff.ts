import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { queryKeys, CACHE_TIMES } from '@/lib/query-config'
import { useCompany } from '@/contexts/company-context'

interface StaffData {
  companyId: string
  companyName: string
  staffEmails: string[]
}

interface InviteStaffData {
  companyId: string
  companyName: string
  staffEmails: string[]
  message: string
}

export function useStaffEmails() {
  const { company } = useCompany()

  return useQuery<StaffData>({
    queryKey: queryKeys.staff(company?.id || ''),
    queryFn: async () => {
      const response = await fetch('/api/staff/invite')
      if (!response.ok) {
        throw new Error('Failed to fetch staff emails')
      }
      const result = await response.json()
      return result.data
    },
    enabled: !!company?.id,
    staleTime: CACHE_TIMES.NORMAL, // 5 minutes
    retry: 1
  })
}

export function useInviteStaff() {
  const queryClient = useQueryClient()
  const { company } = useCompany()

  return useMutation({
    mutationFn: async ({ emails }: { emails: string[] }) => {
      const response = await fetch('/api/staff/invite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ emails }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to invite staff')
      }

      const result = await response.json()
      return result.data as InviteStaffData
    },
    onSuccess: () => {
      // Invalidate and refetch staff data using centralized query keys
      if (company?.id) {
        queryClient.invalidateQueries({ queryKey: queryKeys.staff(company.id) })
      }
      // Also invalidate company data in case it affects the overall state
      queryClient.invalidateQueries({ queryKey: ['company'] })
    }
  })
}
