import { useQuery } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { CACHE_TIMES, COMMON_QUERY_OPTIONS, queryKeys } from '@/lib/query-config'

export interface BusinessPurchaseAnalytics {
  company_id: string
  company_name: string
  summary: {
    total_members: number
    total_receipts: number
    total_revenue: number
    avg_order_value: number
    total_items: number
    total_transactions: number
  }
  recent_activity: {
    new_members: number
    receipts: number
    revenue: number
    active_members: number
    avg_order_value: number
    transactions: number
  }
  growth: {
    revenue_growth: number
    customer_growth: number
  }
  top_items: {
    by_quantity: Array<{
      id: string
      name: string
      category?: string
      subcategory?: string
      quantity_sold: number
      revenue: number
      orders: number
      avg_price: number
      last_sold?: string
    }>
    by_revenue: Array<{
      id: string
      name: string
      category?: string
      subcategory?: string
      quantity_sold: number
      revenue: number
      orders: number
      avg_price: number
      last_sold?: string
    }>
  }
  category_performance: Array<{
    category: string
    total_items: number
    quantity_sold: number
    revenue: number
    orders: number
    avg_price: number
    avg_quantity_per_order: number
  }>
  monthly_trends: Array<{
    month: string
    revenue: number
    receipts: number
    customers: number
    avgOrderValue: number
  }>
  member_analytics: {
    top_spending_members: Array<{
      member_id: string
      name: string
      phone_number: string
      total_spent: number
      transaction_count: number
      avg_order_value: number
      favorite_item?: string | null
    }>
  }
  weekly_item_trends: Array<{
    week: string
    week_start: string
    item1?: number
    item1_name?: string
    item1_revenue?: number
    item2?: number
    item2_name?: string
    item2_revenue?: number
    item3?: number
    item3_name?: string
    item3_revenue?: number
    item4?: number
    item4_name?: string
    item4_revenue?: number
    item5?: number
    item5_name?: string
    item5_revenue?: number
  }>
  weekly_spender_trends: Array<{
    week: string
    week_start: string
    spender1?: number
    spender1_name?: string
    spender1_transactions?: number
    spender2?: number
    spender2_name?: string
    spender2_transactions?: number
    spender3?: number
    spender3_name?: string
    spender3_transactions?: number
    spender4?: number
    spender4_name?: string
    spender4_transactions?: number
    spender5?: number
    spender5_name?: string
    spender5_transactions?: number
  }>
}

export function useBusinessPurchaseAnalytics() {
  const { company } = useCompany()
  const companyId = company?.id

  return useQuery({
    queryKey: queryKeys.businessPurchaseAnalytics(companyId || ''),
    queryFn: async (): Promise<BusinessPurchaseAnalytics | null> => {
      if (!companyId) {
        console.log('useBusinessPurchaseAnalytics: No company ID available')
        return null
      }

      console.log('useBusinessPurchaseAnalytics: Fetching analytics for company:', companyId)

      const response = await fetch(`/api/business/purchase-analytics?companyId=${companyId}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch business purchase analytics: ${response.status}`)
      }

      const data = await response.json()
      return data
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.NORMAL,
    gcTime: CACHE_TIMES.NORMAL * 2,
    ...COMMON_QUERY_OPTIONS,
    retry: 2,
  })
}
