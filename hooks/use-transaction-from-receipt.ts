import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCompany } from "@/contexts/company-context";
import { queryKeys } from '@/lib/query-config';
import type { ReceiptData } from "@/lib/receipt-ocr";
import type { Transaction } from "./use-transactions";

interface CreateTransactionFromReceiptData {
  member_id: string;
  transaction_type?: 'EARN' | 'REDEEM';
  image: File;
}

interface TransactionFromReceiptResult {
  success: boolean;
  data: {
    transaction: Transaction;
    ocr_data: ReceiptData;
    suggested_form_data: {
      description: string;
      total_amount: number;
      business_name: string;
      financial_system_number: string;
      points_change: number;
      receipt_date: string;
    };
    image_url: string;
    confidence: number;
  };
}

/**
 * Hook to create a transaction directly from a receipt image
 * This combines upload + OCR + transaction creation into one atomic operation
 */
export function useCreateTransactionFromReceipt() {
  const { company } = useCompany();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTransactionFromReceiptData): Promise<TransactionFromReceiptResult> => {
      if (!company) {
        throw new Error("Company ID is required");
      }

      const formData = new FormData();
      formData.append('image', data.image);
      formData.append('member_id', data.member_id);
      formData.append('company_id', company.id);
      formData.append('transaction_type', data.transaction_type || 'EARN');

      const response = await fetch('/api/transactions/create-from-receipt', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create transaction from receipt');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: queryKeys.transactions(company?.id || '') });
      queryClient.invalidateQueries({ queryKey: queryKeys.members(company?.id || '') });
    },
  });
}
