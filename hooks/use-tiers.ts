import { useQuery } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { CACHE_TIMES, queryKeys, COMMON_QUERY_OPTIONS } from '@/lib/query-config'

export interface TierDefinition {
  id: string
  tier_name: string
  minimum_points: number
  benefits_description?: string
  company_id: string
}

export function useTiers() {
  const { company } = useCompany()
  const companyId = company?.id

  return useQuery({
    queryKey: queryKeys.tiers(companyId || ''),
    queryFn: async (): Promise<TierDefinition[]> => {
      if (!companyId) {
        return []
      }

      const response = await fetch(`/api/tiers?companyId=${companyId}`)
      if (!response.ok) {
        throw new Error('Failed to fetch tiers')
      }

      const data = await response.json()
      return data || []
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.NORMAL,
    gcTime: CACHE_TIMES.NORMAL * 2,
    ...COMMON_QUERY_OPTIONS
  })
}
