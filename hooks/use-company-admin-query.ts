import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { CACHE_TIMES, queryKeys } from '@/lib/query-config';
import { useMemo } from 'react';

export interface CompanyAdministrator {
  company_id: string;
  administrator_id: string;
  role?: string;
  created_at?: string;
}

/**
 * Custom hook for fetching company administrator relationship using React Query
 * This replaces the problematic direct Supabase query in analytics page
 */
export function useCompanyAdminQuery() {
  const { user, isLoading: authLoading } = useAuth();
  const queryClient = useQueryClient();
  
  // Memoize the user ID to prevent unnecessary re-renders
  const userId = useMemo(() => user?.id || null, [user?.id]);

  const {
    data: adminResponse,
    isLoading: adminLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: queryKeys.companyAdmin(userId || ''), // Use centralized query key
    queryFn: async () => {
      if (!userId) {
        console.log('useCompanyAdminQuery: No user ID available, skipping fetch');
        return null;
      }
      
      console.log('useCompanyAdminQuery: Fetching admin data via API for user:', userId);

      // Use the API endpoint with cache-busting query param to prevent stale responses
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin-status?_t=${timestamp}`, {
        method: 'GET',
        credentials: 'include', // Include cookies for authentication
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log('User not authenticated');
          return null;
        }
        throw new Error(`Failed to fetch admin status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Admin status API response:', data);

      if (data.isAdmin && data.adminData) {
        return data.adminData as CompanyAdministrator;
      }

      console.log('User has no admin privileges');
      return null;
    },
    enabled: !!userId && !authLoading, // Only enabled when we have a user ID and auth is not loading
    staleTime: CACHE_TIMES.NORMAL, // Use normal cache time for admin status
    gcTime: CACHE_TIMES.NORMAL * 2,
    refetchOnWindowFocus: false, // Prevent refetch on window focus
    refetchOnMount: true, // Always refetch on mount to ensure fresh data
    retry: 2, // Retry failed requests more times
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // Exponential backoff
  });

  // Combine loading states
  const isLoading = authLoading || adminLoading;
  
  // Memoize the result to prevent unnecessary re-renders
  const result = useMemo(() => ({
    userId,
    adminData: adminResponse,
    companyId: adminResponse?.company_id || null,
    isLoading,
    error,
    refetch,
    isAdmin: !!adminResponse,
    // Add invalidation helper
    invalidateAdminStatus: () => queryClient.invalidateQueries({ queryKey: queryKeys.companyAdmin(userId || '') }),
  }), [userId, adminResponse, isLoading, error, refetch, queryClient]);

  // Remove noisy console.log to reduce spam
  // console.log('useCompanyAdminQuery result:', {
  //   userId: userId,
  //   adminData: adminResponse,
  //   companyId: result.companyId,
  //   isAdmin: result.isAdmin,
  //   isLoading,
  //   error: error?.message
  // });

  return result;
}
