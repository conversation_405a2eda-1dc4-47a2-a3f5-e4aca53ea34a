import { useQuery } from '@tanstack/react-query'
import { useCompany } from '@/contexts/company-context'
import { CACHE_TIMES, COMMON_QUERY_OPTIONS, QUERY_KEYS } from '@/lib/query-config'

interface BusinessSummary {
  total_members: number
  total_receipts: number
  total_transactions: number
  total_revenue: number
  avg_order_value: number
}

export function useBusinessSummary() {
  const { company } = useCompany()
  
  return useQuery({
    queryKey: [QUERY_KEYS.BUSINESS_ANALYTICS, 'summary', company?.id],
    queryFn: async (): Promise<BusinessSummary> => {
      if (!company?.id) throw new Error('No company ID')
      
      const response = await fetch(`/api/analytics/business?companyId=${company.id}&type=summary`)
      if (!response.ok) throw new Error('Failed to fetch business summary')
      
      const result = await response.json()
      return result.data
    },
    enabled: !!company?.id,
    staleTime: CACHE_TIMES.NORMAL,
    ...COMMON_QUERY_OPTIONS
  })
}

