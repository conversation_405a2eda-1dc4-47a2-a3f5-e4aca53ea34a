import { useAuth } from './use-auth'
import { useQuery } from '@tanstack/react-query'

export interface SuperAdminData {
  isSuperAdmin: boolean
  user: {
    id: string
    email: string
  } | null
}

export function useSuperAdmin() {
  const { user, isLoading: authLoading } = useAuth()

  const { data, isLoading: adminLoading, error } = useQuery({
    queryKey: ['super-admin', user?.id],
    queryFn: async (): Promise<SuperAdminData> => {
      if (!user) {
        return { isSuperAdmin: false, user: null }
      }

      const response = await fetch('/api/super-admin-check', {
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to check super admin status')
      }

      return await response.json()
    },
    enabled: !!user && !authLoading,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  })

  return {
    isSuperAdmin: data?.isSuperAdmin || false,
    isLoading: authLoading || adminLoading,
    user: data?.user || null,
    error
  }
}
