import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCompany } from "@/contexts/company-context";
import { queryKeys } from '@/lib/query-config';

export interface UploadRewardImageData {
  rewardId: string;
  file: File;
}

export interface DeleteRewardImageData {
  rewardId: string;
}

/**
 * Hook to upload a reward image
 */
export function useUploadRewardImage() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ rewardId, file }: UploadRewardImageData) => {
      console.log('=== useUploadRewardImage mutationFn called ===')
      console.log('rewardId:', rewardId)
      console.log('file:', file)
      console.log('companyId:', companyId)

      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('rewardId', rewardId);
      formData.append('companyId', companyId);

      console.log('Making fetch request to /api/upload/reward-image')
      const response = await fetch('/api/upload/reward-image', {
        method: 'POST',
        body: formData,
      });

      console.log('Response status:', response.status)
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Upload failed:', errorData)
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const result = await response.json();
      console.log('Upload successful:', result)
      return result;
    },
    onSuccess: (data, variables) => {
      console.log('=== Image upload success, invalidating cache ===')
      console.log('Uploaded image data:', data)

      // Invalidate and refetch rewards list immediately
      queryClient.invalidateQueries({
        queryKey: queryKeys.rewards(companyId || ''),
        refetchType: 'all' // Force refetch of all queries, not just active ones
      });

      // Update the specific reward in cache if it exists
      queryClient.invalidateQueries({
        queryKey: ['reward', companyId || '', variables.rewardId],
        refetchType: 'all' // Force refetch of all queries
      });

      // Force a manual cache update with the new data
      if (data.reward) {
        queryClient.setQueryData(
          ['reward', companyId || '', variables.rewardId],
          data.reward
        );

        // Also update the rewards list cache with the updated reward
        queryClient.setQueriesData(
          { queryKey: queryKeys.rewards(companyId || '') },
          (oldData: unknown) => {
            const typedOldData = oldData as { data?: unknown[] };
            if (!typedOldData?.data) return oldData;
            return {
              ...typedOldData,
              data: typedOldData.data.map((reward: unknown) => {
                const typedReward = reward as { id: string };
                return typedReward.id === variables.rewardId ? data.reward : reward;
              })
            };
          }
        );
      }

      // Force a complete cache refresh after a short delay
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: queryKeys.rewards(companyId || ''),
          refetchType: 'all'
        });
      }, 1000);
    },
  });
}

/**
 * Hook to delete a reward image
 */
export function useDeleteRewardImage() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ rewardId }: DeleteRewardImageData) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch(
        `/api/upload/reward-image?rewardId=${rewardId}&companyId=${companyId}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete image');
      }

      return response.json();
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch rewards list
      queryClient.invalidateQueries({ queryKey: queryKeys.rewards(companyId || '') });
      // Update the specific reward in cache if it exists
      queryClient.invalidateQueries({ queryKey: ['reward', companyId || '', variables.rewardId] });
    },
  });
}
