const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function runMigration() {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    console.log('Running companies table migration...');

    // Use a simple approach - try to select from the columns and add if they don't exist
    const { data, error } = await supabase
      .from('companies')
      .select('id, email')
      .limit(1);

    if (error && error.message.includes('column "email" does not exist')) {
      console.log('Adding missing columns...');

      // Since direct ALTER TABLE might not work, we'll handle this in the API
      console.log('Columns need to be added via Supabase dashboard or direct SQL access');
      console.log('Required columns: email TEXT, phone TEXT, address TEXT, currency TEXT DEFAULT \'ETB\'');

    } else {
      console.log('✅ Companies table appears to have required columns');
    }

  } catch (error) {
    console.error('Migration check failed:', error);
  }
}

runMigration();
