// Script to add missing companies table columns for admin interface
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function addCompaniesColumns() {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    console.log('Adding missing columns to companies table...');

    // Add email column
    try {
      await supabase.rpc('exec_sql', {
        sql: 'ALTER TABLE companies ADD COLUMN IF NOT EXISTS email TEXT;'
      });
      console.log('✅ Added email column');
    } catch (err) {
      console.log('Email column might already exist');
    }

    // Add phone column
    try {
      await supabase.rpc('exec_sql', {
        sql: 'ALTER TABLE companies ADD COLUMN IF NOT EXISTS phone TEXT;'
      });
      console.log('✅ Added phone column');
    } catch (err) {
      console.log('Phone column might already exist');
    }

    // Add address column
    try {
      await supabase.rpc('exec_sql', {
        sql: 'ALTER TABLE companies ADD COLUMN IF NOT EXISTS address TEXT;'
      });
      console.log('✅ Added address column');
    } catch (err) {
      console.log('Address column might already exist');
    }

    // Add currency column
    try {
      await supabase.rpc('exec_sql', {
        sql: "ALTER TABLE companies ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'ETB';"
      });
      console.log('✅ Added currency column');
    } catch (err) {
      console.log('Currency column might already exist');
    }

    console.log('✅ Companies table column additions completed');

  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  }
}

addCompaniesColumns();
