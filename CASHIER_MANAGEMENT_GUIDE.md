# Cashier Management System Implementation Guide

## Overview

This document outlines the complete cashier management system for the Loyal loyalty platform, including invitation, registration, and authentication workflows. The system now supports **seamless cashier registration** with auto-account creation and temporary passwords for frictionless onboarding.

## System Architecture

### Database Tables

1. **`company_administrators`** - Main role assignment table
   - `administrator_id` (UUID) - References auth.users
   - `company_id` (UUID) - References companies
   - `role` (TEXT) - 'OWNER' or 'CASHIER'

2. **`cashier_invitations`** - Invitation tracking table
   - `company_id` (UUID) - Company issuing invitation
   - `email` (VARCHAR) - Invited person's email
   - `invitation_token` (VARCHAR) - Secure token for acceptance
   - `expires_at` (TIMESTAMP) - When invitation expires
   - `used_at` (TIMESTAMP) - When invitation was accepted

3. **`administrators`** - User profile information
   - `id` (UUID) - Matches auth.users.id
   - `email` (VARCHAR) - User's email
   - `name` (VARCHAR) - User's full name

### API Endpoints

#### Cashier Invitation Management

**POST `/api/cashiers/invite`** - Send cashier invitation
```json
{
  "email": "<EMAIL>",
  "companyId": "uuid-here"
}
```

**GET `/api/cashiers/invite?companyId=uuid`** - List pending invitations

**DELETE `/api/cashiers/invite/:id`** - Cancel invitation

#### Invitation Acceptance

**GET `/api/cashiers/accept?token=xxx`** - Get invitation details

**POST `/api/cashiers/accept`** - Accept invitation (manual registration)
```json
{
  "token": "invitation-token",
  "password": "secure-password",
  "name": "Full Name"
}
```

**POST `/api/cashiers/auto-create`** - Auto-create account with temporary password
```json
{
  "token": "invitation-token"
}
```

Response includes temporary password for seamless onboarding:
```json
{
  "success": true,
  "user": {
    "email": "<EMAIL>",
    "tempPassword": "TempPass123!",
    "role": "CASHIER",
    "companyName": "Company Name",
    "requiresPasswordChange": true
  }
}
```

#### Cashier Management

**GET `/api/cashiers?companyId=uuid`** - List company cashiers

**DELETE `/api/cashiers/:id`** - Remove cashier from company

## Implementation Steps

### 1. Database Setup

Run the migration file to create required tables:

```bash
# Execute the SQL migration
psql -f cashier-invitation-system.sql
```

### 2. Business Admin Workflow

**For Business Owners to Invite Cashiers:**

1. Navigate to Staff Management section
2. Enter cashier's email address
3. System generates secure invitation link
4. Share link with cashier (via email/messaging)

**Example Frontend Code:**
```typescript
const inviteCashier = async (email: string, companyId: string) => {
  const response = await fetch('/api/cashiers/invite', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ email, companyId })
  })

  if (response.ok) {
    const data = await response.json()
    // Share data.invitation.invitationLink with cashier
    console.log('Invitation Link:', data.invitation.invitationLink)
  }
}
```

### 3. Cashier Registration Workflow

**Two Registration Options Available:**

#### Option A: Seamless Auto-Registration (Recommended)
1. Receive invitation link from business owner
2. Click link to view invitation details
3. Click "Create Account Automatically" button
4. System generates secure temporary password
5. Account created instantly with CASHIER role
6. Login with email and temporary password
7. Forced to change password on first login

#### Option B: Manual Registration
1. Receive invitation link from business owner
2. Click link to view invitation details
3. Fill out registration form (name + password)
4. System assigns CASHIER role
5. Can immediately login with chosen credentials

**Example Frontend Code for Auto-Registration:**
```typescript
const autoCreateAccount = async (token: string) => {
  const response = await fetch('/api/cashiers/auto-create', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token })
  })

  if (response.ok) {
    const data = await response.json()
    // Display temporary password to user
    console.log('Temporary Password:', data.user.tempPassword)
    // Redirect to login
    window.location.href = '/login'
  }
}
```

**Example Frontend Code for Manual Registration:**
```typescript
const acceptInvitation = async (token: string, password: string, name: string) => {
  const response = await fetch('/api/cashiers/accept', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ token, password, name })
  })

  if (response.ok) {
    // Redirect to login
    window.location.href = '/login'
  }
}
```

### 4. Authentication & Permissions

**Role-Based Access Control:**

- **OWNER**: Full company access (existing functionality)
- **CASHIER**: Limited access for day-to-day operations

**Permission Checking:**
```typescript
import { getUserRole } from '@/lib/auth'

// In API routes or components
const userRole = await getUserRole(supabase, companyId)

if (userRole === 'OWNER') {
  // Full access
} else if (userRole === 'CASHIER') {
  // Limited access
} else {
  // No access
}
```

### 5. Cashier Capabilities

**What Cashiers CAN Do:**
- View member list and search members
- Add new members to the loyalty program
- Process transactions (earn/redeem points)
- Upload receipts
- View basic transaction history
- View available rewards
- Process reward redemptions

**What Cashiers CANNOT Do:**
- Modify company settings
- Create/edit rewards
- Create/edit tier definitions
- View financial reports
- Manage other staff members
- Access admin dashboard analytics

### 6. Frontend UI Considerations

**Staff Management Page (Owner Only):**
```typescript
// components/StaffManagement.tsx
export function StaffManagement() {
  const [cashiers, setCashiers] = useState([])
  const [invitations, setInvitations] = useState([])

  // Load cashiers and pending invitations
  // Show invitation form
  // Show remove cashier buttons
  // Show pending invitation status
}
```

**Login Page Enhancement:**
```typescript
// pages/auth/signin.tsx
// Add role context after successful login
// Redirect based on role (Owner → Dashboard, Cashier → Members)
```

**Navigation Menu (Role-Based):**
```typescript
// components/Navigation.tsx
if (userRole === 'OWNER') {
  // Show all menu items
} else if (userRole === 'CASHIER') {
  // Show limited menu: Members, Transactions, Rewards (view only)
}
```

## Security Considerations

1. **Invitation Tokens**:
   - Generated with cryptographically secure random bytes
   - Single-use tokens that expire after 7 days
   - Tokens are invalidated after use

2. **Role Validation**:
   - All API endpoints verify user role before granting access
   - RLS policies enforce data isolation by company

3. **Password Requirements**:
   - Manual registration: Minimum 8 characters
   - Auto-generated temporary passwords: 12 characters with complexity requirements
   - Temporary passwords include uppercase, lowercase, numbers, and special characters
   - Forced password change on first login for auto-created accounts

4. **Temporary Password Security**:
   - Generated using cryptographically secure random bytes
   - Excludes confusing characters (0, 1, I, O, l)
   - User metadata flag `password_change_required` enforces password change
   - Middleware redirects to change-password page until completed

5. **Audit Trail**:
   - Track who invited whom and when
   - Log role changes and access patterns
   - Monitor invitation acceptance and account creation

## Testing Scenarios

### Manual Testing Steps

1. **Invitation Flow:**
   - Owner logs in and invites cashier
   - Verify invitation record created
   - Verify invitation link works

2. **Acceptance Flow (Auto-Registration):**
   - Use invitation link
   - Click "Create Account Automatically"
   - Verify account created with temporary password
   - Login with temporary password
   - Verify redirected to change-password page
   - Change password successfully
   - Verify CASHIER role assigned

3. **Acceptance Flow (Manual Registration):**
   - Use invitation link
   - Fill registration form with name and password
   - Verify CASHIER role assigned
   - Verify can login successfully

3. **Permission Testing:**
   - Login as cashier
   - Verify limited access to features
   - Verify cannot access owner-only features

4. **Management Flow:**
   - Owner views cashier list
   - Owner removes cashier
   - Verify cashier loses access

## Production Deployment

1. **Environment Variables:**
   ```env
   NEXT_PUBLIC_BASE_URL=https://your-domain.com
   ```

2. **Email Service Integration:**
   - Replace manual link sharing with automated emails
   - Use services like SendGrid, SES, or Resend

3. **Monitoring:**
   - Track invitation acceptance rates
   - Monitor failed login attempts
   - Alert on suspicious role escalation attempts

## Current Status

✅ **Completed:**
- Database schema and migrations
- API endpoints for invitation management
- API endpoints for invitation acceptance (manual and auto-create)
- API endpoints for cashier management
- Role-based authentication utilities
- Seamless auto-registration with temporary passwords
- Password change enforcement middleware
- Secure temporary password generation
- Frontend UI for invitation acceptance with dual options
- Complete testing and validation of seamless flow

🔄 **Next Steps:**
- Integrate email service for automatic invitation sending
- Add comprehensive error handling and validation
- Create admin interface for cashier management
- Add audit logging for security compliance
- Enhance UI/UX for mobile devices

## Usage Example

```typescript
// 1. Business owner invites cashier
await fetch('/api/cashiers/invite', {
  method: 'POST',
  body: JSON.stringify({
    email: '<EMAIL>',
    companyId: 'company-uuid'
  })
})

// 2A. Seamless auto-registration (recommended)
const autoResponse = await fetch('/api/cashiers/auto-create', {
  method: 'POST',
  body: JSON.stringify({
    token: 'invitation-token'
  })
})
const { user } = await autoResponse.json()
// user.tempPassword contains the temporary password
// user.requiresPasswordChange = true

// 2B. Manual registration (alternative)
await fetch('/api/cashiers/accept', {
  method: 'POST',
  body: JSON.stringify({
    token: 'invitation-token',
    password: 'secure123',
    name: 'John Smith'
  })
})

// 3. Cashier can now login and access limited features
const role = await getUserRole(supabase, companyId)
if (role === 'CASHIER') {
  // Show cashier interface
}
```

This system provides a complete, secure, and scalable solution for managing cashier access to the Loyal loyalty platform.
