{"name": "src", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cypress": "cypress open", "cypress:headless": "cypress run", "e2e": "start-server-and-test dev http://localhost:3000 cypress", "e2e:headless": "start-server-and-test dev http://localhost:3000 cypress:headless"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.9", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.24", "@ai-sdk/react": "^2.0.28", "@hookform/resolvers": "^5.0.1", "@playwright/test": "^1.55.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/ssr": "^0.6.1", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.74.4", "@tanstack/react-query-devtools": "^5.81.2", "@tanstack/react-table": "^8.21.3", "@types/node-telegram-bot-api": "^0.64.9", "@types/qrcode": "^1.5.5", "ai": "^4.3.19", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "framer-motion": "^12.9.1", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "node-telegram-bot-api": "^0.66.0", "qrcode": "^1.5.4", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.7.0", "@modelcontextprotocol/server-postgres": "^0.6.2", "@next/bundle-analyzer": "^15.3.1", "@supabase/mcp-server-supabase": "^0.4.1", "@supabase/supabase-js": "^2.49.4", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^15.0.0", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vercel/sdk": "^1.6.5", "autoprefixer": "^10.4.16", "cypress": "^14.3.2", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-config-prettier": "^10.1.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^1.3.5", "next-router-mock": "^0.9.12", "node-mocks-http": "^1.14.1", "postcss": "^8.4.31", "prettier": "^3.5.3", "shadcn-ui": "^0.9.5", "start-server-and-test": "^1.15.4", "tailwindcss": "^3.3.5", "typescript": "^5", "undici": "^6.10.1", "uuid": "^11.1.0", "vercel": "^41.7.0", "zod": "^3.25.76"}}