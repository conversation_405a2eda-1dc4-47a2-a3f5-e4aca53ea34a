// Telegram Bot API utilities
const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN!
const TELEGRAM_API_BASE = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}`

export interface TelegramUser {
  id: number
  is_bot: boolean
  first_name: string
  last_name?: string
  username?: string
  language_code?: string
}

export interface TelegramMessage {
  message_id: number
  from?: TelegramUser
  chat: {
    id: number
    type: string
  }
  date: number
  text?: string
}

export interface TelegramUpdate {
  update_id: number
  message?: TelegramMessage
}

export async function sendTelegramMessage(
  chatId: number | string,
  text: string,
  options: {
    parse_mode?: 'HTML' | 'Markdown'
    reply_markup?: Record<string, unknown>
  } = {}
): Promise<boolean> {
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/sendMessage`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId,
        text,
        ...options
      })
    })

    const result = await response.json()
    return result.ok
  } catch (error) {
    console.error('Failed to send Telegram message:', error)
    return false
  }
}

export async function sendTelegramPhoto(
  chatId: number | string,
  photo: string,
  caption?: string
): Promise<boolean> {
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/sendPhoto`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        chat_id: chatId,
        photo,
        caption
      })
    })

    const result = await response.json()
    return result.ok
  } catch (error) {
    console.error('Failed to send Telegram photo:', error)
    return false
  }
}

export async function setTelegramWebhook(webhookUrl: string): Promise<boolean> {
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/setWebhook`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message']
      })
    })

    const result = await response.json()
    return result.ok
  } catch (error) {
    console.error('Failed to set Telegram webhook:', error)
    return false
  }
}

export async function deleteTelegramWebhook(): Promise<boolean> {
  try {
    const response = await fetch(`${TELEGRAM_API_BASE}/deleteWebhook`, {
      method: 'POST'
    })

    const result = await response.json()
    return result.ok
  } catch (error) {
    console.error('Failed to delete Telegram webhook:', error)
    return false
  }
}

export async function setTelegramBotMenu(): Promise<boolean> {
  try {
    const commands = [
      { command: 'start', description: 'Initialize bot and link account' },
      { command: 'link', description: 'Get account linking instructions' },
      { command: 'balance', description: 'Check your points balance' },
      { command: 'tier', description: 'View your tier status and benefits' },
      { command: 'rewards', description: 'Browse available rewards' },
      { command: 'history', description: 'View transaction history' },
      { command: 'profile', description: 'View your profile information' },
      { command: 'settings', description: 'Bot preferences' },
      { command: 'help', description: 'Show help message' },
      { command: 'unlink', description: 'Unlink your account' }
    ]

    const response = await fetch(`${TELEGRAM_API_BASE}/setMyCommands`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        commands
      })
    })

    const result = await response.json()
    console.log('Bot menu setup result:', result)
    return result.ok
  } catch (error) {
    console.error('Failed to set Telegram bot menu:', error)
    return false
  }
}
