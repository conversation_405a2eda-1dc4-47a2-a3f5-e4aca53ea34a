/**
 * Utility for generating secure temporary passwords for auto-created accounts
 */

import { randomBytes } from 'crypto';

/**
 * Generates a secure temporary password with specified complexity
 * @param length Length of the password (default: 10)
 * @returns A secure temporary password
 */
export function generateTemporaryPassword(length: number = 10): string {
  // Define character sets
  const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ'; // Removed confusing I and O
  const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz'; // Removed confusing l
  const numberChars = '********'; // Removed confusing 0 and 1
  const specialChars = '@#$%&*!?';
  
  // Combine all character sets
  const allChars = uppercaseChars + lowercaseChars + numberChars + specialChars;
  
  // Generate random bytes
  const randomBytesBuffer = randomBytes(length * 2); // Generate more bytes than needed
  
  let password = '';
  
  // Ensure at least one character from each set
  password += uppercaseChars.charAt(Math.floor(Math.random() * uppercaseChars.length));
  password += lowercaseChars.charAt(Math.floor(Math.random() * lowercaseChars.length));
  password += numberChars.charAt(Math.floor(Math.random() * numberChars.length));
  password += specialChars.charAt(Math.floor(Math.random() * specialChars.length));
  
  // Fill the rest with random characters
  for (let i = 4; i < length; i++) {
    const randomIndex = randomBytesBuffer[i] % allChars.length;
    password += allChars.charAt(randomIndex);
  }
  
  // Shuffle the password to avoid predictable pattern
  return shuffleString(password);
}

/**
 * Shuffles a string using Fisher-Yates algorithm
 * @param str String to shuffle
 * @returns Shuffled string
 */
function shuffleString(str: string): string {
  const arr = str.split('');
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.join('');
}
