/**
 * Centralized React Query configuration
 */

export const CACHE_TIMES = {
  // Static data that rarely changes
  STATIC: 1000 * 60 * 30,    // 30 minutes (company info, tiers)

  // Normal data that changes occasionally
  NORMAL: 1000 * 60 * 5,     // 5 minutes (members, rewards)
  MEDIUM: 1000 * 60 * 5,     // 5 minutes (alias for NORMAL)

  // Dynamic data that changes frequently
  DYNAMIC: 1000 * 60 * 1,    // 1 minute (transactions, redemptions)
  SHORT: 1000 * 60 * 1,      // 1 minute (alias for DYNAMIC)

  // Real-time data
  REALTIME: 1000 * 30,       // 30 seconds (dashboard metrics)
} as const;

export const QUERY_KEYS = {
  COMPANY: 'company',
  COMPANY_ADMIN: 'companyAdmin',
  MEMBERS: 'members',
  MEMBER_REDEMPTIONS: 'memberRedemptions',
  REWARDS: 'rewards',
  TIERS: 'tiers',
  TOP_MEMBERS: 'topMembers',
  ANALYTICS: 'analytics',
  MEMBER_ANALYTICS: 'memberAnalytics',
  BUSINESS_ANALYTICS: 'businessAnalytics',
  TRANSACTIONS: 'transactions',
  RECEIPTS: 'receipts',
  BUSINESS_ITEMS: 'businessItems',
  TEMPLATES: 'templates'
} as const;

export const queryKeys = {
  // Company-related queries
  company: (companyId: string) => ['company', companyId] as const,
  companyAdmin: (userId: string) => ['company', 'admin', userId] as const,

  // Member-related queries
  members: (companyId: string, limit?: number) => ['members', companyId, ...(limit ? [limit] : [])] as const,
  member: (companyId: string, memberId: string) => ['member', companyId, memberId] as const,
  memberStats: (memberId: string) => ['member', 'stats', memberId] as const,
  memberRedemptions: (memberId: string, companyId: string) => ['member', 'redemptions', memberId, companyId] as const,

  // Transaction-related queries
  transactions: (companyId: string, limit?: number, dateRange?: { from?: Date; to?: Date }) => ['transactions', companyId, ...(limit ? [limit] : []), ...(dateRange ? [JSON.stringify(dateRange)] : [])] as const,
  transaction: (transactionId: string) => ['transaction', transactionId] as const,
  memberTransactions: (memberId: string) => ['transactions', 'member', memberId] as const,

  // Reward-related queries
  rewards: (companyId: string) => ['rewards', companyId] as const,
  reward: (rewardId: string) => ['reward', rewardId] as const,
  redemptions: (companyId: string, limit?: number) => ['redemptions', companyId, ...(limit ? [limit] : [])] as const,
  redemption: (redemptionId: string) => ['redemption', redemptionId] as const,

  // Tier-related queries
  tiers: (companyId: string) => ['tiers', companyId] as const,
  tier: (tierId: string) => ['tier', tierId] as const,

  // Analytics and reports
  analytics: (companyId: string) => ['analytics', companyId] as const,
  businessPurchaseAnalytics: (companyId: string) => ['business', 'purchase-analytics', companyId] as const,
  topMembers: (companyId: string) => ['top-members', companyId] as const,

  // Marketing campaigns
  campaigns: (companyId: string) => ['campaigns', companyId] as const,
  campaign: (campaignId: string) => ['campaign', campaignId] as const,

  // Onboarding
  onboardingStatus: (userId: string) => ['onboarding-status', userId] as const,

  // Staff
  staff: (companyId: string) => ['staff', companyId] as const,

  // Dashboard metrics
  pointsData: (companyId: string, timeRange?: string) => ['pointsData', companyId, ...(timeRange ? [timeRange] : [])] as const,
  activeMembers: (companyId: string, days?: number) => ['activeMembers', companyId, ...(days ? [days] : [])] as const,
  membersCount: (companyId: string) => ['membersCount', companyId] as const,

  // Telegram Integration
  telegramStatus: (memberId: string, companyId?: string) => ['telegramStatus', memberId, ...(companyId ? [companyId] : [])] as const,
  telegramMembers: (companyId: string) => ['membersWithTelegram', companyId] as const,
  telegramNotifications: (companyId: string, limit?: number) => ['telegramNotifications', companyId, ...(limit ? [limit] : [])] as const,
} as const;

export const COMMON_QUERY_OPTIONS = {
  refetchOnWindowFocus: false,
  refetchOnReconnect: false,
  retry: (failureCount: number, error: unknown) => {
    // Don't retry on 4xx errors
    const errorStatus = (error as { status?: number })?.status;
    if (errorStatus && errorStatus >= 400 && errorStatus < 500) return false;
    return failureCount < 2;
  },
} as const;
