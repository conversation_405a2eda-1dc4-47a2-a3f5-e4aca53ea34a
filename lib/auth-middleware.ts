import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Middleware to check if user requires password change
 * Redirects to change-password page if needed
 */
export async function checkPasswordChangeRequired(request: NextRequest) {
  const supabase = await createClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return null // Not authenticated, let other middleware handle
    }

    // Check if password change is required
    const passwordChangeRequired = user.user_metadata?.password_change_required
    
    if (passwordChangeRequired && !request.nextUrl.pathname.startsWith('/change-password')) {
      // Redirect to change password page
      const changePasswordUrl = new URL('/change-password', request.url)
      return NextResponse.redirect(changePasswordUrl)
    }

    // If on change-password page but no longer required, redirect to dashboard
    if (!passwordChangeRequired && request.nextUrl.pathname === '/change-password') {
      const dashboardUrl = new URL('/dashboard', request.url)
      return NextResponse.redirect(dashboardUrl)
    }

    return null // No redirect needed
  } catch (error) {
    console.error('Error checking password change requirement:', error)
    return null
  }
}

/**
 * Check if user has completed onboarding and password setup
 */
export async function isUserFullyOnboarded(userId: string): Promise<boolean> {
  const supabase = await createClient()
  
  try {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user || user.id !== userId) {
      return false
    }

    // Check if password change is still required
    const passwordChangeRequired = user.user_metadata?.password_change_required
    
    return !passwordChangeRequired
  } catch (error) {
    console.error('Error checking user onboarding status:', error)
    return false
  }
}
