import { format, toZonedTime } from 'date-fns-tz'

// East African Time (EAT) timezone
const EAST_AFRICAN_TIMEZONE = 'Africa/Nairobi'

/**
 * Format a date string or Date object to East African Time
 * @param date - Date string or Date object
 * @param formatString - Format string (default: 'PPp' for full date and time)
 * @returns Formatted date string in East African Time
 */
export function formatToEastAfricanTime(
  date: string | Date | null | undefined,
  formatString: string = 'PPp'
): string {
  // Handle null or undefined dates
  if (date === null || date === undefined) {
    console.error('Null or undefined date value')
    return 'Invalid Date'
  }
  
  const dateObj = typeof date === 'string' ? new Date(date) : date

  // Check if the date is valid
  try {
    if (isNaN(dateObj.getTime())) {
      console.error('Invalid date value:', date)
      return 'Invalid Date'
    }
  } catch (error) {
    console.error('Error processing date:', error, date)
    return 'Invalid Date'
  }

  const zonedDate = toZonedTime(dateObj, EAST_AFRICAN_TIMEZONE)
  return format(zonedDate, formatString, { timeZone: EAST_AFRICAN_TIMEZONE })
}

/**
 * Get current time in East African Time
 * @returns Current date in East African Time
 */
export function getCurrentEastAfricanTime(): Date {
  return toZonedTime(new Date(), EAST_AFRICAN_TIMEZONE)
}

/**
 * Format a date to East African Time with custom format
 * @param date - Date string or Date object
 * @param formatString - Custom format string
 * @returns Formatted date string
 */
export function formatEAT(date: string | Date | null | undefined, formatString: string): string {
  return formatToEastAfricanTime(date, formatString)
}
