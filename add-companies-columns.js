// Script to add missing columns to companies table
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
require('dotenv').config({ path: '.env.local' });

async function addCompaniesColumns() {
  try {
    // Use service role key for schema changes
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    console.log('Adding missing columns to companies table...');

    // Read and execute the SQL migration
    const migration = fs.readFileSync('./add-companies-columns.sql', 'utf8');

    const { error } = await supabase.rpc('exec_sql', { sql: migration });

    if (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }

    console.log('✅ Successfully added missing columns to companies table');

  } catch (error) {
    console.error('<PERSON><PERSON><PERSON> failed:', error);
    process.exit(1);
  }
}

addCompaniesColumns();
