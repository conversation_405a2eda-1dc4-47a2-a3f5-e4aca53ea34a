// Script <NAME_EMAIL> a super admin
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function makeSuperAdmin() {
  try {
    // Use service role key for admin operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    const email = '<EMAIL>';
    const userId = '0557e2e2-75dd-4b23-a6fb-ad5ac0211b00';

    console.log(`Making ${email} a super admin...`);

    // Update the user in auth.users table
    const { data, error } = await supabase.auth.admin.updateUserById(userId, {
      app_metadata: { is_super_admin: true }
    });

    if (error) {
      console.error('Error updating user:', error);
      process.exit(1);
    }

    console.log('✅ User successfully made super admin:', data);

    // Also update the users table if it has is_super_admin column
    const { error: updateError } = await supabase
      .from('users')
      .update({ is_super_admin: true })
      .eq('email', email);

    if (updateError) {
      console.log('Note: Could not update users table (might not exist):', updateError.message);
    } else {
      console.log('✅ Also updated users table');
    }

  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  }
}

makeSuperAdmin();
