#!/usr/bin/env node

/**
 * Test script to verify that member creation with initial points works correctly
 * This script will create a test member and verify that the initial points are saved
 */

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testMemberCreation() {
  try {
    console.log('🧪 Testing member creation with initial points...\n');

    // Get a company to test with
    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(1);

    if (companyError || !companies || companies.length === 0) {
      throw new Error('No companies found');
    }

    const company = companies[0];
    console.log(`📊 Using company: ${company.name} (${company.id})`);

    // Generate test member data
    const testMember = {
      name: `Test Member ${Date.now()}`,
      phone_number: `+1555${Math.floor(Math.random() * 1000000).toString().padStart(6, '0')}`,
      email: `test${Date.now()}@example.com`,
      birthday: '1990-01-01',
      birthday_month_day: '01-01',
      loyalty_id: `F${Math.floor(Math.random() * 10000000).toString().padStart(7, '0')}`,
      company_id: company.id,
      lifetime_points: 0,
      redeemed_points: 0,
      expired_points: 0,
      registration_date: new Date().toISOString(),
    };

    console.log(`👤 Creating test member: ${testMember.name}`);

    // Create the member
    const { data: newMember, error: memberError } = await supabase
      .from('loyalty_members')
      .insert(testMember)
      .select()
      .single();

    if (memberError) {
      throw new Error(`Failed to create member: ${memberError.message}`);
    }

    console.log(`✅ Member created with ID: ${newMember.id}`);

    // Add initial points using the same logic as the API
    const initialPoints = 100;
    console.log(`💰 Adding ${initialPoints} initial points...`);

    const expirationDate = new Date();
    expirationDate.setFullYear(expirationDate.getFullYear() + 1);

    const { data: transaction, error: transactionError } = await supabase
      .from('points_transactions')
      .insert({
        member_id: newMember.id,
        company_id: company.id,
        transaction_type: 'EARN',
        points_change: initialPoints,
        transaction_date: new Date().toISOString(),
        description: 'Initial points (test)',
        expiration_date: expirationDate.toISOString().split('T')[0],
      })
      .select()
      .single();

    if (transactionError) {
      throw new Error(`Failed to create transaction: ${transactionError.message}`);
    }

    console.log(`✅ Transaction created with ID: ${transaction.id}`);

    // Wait a moment for triggers to fire
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if the member's points were updated
    const { data: updatedMember, error: fetchError } = await supabase
      .from('loyalty_members')
      .select('lifetime_points, redeemed_points, expired_points')
      .eq('id', newMember.id)
      .single();

    if (fetchError) {
      throw new Error(`Failed to fetch updated member: ${fetchError.message}`);
    }

    console.log('\n📊 Results:');
    console.log(`   Lifetime Points: ${updatedMember.lifetime_points}`);
    console.log(`   Redeemed Points: ${updatedMember.redeemed_points}`);
    console.log(`   Expired Points: ${updatedMember.expired_points}`);
    console.log(`   Available Points: ${updatedMember.lifetime_points - updatedMember.redeemed_points - updatedMember.expired_points}`);

    // Verify the results
    if (updatedMember.lifetime_points === initialPoints) {
      console.log('\n🎉 SUCCESS: Initial points were correctly saved!');
    } else {
      console.log('\n❌ FAILURE: Initial points were not saved correctly');
      console.log(`   Expected: ${initialPoints}, Got: ${updatedMember.lifetime_points}`);
    }

    // Clean up - delete the test member
    console.log('\n🧹 Cleaning up test data...');

    await supabase
      .from('points_transactions')
      .delete()
      .eq('member_id', newMember.id);

    await supabase
      .from('loyalty_members')
      .delete()
      .eq('id', newMember.id);

    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testMemberCreation();
