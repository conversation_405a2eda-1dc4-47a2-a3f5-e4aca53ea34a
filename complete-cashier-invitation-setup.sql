-- COMPLETE CASHIER INVITATION SYSTEM SETUP
-- Run this file to create the complete cashier invitation system with Telegram support
-- This combines both the table creation and Telegram enhancement

-- ===================================================================
-- STEP 1: Create the base cashier invitation system
-- ===================================================================

-- Create cashier_invitations table
CREATE TABLE IF NOT EXISTS cashier_invitations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  invited_by UUID NOT NULL, -- administrator_id who sent the invitation
  email VARCHAR(255) NOT NULL,
  invitation_token VARCHAR(255) NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  used_at TIMESTAMP WITH TIME ZONE NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure one active invitation per email per company
  UNIQUE(company_id, email)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_cashier_invitations_token
ON cashier_invitations(invitation_token);

CREATE INDEX IF NOT EXISTS idx_cashier_invitations_company_email
ON cashier_invitations(company_id, email);

-- Enable RLS
ALTER TABLE cashier_invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for cashier_invitations
CREATE POLICY "Company admins can manage invitations for their company"
ON cashier_invitations
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM company_administrators ca
    WHERE ca.company_id = cashier_invitations.company_id
    AND ca.administrator_id = auth.uid()
    AND ca.role = 'OWNER'
  )
);

-- Create function to cleanup expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM cashier_invitations
  WHERE expires_at < NOW()
  AND used_at IS NULL;
END;
$$;

-- Create function to generate secure invitation tokens
CREATE OR REPLACE FUNCTION generate_invitation_token()
RETURNS text
LANGUAGE plpgsql
AS $$
DECLARE
  token text;
  exists_count integer;
BEGIN
  LOOP
    -- Generate a secure random token
    token := encode(gen_random_bytes(32), 'base64');

    -- Check if token already exists
    SELECT COUNT(*) INTO exists_count
    FROM cashier_invitations
    WHERE invitation_token = token;

    -- Exit loop if token is unique
    EXIT WHEN exists_count = 0;
  END LOOP;

  RETURN token;
END;
$$;

-- ===================================================================
-- STEP 2: Add Telegram support to cashier invitations
-- ===================================================================

-- Add new columns to cashier_invitations table for Telegram support
ALTER TABLE cashier_invitations
ADD COLUMN IF NOT EXISTS telegram_sent_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS telegram_chat_id VARCHAR(255);

-- Create index for efficient queries on telegram delivery status
CREATE INDEX IF NOT EXISTS idx_cashier_invitations_telegram_sent
ON cashier_invitations(telegram_sent_at)
WHERE telegram_sent_at IS NOT NULL;

-- ===================================================================
-- STEP 3: Add documentation comments
-- ===================================================================

-- Add comments for documentation
COMMENT ON TABLE cashier_invitations IS 'Stores invitations sent to potential cashiers by business administrators with Telegram support';
COMMENT ON COLUMN cashier_invitations.invitation_token IS 'Secure token used to complete the invitation process';
COMMENT ON COLUMN cashier_invitations.expires_at IS 'When the invitation expires (typically 7 days from creation)';
COMMENT ON COLUMN cashier_invitations.used_at IS 'When the invitation was accepted (NULL if unused)';
COMMENT ON COLUMN cashier_invitations.telegram_sent_at IS 'Timestamp when invitation was sent via Telegram';
COMMENT ON COLUMN cashier_invitations.telegram_chat_id IS 'Telegram Chat ID of the invitation recipient';

-- ===================================================================
-- VERIFICATION: Check that everything was created successfully
-- ===================================================================

-- Verify table exists with all columns
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'cashier_invitations') THEN
        RAISE NOTICE '✅ cashier_invitations table created successfully';
    ELSE
        RAISE EXCEPTION '❌ Failed to create cashier_invitations table';
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'cashier_invitations' AND column_name = 'telegram_sent_at') THEN
        RAISE NOTICE '✅ Telegram columns added successfully';
    ELSE
        RAISE EXCEPTION '❌ Failed to add Telegram columns';
    END IF;

    RAISE NOTICE '🎉 Cashier invitation system with Telegram support is ready!';
END;
$$;
