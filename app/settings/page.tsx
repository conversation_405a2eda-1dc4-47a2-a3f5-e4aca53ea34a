'use client'

import { useState, useEffect } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useUserRole } from '@/hooks/use-user-role'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'
import { CashierManagement } from '@/components/cashier-management'
import TemplateManagement from '@/components/settings/template-management'

export default function SettingsPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const userRole = useUserRole()

  console.log('Settings Page - Debug Info:', {
    userId: user?.id,
    companyId: company?.id,
    role: userRole.role,
    isOwner: userRole.isOwner,
  })
  const [activeTab, setActiveTab] = useState('general')
  const router = useRouter()

  // Redirect cashiers away from settings page
  useEffect(() => {
    if (!authLoading && !companyLoading && userRole.isCashier) {
      toast.error('Access denied. Cashiers cannot access settings.')
      router.push('/dashboard')
      return
    }
  }, [authLoading, companyLoading, userRole.isCashier, router])

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login')
    }
  }, [authLoading, user, router])

  // Loading state
  const isLoading = authLoading || companyLoading

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        <div className="flex flex-col gap-4">
          <div className="animate-shimmer w-48 h-8 rounded-lg"></div>
          <div className="animate-shimmer w-full h-64 rounded-lg"></div>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect in the effect
  }

  const handleSaveGeneral = () => {
    toast.success('General settings saved successfully')
  }

  const handleSaveNotifications = () => {
    toast.success('Notification settings saved successfully')
  }

  const handleSaveAPI = () => {
    toast.success('API settings saved successfully')
  }

  const handleSetupTelegramMenu = async () => {
    try {
      toast.info('Setting up Telegram bot menu...', { duration: 2000 })

      const response = await fetch('/api/telegram/setup-menu', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ adminSetup: true })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success('✅ Telegram bot menu configured successfully!', {
          description: 'All users will now see the menu when they open the bot chat.',
          duration: 5000
        })
      } else {
        toast.error('Failed to setup bot menu: ' + (result.error || 'Unknown error'))
      }
    } catch (error) {
      toast.error('Error setting up bot menu: ' + (error as Error).message)
    }
  }

  return (
    <div className="container mx-auto px-4 py-6 max-w-7xl">
      <div className="flex flex-col gap-6">
        <h1 className="text-3xl font-bold">Settings</h1>

        <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-5 md:w-[600px] mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="staff">Staff</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="api">API</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>
                  Manage your company profile and general settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="companyName">Company Name</Label>
                      <Input
                        id="companyName"
                        defaultValue={company?.name || ''}
                        placeholder="Your Company Name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contactEmail">Contact Email</Label>
                      <Input
                        id="contactEmail"
                        type="email"
                        defaultValue={company?.contact_email || ''}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <select
                        id="timezone"
                        className="w-full p-2 border rounded-md"
                        defaultValue="UTC"
                      >
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="currency">Currency</Label>
                      <select
                        id="currency"
                        className="w-full p-2 border rounded-md"
                        defaultValue="USD"
                      >
                        <option value="USD">USD ($)</option>
                        <option value="EUR">EUR (€)</option>
                        <option value="GBP">GBP (£)</option>
                        <option value="CAD">CAD ($)</option>
                        <option value="AUD">AUD ($)</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="address">Business Address</Label>
                    <textarea
                      id="address"
                      className="w-full p-2 border rounded-md min-h-[100px]"
                      defaultValue={company?.address || ''}
                      placeholder="123 Business St, City, State, ZIP"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleSaveGeneral}>Save Changes</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure how and when you receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Email Notifications</h3>
                      <p className="text-sm text-muted-foreground">Receive notifications via email</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">New Member Alerts</h3>
                      <p className="text-sm text-muted-foreground">Get notified when new members join</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Reward Redemption Alerts</h3>
                      <p className="text-sm text-muted-foreground">Get notified when rewards are redeemed</p>
                    </div>
                    <Switch defaultChecked />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Daily Summary</h3>
                      <p className="text-sm text-muted-foreground">Receive a daily summary of activity</p>
                    </div>
                    <Switch />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Weekly Reports</h3>
                      <p className="text-sm text-muted-foreground">Receive weekly performance reports</p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleSaveNotifications}>Save Changes</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="staff">
            <Card>
              <CardHeader>
                <CardTitle>Staff Management</CardTitle>
                <CardDescription>
                  Manage staff members and their roles
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CashierManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <TemplateManagement />
          </TabsContent>

          <TabsContent value="api">
            <Card>
              <CardHeader>
                <CardTitle>API Settings</CardTitle>
                <CardDescription>
                  Manage your API keys and integration settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="p-4 border rounded-md bg-muted">
                    <h3 className="font-medium mb-2">Your API Key</h3>
                    <div className="flex items-center gap-2">
                      <Input
                        type="password"
                        value="••••••••••••••••••••••••••••••"
                        readOnly
                      />
                      <Button variant="outline" onClick={() => toast.success('API key copied to clipboard')}>
                        Copy
                      </Button>
                      <Button variant="outline" onClick={() => toast.success('New API key generated')}>
                        Regenerate
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground mt-2">
                      Keep this key secret. It provides full access to your account.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="webhookUrl">Webhook URL</Label>
                    <Input
                      id="webhookUrl"
                      placeholder="https://your-server.com/webhook"
                    />
                    <p className="text-sm text-muted-foreground">
                      We&apos;ll send events to this URL
                    </p>
                  </div>

                  <div className="p-4 border rounded-md bg-blue-50">
                    <h3 className="font-medium mb-2">🤖 Telegram Bot Menu</h3>
                    <div className="space-y-3">
                      <p className="text-sm text-muted-foreground">
                        Setup the command menu for your Telegram bot. This will add a menu button (☰) that users can tap to see all available commands.
                      </p>
                      <div className="flex items-center gap-3">
                        <Button
                          onClick={handleSetupTelegramMenu}
                          variant="outline"
                          className="flex items-center gap-2"
                        >
                          <span>📱</span>
                          Setup Bot Menu
                        </Button>
                        <div className="text-xs text-muted-foreground">
                          <div>✅ Works for all users (new & existing)</div>
                          <div>🔄 Users may need to restart Telegram to see changes</div>
                        </div>
                      </div>
                      <details className="text-xs text-muted-foreground">
                        <summary className="cursor-pointer font-medium">Available Commands</summary>
                        <div className="mt-2 pl-4 space-y-1">
                          <div>/start - Initialize bot and link account</div>
                          <div>/balance - Check points balance</div>
                          <div>/rewards - Browse available rewards</div>
                          <div>/history - View transaction history</div>
                          <div>/profile - View profile information</div>
                          <div>/help - Show help message</div>
                          <div>/unlink - Unlink account</div>
                        </div>
                      </details>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h3 className="font-medium">Webhook Events</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="event-member-created" defaultChecked />
                        <Label htmlFor="event-member-created">Member Created</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="event-points-earned" defaultChecked />
                        <Label htmlFor="event-points-earned">Points Earned</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="event-reward-redeemed" defaultChecked />
                        <Label htmlFor="event-reward-redeemed">Reward Redeemed</Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <input type="checkbox" id="event-tier-changed" defaultChecked />
                        <Label htmlFor="event-tier-changed">Tier Changed</Label>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button onClick={handleSaveAPI}>Save Changes</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
