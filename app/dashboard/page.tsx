'use client'

import { useEffect, useState, Suspense } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { useOnboardingStatus } from '@/hooks/use-onboarding'
import { useUserRole } from '@/hooks/use-user-role'
import { useCompanyAdminQuery } from '@/hooks/use-company-admin-query'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import MainLayout from '@/components/layout/MainLayout'
import { ArrowUpRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import dynamic from 'next/dynamic'
import { LoadingSpinner, LoadingOverlay } from '@/components/ui/loading-spinner'
import WelcomeState from './components/welcome-state'
import DemoData from './components/demo-data'
import SetupWizard from './components/setup-wizard'
import { TopMembersSkeleton } from './components/top-members'

// Import skeleton components
const KpiCardsSkeleton = () => (
  <div className="grid gap-5 md:grid-cols-2 lg:grid-cols-4">
    {[1, 2, 3, 4].map((i) => (
      <Card key={i} className="premium-card">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </CardHeader>
        <CardContent className="pt-6">
          <Skeleton className="h-8 w-24 mb-2" />
          <Skeleton className="h-4 w-full" />
        </CardContent>
        <CardFooter className="pt-0">
          <Skeleton className="h-8 w-full" />
        </CardFooter>
      </Card>
    ))}
  </div>
)

const PointsChartSkeleton = () => (
  <Card>
    <CardHeader>
      <CardTitle><Skeleton className="h-6 w-32" /></CardTitle>
      <CardDescription><Skeleton className="h-4 w-48" /></CardDescription>
    </CardHeader>
    <CardContent className="p-0 h-[350px]">
      <div className="p-4 space-y-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-start gap-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>
        ))}
      </div>
    </CardContent>
    <CardFooter>
      <Skeleton className="h-9 w-full" />
    </CardFooter>
  </Card>
)

const ActivityFeedSkeleton = () => (
  <Card>
    <CardHeader>
      <CardTitle><Skeleton className="h-6 w-32" /></CardTitle>
      <CardDescription><Skeleton className="h-4 w-48" /></CardDescription>
    </CardHeader>
    <CardContent className="p-0 h-[350px]">
      <div className="p-4 space-y-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="flex items-start gap-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
            </div>
          </div>
        ))}
      </div>
    </CardContent>
    <CardFooter>
      <Skeleton className="h-9 w-full" />
    </CardFooter>
  </Card>
)

// Dynamically import components with loading states to improve initial load performance
const KpiCards = dynamic(() => import('./components/kpi-cards'), {
  loading: () => <KpiCardsSkeleton />,
  ssr: false
})

const PointsOverTimeChart = dynamic(() => import('./components/points-chart'), {
  loading: () => <PointsChartSkeleton />,
  ssr: false
})

const MemberActivityFeed = dynamic(() => import('./components/activity-feed'), {
  loading: () => <ActivityFeedSkeleton />,
  ssr: false
})

// Optimize Business Stats with code splitting
const BusinessStats = dynamic(() => import('./components/business-stats'), {
  loading: () => <LoadingSpinner />,
  ssr: false
})

// Business Metrics Section
const BusinessMetricsSection = dynamic(() => import('./components/business-metrics-section'), {
  loading: () => <LoadingSpinner />,
  ssr: false
})

const QuickActions = dynamic(() => import('./components/quick-actions'), {
  ssr: false
})

// Top Members component
const TopMembers = dynamic(() => import('./components/top-members'), {
  loading: () => <TopMembersSkeleton />,
  ssr: false
})

// Business Purchase Analytics component with dashboard configuration
const ConfigurableDashboard = dynamic(() => import('@/components/dashboard/configurable-dashboard'), {
  loading: () => <LoadingSpinner />,
  ssr: false
})

export default function Page() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const { data: onboardingData, isLoading: onboardingLoading } = useOnboardingStatus(user?.id, authLoading)
  const { isCashier } = useUserRole()
  const { isLoading: roleLoading } = useCompanyAdminQuery()
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)
  const [activeTimeRange, setActiveTimeRange] = useState('7d')
  const [showSetupWizard, setShowSetupWizard] = useState(false)

  // Use company information from context
  const businessName = company?.name || 'Your Business'

  // Check if this is a new company that should show welcome state
  const isNewCompany = !onboardingData?.hasCompany || (!onboardingData?.isOnboardingComplete && onboardingData?.hasCompany)
  const showWelcomeState = isNewCompany && !onboardingData?.isOnboardingComplete

  // Ensure client-side only rendering for date formatting
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Redirect cashiers to transactions page
  useEffect(() => {
    if (isClient && !roleLoading && isCashier) {
      router.replace('/transactions')
    }
  }, [isClient, roleLoading, isCashier, router])

  // Redirect if not authenticated *after* loading is complete
  useEffect(() => {
    if (isClient && !authLoading && !user) {
      // Add a safety check to prevent infinite redirects - use sessionStorage for consistency
      const redirectAttempts = parseInt(sessionStorage.getItem('loginRedirectAttempts') || '0');

      if (redirectAttempts < 3) {
        sessionStorage.setItem('loginRedirectAttempts', (redirectAttempts + 1).toString());
        router.push('/login');
      } else {
        console.error('Too many redirect attempts, staying on dashboard to prevent loop');
      }
    }

    // Reset redirect counter when we're on the dashboard and authenticated
    if (isClient && user && !authLoading) {
      sessionStorage.removeItem('loginRedirectAttempts');
    }
  }, [user, authLoading, router, isClient])

  // Render skeletons while loading auth state or if user is null (before redirect happens)
  console.log('Dashboard loading state:', {
    isClient,
    authLoading,
    companyLoading,
    onboardingLoading,
    roleLoading,
    user,
    company,
    onboardingData,
    isCashier
  });
  if (!isClient || authLoading || companyLoading || onboardingLoading || roleLoading) {
    return (
      <LoadingOverlay />
    )
  }

  // If user has company metadata but no company (auto-creation failed), show fallback
  if (user && !company && user.user_metadata?.company_name) {
    const companyName = user.user_metadata.company_name
    const businessType = user.user_metadata.business_type

    const handleCreateCompany = async () => {
      try {
        const response = await fetch('/api/companies/auto-create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: user.id,
            companyName,
            businessType: businessType || 'other',
          }),
        })

        if (response.ok) {
          // Refresh the page to reload company context
          window.location.reload()
        } else {
          console.error('Failed to create company')
        }
      } catch (error) {
        console.error('Error creating company:', error)
      }
    }

    return (
      <MainLayout>
        <div className="container mx-auto py-8 text-center space-y-6">
          <div className="max-w-md mx-auto">
            <h1 className="text-2xl font-semibold mb-4">Company Setup Required</h1>
            <p className="text-muted-foreground mb-6">
              We found your business information ({companyName}) but your company profile wasn&apos;t created automatically.
              Let&apos;s create it now to access your loyalty program dashboard.
            </p>
            <Button onClick={handleCreateCompany} className="w-full">
              Create {companyName} Profile
            </Button>
            <p className="text-sm text-muted-foreground mt-4">
              Business Type: {businessType || 'Other'}
            </p>
          </div>
        </div>
      </MainLayout>
    )
  }

  // If we reach here, authLoading is false and user is authenticated.
  // Format date for display - only run on client side after mount
  const date = isClient
    ? new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    : ''; // Keep date empty initially if !isClient

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8 dashboard-pattern">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div className="flex items-center gap-4">
            {/* Company Logo */}
            {company?.logo_url && (
              <div className="hidden sm:flex items-center justify-center w-12 h-12 rounded-lg overflow-hidden bg-muted/30 border">
                <Image
                  src={company!.logo_url!}
                  alt={`${company!.name} logo`}
                  width={48}
                  height={48}
                  className="object-contain"
                />
              </div>
            )}
            <div>
              <h1 className="text-2xl md:text-3xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-amber-500 dark:from-purple-400 dark:to-amber-400">
                {showWelcomeState ? `Welcome to ${businessName}!` : 'Welcome back'}
              </h1>
              <p className="text-muted-foreground font-medium">
                {showWelcomeState
                  ? "Let's get your loyalty program set up"
                  : `${businessName} • ${date}`
                }
              </p>
            </div>
          </div>
          <div className="flex flex-wrap items-center gap-3">
            {!showWelcomeState && (
              <span className="py-1 px-3 rounded-full bg-muted/60 text-xs font-medium flex items-center gap-1">
                <span className="h-1.5 w-1.5 rounded-full bg-emerald-400 animate-pulse"></span>
                <span className="text-muted-foreground">Premium Plan</span>
              </span>
            )}
            <Button asChild variant="outline" size="sm">
              <Link href="/settings" className="flex items-center gap-2">
                <ArrowUpRight className="h-4 w-4" />
                <span>Settings</span>
              </Link>
            </Button>
            {showWelcomeState && (
              <Button onClick={() => setShowSetupWizard(true)} className="flex items-center gap-2">
                Setup Wizard
              </Button>
            )}
          </div>
        </div>

        {/* Conditional Content Based on Onboarding Status */}
        {showWelcomeState ? (
          // New Business Welcome State
          <div className="space-y-8">
            <WelcomeState />
            <DemoData showDemoLabel={true} />
            {showSetupWizard && (
              <SetupWizard
                onComplete={() => setShowSetupWizard(false)}
                onSkip={() => setShowSetupWizard(false)}
              />
            )}
          </div>
        ) : (
          // Regular Dashboard Content
          <div className="space-y-8">
            {/* KPI Cards */}
            <Suspense fallback={<KpiCardsSkeleton />}>
              <KpiCards />
            </Suspense>

            {/* Business Metrics Section */}
            <Suspense fallback={<LoadingSpinner />}>
              <BusinessMetricsSection />
            </Suspense>

            {/* Business Purchase Analytics with Configuration - Full Width */}
            <div className="w-full">
              <Suspense fallback={<LoadingSpinner />}>
                <ConfigurableDashboard />
              </Suspense>
            </div>

            {/* Top Members - Full Width */}
            <div className="w-full">
              <Suspense fallback={<TopMembersSkeleton />}>
                <TopMembers />
              </Suspense>
            </div>

            {/* Data Visualization and Activity Feed Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Points Over Time Chart */}
              <div>
                <Card className="premium-card h-full">
                  <CardHeader className="pb-2 border-b border-border">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <CardTitle>Points Over Time</CardTitle>
                      <div className="flex items-center p-1 bg-muted/50 rounded-lg">
                        {['7d', '30d', '90d', '1y'].map((range) => (
                          <Button
                            key={range}
                            variant="ghost"
                            size="sm"
                            onClick={() => setActiveTimeRange(range)}
                            className={`h-8 px-3 text-xs font-medium rounded-md ${
                              activeTimeRange === range
                                ? 'bg-background text-primary shadow-sm'
                                : 'text-muted-foreground hover:text-foreground'
                            }`}
                          >
                            {range.toUpperCase()}
                          </Button>
                        ))}
                      </div>
                    </div>
                    <CardDescription className="text-xs pt-1">
                      Track your customer engagement and rewards activity
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <Suspense fallback={<PointsChartSkeleton />}>
                      <PointsOverTimeChart
                        activeTimeRange={activeTimeRange}
                      />
                    </Suspense>
                  </CardContent>
                </Card>
              </div>

              {/* Activity Feed */}
              <div>
                <Card className="premium-card h-full">
                  <CardHeader className="pb-2 border-b border-border">
                    <div className="flex items-center justify-between">
                      <CardTitle>Recent Activity</CardTitle>
                      <Button variant="ghost" size="icon" className="h-8 w-8 rounded-full">
                        {/* <ArrowUpRight className="h-4 w-4 text-muted-foreground" /> */}
                      </Button>
                    </div>
                    <CardDescription className="text-xs pt-1">
                      Latest customer interactions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-0 h-[350px] overflow-auto">
                    <Suspense fallback={<ActivityFeedSkeleton />}>
                      <MemberActivityFeed />
                    </Suspense>
                  </CardContent>
                  <CardFooter className="py-3 border-t border-border">
                    <Button asChild variant="ghost" size="sm" className="w-full justify-between">
                      <Link href="/activity" className="flex items-center justify-between w-full">
                        <span>View all activity</span>
                        <ArrowUpRight className="h-4 w-4" />
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </div>

            {/* Quick Actions and Business Stats */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Quick Actions */}
              <Suspense fallback={<div>Loading...</div>}>
                <QuickActions />
              </Suspense>

              {/* Business Stats */}
              <Suspense fallback={<div>Loading...</div>}>
                <BusinessStats />
              </Suspense>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
