'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { Loader2, Copy, Check, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

// Define invitation details type
interface InvitationDetails {
  id: string
  email: string
  companyName: string
  invitedBy: string
  expiresAt: string
}

// Define auto-created account type
interface AutoCreatedAccount {
  email: string
  tempPassword?: string
  role: string
  companyName: string
  requiresPasswordChange: boolean
}

// Main component that uses Suspense for useSearchParams
export default function AcceptInvitationPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Loading Invitation</CardTitle>
            <CardDescription>Please wait while we verify your invitation...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    }>
      <InvitationContent />
    </Suspense>
  )
}

// Content component that uses useSearchParams safely inside Suspense
function InvitationContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')

  const [loading, setLoading] = useState(false)
  const [invitationDetails, setInvitationDetails] = useState<InvitationDetails | null>(null)
  const [invitationLoading, setInvitationLoading] = useState(true)
  const [invitationError, setInvitationError] = useState<string | null>(null)
  
  const [autoCreatedAccount, setAutoCreatedAccount] = useState<AutoCreatedAccount | null>(null)
  const [copied, setCopied] = useState(false)

  const [name, setName] = useState('')
  const [password, setPassword] = useState('')
  const [formErrors, setFormErrors] = useState<{ name?: string; password?: string }>({});

  // Fetch invitation details
  useEffect(() => {
    if (!token) {
      setInvitationLoading(false)
      setInvitationError('No invitation token provided')
      return
    }

    fetch(`/api/cashiers/accept?token=${token}`)
      .then(res => res.json())
      .then(data => {
        if (data.error) {
          setInvitationError(data.error)
        } else {
          setInvitationDetails(data.invitation)
        }
      })
      .catch(err => {
        console.error('Error fetching invitation:', err)
        setInvitationError('Failed to load invitation details')
      })
      .finally(() => {
        setInvitationLoading(false)
      })
  }, [token])

  // Auto-create account with temporary password
  const handleAutoCreate = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/cashiers/auto-create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
        }),
      })

      const data = await response.json()
      console.log('Auto-create response:', data)

      if (!response.ok) {
        toast.error(data.error || "Failed to create account")
        setLoading(false)
        return
      }

      // Make sure we're setting the correct data structure
      setAutoCreatedAccount(data.user)
      console.log('Setting auto-created account:', data.user)
      toast.success("Account created successfully!")
    } catch (error) {
      console.error('Error creating account:', error)
      toast.error("An unexpected error occurred")
    } finally {
      setLoading(false)
    }
  }

  // Copy temporary password to clipboard
  const copyToClipboard = () => {
    if (autoCreatedAccount?.tempPassword) {
      navigator.clipboard.writeText(autoCreatedAccount.tempPassword)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
      toast.success("Temporary password copied to clipboard")
    }
  }

  // Manual account creation
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setFormErrors({})

    // Validate form
    let hasErrors = false
    const errors: { name?: string; password?: string } = {}

    if (!name.trim()) {
      errors.name = 'Name is required'
      hasErrors = true
    }

    if (!password || password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
      hasErrors = true
    }

    if (hasErrors) {
      setFormErrors(errors)
      setLoading(false)
      return
    }

    try {
      const response = await fetch('/api/cashiers/accept', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          name,
          password,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        toast.error(data.error || "Failed to accept invitation")
        setLoading(false)
        return
      }

      toast.success("Invitation accepted! You can now log in.")

      // Redirect to login page
      router.push('/login')
    } catch (error) {
      console.error('Error accepting invitation:', error)
      toast.error("An unexpected error occurred")
      setLoading(false)
    }
  }

  if (invitationLoading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Loading Invitation</CardTitle>
            <CardDescription>Please wait while we verify your invitation...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (invitationError) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md border-red-200">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-600">Invitation Error</CardTitle>
            <CardDescription>{invitationError}</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button variant="outline" onClick={() => router.push('/')}>
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  if (!invitationDetails) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <Card className="w-full max-w-md border-red-200">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-red-600">Invalid Invitation</CardTitle>
            <CardDescription>This invitation link is invalid or has expired.</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button variant="outline" onClick={() => router.push('/')}>
              Return to Home
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Accept Cashier Invitation</CardTitle>
          <CardDescription>
            Complete your account setup to join as a cashier
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invitationLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : invitationError ? (
            <div className="text-center py-8">
              <p className="text-destructive">{invitationError}</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => router.push('/login')}
              >
                Go to Login
              </Button>
            </div>
          ) : autoCreatedAccount ? (
            <div className="space-y-6">
              <Alert className="bg-green-50 border-green-200">
                <AlertTitle className="text-green-800">Account Created Successfully!</AlertTitle>
                <AlertDescription className="text-green-700">
                  Your account has been created. You can now log in with your email and temporary password.
                </AlertDescription>
              </Alert>
              
              <div className="space-y-4">
                <div>
                  <Label>Email</Label>
                  <div className="p-2 border rounded mt-1 bg-muted">
                    {autoCreatedAccount.email}
                  </div>
                </div>
                
                {autoCreatedAccount.tempPassword && (
                  <div>
                    <Label>Temporary Password</Label>
                    <div className="flex mt-1">
                      <div className="p-2 border rounded flex-1 bg-muted font-mono">
                        {autoCreatedAccount.tempPassword}
                      </div>
                      <Button 
                        variant="outline" 
                        size="icon" 
                        className="ml-2" 
                        onClick={copyToClipboard}
                      >
                        {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                      </Button>
                    </div>
                    <p className="text-sm text-amber-600 mt-2">
                      <strong>Important:</strong> Save this password! You&apos;ll need to change it after your first login.
                    </p>
                  </div>
                )}
                
                <div>
                  <Label>Company</Label>
                  <div className="p-2 border rounded mt-1 bg-muted">
                    {autoCreatedAccount.companyName}
                  </div>
                </div>
                
                <div>
                  <Label>Role</Label>
                  <div className="p-2 border rounded mt-1 bg-muted">
                    {autoCreatedAccount.role}
                  </div>
                </div>
              </div>
              
              <Button 
                className="w-full mt-4" 
                onClick={() => router.push('/login')}
              >
                Go to Login
              </Button>
            </div>
          ) : (
            <>
              <div className="mb-6 text-sm">
                <p>
                  <span className="font-medium">Company:</span> {invitationDetails?.companyName}
                </p>
                <p>
                  <span className="font-medium">Email:</span> {invitationDetails?.email}
                </p>
                <p>
                  <span className="font-medium">Invited by:</span> {invitationDetails?.invitedBy}
                </p>
              </div>

              <div className="space-y-6">
                <Button 
                  className="w-full" 
                  onClick={handleAutoCreate} 
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Account...
                    </>
                  ) : (
                    'Create Account Automatically'
                  )}
                </Button>
                
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or create manually
                    </span>
                  </div>
                </div>

                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter your full name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        disabled={loading}
                      />
                      {formErrors.name && (
                        <p className="text-sm text-destructive">{formErrors.name}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="password">Password</Label>
                      <Input
                        id="password"
                        type="password"
                        placeholder="Create a password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        disabled={loading}
                      />
                      {formErrors.password && (
                        <p className="text-sm text-destructive">{formErrors.password}</p>
                      )}
                    </div>
                  </div>

                  <Button className="w-full mt-6" type="submit" disabled={loading}>
                    {loading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      'Create Account Manually'
                    )}
                  </Button>
                </form>
              </div>
            </>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <a
              href="/login"
              className="text-primary hover:underline"
              onClick={(e) => {
                e.preventDefault()
                router.push('/login')
              }}
            >
              Log in
            </a>
          </p>
        </CardFooter>
      </Card>
    </div>
  )
}
