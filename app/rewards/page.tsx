'use client'

import { useState, useEffect } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useUserRole } from '@/hooks/use-user-role'
import { useCompany } from '@/contexts/company-context'
import { useRewards, useD<PERSON><PERSON>Reward, RewardDeleteError } from '@/hooks/use-rewards'
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import { Plus, Trash2 } from 'lucide-react'
import { generateDefaultRewardCover } from '@/lib/reward-colors'
import { toast } from 'sonner'
import { DeleteConfirmDialog } from '@/components/delete-confirm-dialog'
import { Cake, HelpCircle } from "lucide-react"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

type APIRewardListItem = {
  id: string
  title: string
  description: string
  points_required: number
  is_active: boolean
  expiration_date: string | null
  created_at: string
  code?: string
  reward_image_url?: string
  reward_type?: string
}

type Reward = {
  id: string
  title: string
  description: string
  points_required: number
  is_active: boolean
  redemptionCount: number
  redemption_count?: number // Added for API response format
  reward_type?: string // Added for birthday reward type
  created_at: string
  expiration_date: string | null
  code: string
  reward_image_url?: string
}

export default function RewardsPage() {
  const { user, isLoading } = useRequireAuth()
  const { isLoading: companyLoading } = useCompany()
  const userRole = useUserRole()
  const [mounted, setMounted] = useState(false)
  const router = useRouter()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [rewardToDelete, setRewardToDelete] = useState<{ id: string; title: string } | null>(null)
  const [forceDelete, setForceDelete] = useState(false)

  // Use the hook to fetch rewards
  const { data: rewardsData, isLoading: loading, refetch } = useRewards()

  // Redirect cashiers away from rewards page
  useEffect(() => {
    if (!isLoading && !companyLoading && userRole.isCashier) {
      toast.error('Access denied. Cashiers cannot manage rewards.')
      router.push('/dashboard')
      return
    }
  }, [isLoading, companyLoading, userRole.isCashier, router])

  // Force a refresh when the component mounts to ensure fresh data
  useEffect(() => {
    if (mounted && !loading) {
      console.log('=== Forcing rewards refetch to get fresh data ===')
      refetch()
    }
  }, [mounted, loading, refetch])

  // Delete reward mutation
  const { mutate: deleteReward, isPending: isDeleting } = useDeleteReward()

  // Open delete confirmation dialog
  const openDeleteDialog = (id: string, title: string) => {
    setRewardToDelete({ id, title })
    setForceDelete(false) // Reset force delete flag
    setDeleteDialogOpen(true)
  }

  // Handle reward deletion
  const handleDeleteReward = async () => {
    if (!rewardToDelete) return

    deleteReward(
      { rewardId: rewardToDelete.id, force: forceDelete },
      {
        onSuccess: () => {
          toast.success('Reward deleted successfully')
          refetch() // Refresh the rewards list
        },
        onError: (error: RewardDeleteError) => {
          // If the error is due to existing redemptions, show a special message with option to force delete
          if (error.code === 'REWARD_HAS_REDEMPTIONS') {
            toast.error(
              'This reward has been redeemed by members. Use the Force Delete option to delete it anyway.',
              {
                action: {
                  label: 'Force Delete',
                  onClick: () => {
                    setForceDelete(true)
                    setDeleteDialogOpen(true) // Re-open the dialog with force delete enabled
                  }
                }
              }
            )
          } else {
            toast.error(`Failed to delete reward: ${error.message}`)
          }
        },
      }
    )
  }

  // Transform the data to match our UI interface
  const rewards: Reward[] = (rewardsData?.data || []).map((item: APIRewardListItem & { redemption_count?: number }) => ({
    id: item.id,
    title: item.title || '',
    description: item.description || '',
    points_required: item.points_required,
    is_active: item.is_active,
    redemptionCount: item.redemption_count || 0, // Use the redemption_count from API
    redemption_count: item.redemption_count, // Keep original field
    created_at: item.created_at,
    expiration_date: item.expiration_date,
    code: item.code ?? '',
    reward_image_url: item.reward_image_url
  }))

  // No prefetching needed - we'll rely on browser caching and Next.js Image optimization

  // Handle hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  // Redirect if not authenticated
  useEffect(() => {
    if (mounted && !isLoading && !user) {
      router.push('/login')
    }
  }, [mounted, isLoading, user, router])

  if (!mounted || isLoading || companyLoading || loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
        <div className="animate-shimmer w-48 h-12 rounded-lg"></div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect in the effect
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Manage Rewards</h2>
        <p className="text-muted-foreground mb-6">
          Create and manage rewards that your members can redeem with their points.
        </p>
      </div>
        <Button className="btn-primary" asChild>
          <Link href="/rewards/add"><Plus className="mr-2 h-4 w-4" />Create New Reward</Link>
        </Button>
      </div>

      <Card className="mb-8">
        <CardContent className="p-6">
          {loading ? (
            <div className="text-center py-8">Loading rewards...</div>
          ) : rewards.length === 0 ? (
            <div className="text-center py-8">
              No rewards found. Create your first reward to get started!
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rewards.map((reward) => (
                <div key={reward.id} className="border rounded-lg overflow-hidden bg-card shadow-sm">
                  {/* Card Header with Image or Gradient */}
                  <div className="relative h-48 overflow-hidden">
                    {reward.reward_image_url ? (
                      <Image
                        src={reward.reward_image_url}
                        alt={reward.title}
                        fill
                        className="object-cover transition-transform hover:scale-105 duration-300"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                        priority={true}
                        loading="eager"
                      />
                    ) : (
                      <div
                        className="w-full h-full"
                        style={{
                          background: generateDefaultRewardCover(reward.id).gradient
                        }}
                      />
                    )}
                  </div>

                  {/* Card Content */}
                  <div className="p-4">
                    <div className="flex justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold">{reward.title}</h3>
                        {reward.reward_type === 'BIRTHDAY' && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center">
                                  <Cake className="h-4 w-4 text-pink-500" />
                                  <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent className="max-w-xs">
                                <p>Birthday rewards are only available to members during their birthday period (±7 days from their birthday).</p>
                                <p className="mt-1">The system automatically checks eligibility when members attempt to redeem.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        {reward.reward_type === 'BIRTHDAY' && (
                          <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
                            Birthday
                          </Badge>
                        )}
                        <Badge variant={reward.is_active ? "default" : "secondary"}>
                          {reward.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                        {reward.expiration_date && (
                          <Badge
                            variant={
                              new Date(reward.expiration_date) <= new Date() ? "destructive" :
                              new Date(reward.expiration_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) ? "outline" :
                              "secondary"
                            }
                            className={
                              new Date(reward.expiration_date) <= new Date() ? "" :
                              new Date(reward.expiration_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) ? "border-orange-200 text-orange-700 bg-orange-50" :
                              ""
                            }
                          >
                            {new Date(reward.expiration_date) <= new Date() ? 'Expired' :
                             new Date(reward.expiration_date) <= new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) ? 'Expires Soon' :
                             'Active'}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{reward.description}</p>

                    <div className="text-xl font-bold text-primary mb-2">
                      {reward.points_required} <span className="text-sm font-normal text-muted-foreground">points</span>
                    </div>

                    <div className="flex justify-between text-sm text-muted-foreground mb-4">
                      <span className={new Date(reward.expiration_date || '') <= new Date() ? 'text-red-600' : ''}>
                        Valid until {reward.expiration_date ? new Date(reward.expiration_date).toLocaleDateString() : 'No expiry'}
                      </span>
                      <span>{reward.redemptionCount} redemptions</span>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1"
                        asChild
                      >
                        <Link href={`/rewards/${reward.id}`}>
                          <span>View</span>
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1"
                        asChild
                      >
                        <Link href={`/rewards/${reward.id}/edit`}>
                          <span>Edit</span>
                        </Link>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center gap-1 text-destructive hover:text-destructive hover:bg-destructive/10"
                        onClick={() => openDeleteDialog(reward.id, reward.title)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span>Delete</span>
                      </Button>
                    </div>

                    {reward.code && (
                      <div className="mt-2 text-xs text-muted-foreground">
                        Code: <span className="font-mono">{reward.code}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        title={`Delete Reward: ${rewardToDelete?.title || ''}`}
        description={forceDelete
          ? "WARNING: This will permanently delete the reward AND all its redemption records. This action cannot be undone."
          : "Are you sure you want to delete this reward? This action cannot be undone."}
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteReward}
      />
    </div>
  )
}
