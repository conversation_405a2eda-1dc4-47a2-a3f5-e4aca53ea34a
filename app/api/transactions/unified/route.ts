import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { z } from 'zod'
import { sendPointsNotification, sendRewardRedemptionNotification } from '@/lib/telegram-notifications'

interface Transaction {
  id: string
  member_id: string
  company_id: string
  transaction_type: 'EARN' | 'REDEEM'
  points_change: number
  description: string
  transaction_date: string
  total_amount?: number
  business_name?: string
  receipt_number?: string // Changed from financial_system_number to match DB schema
  reward_id?: string
  created_at?: string
}

interface Reward {
  id: string
  title: string
  description?: string
  points_required: number
  reward_value_type: string
  reward_value: number
  is_active: boolean
  company_id: string
}

// Schema for unified transaction processing
const unifiedTransactionSchema = z.object({
  member_id: z.string().uuid(),
  company_id: z.string().uuid(),
  total_amount: z.number().positive(),
  subtotal: z.number().positive().optional(), // Subtotal before tax for reward calculations
  tax_amount: z.number().optional(), // Tax amount
  business_name: z.string().optional(),
  financial_system_number: z.string().optional(),
  transaction_date: z.string(),
  description: z.string().optional(),
  points_earned: z.number().optional(),
  receipt_image_url: z.string().url().optional().or(z.literal('')), // Allow empty string
  receipt_ocr_data: z.string().optional(), // JSON string of enhanced OCR data
  receipt_ocr_confidence: z.number().min(0).max(1).optional(), // Confidence score
  applied_rewards: z.array(z.object({
    reward_id: z.string().uuid(),
    points_used: z.number().min(0) // Allow 0 for double points rewards that don't consume points
  })).max(1).optional() // Enforce max 1 reward per transaction
})

export async function POST(request: NextRequest) {
  try {
    console.log('Creating Supabase service role client...')
    const supabase = getServiceRoleClient() // Use service role client
    console.log('Service role client created successfully')

    const requestData = await request.json()
    console.log('Received request data:', requestData)

    const validatedData = unifiedTransactionSchema.parse(requestData)
    console.log('Validated data:', validatedData)

    // Check for duplicate receipt by FS No. if provided
    if (validatedData.financial_system_number) {
      const { data: existingReceipt, error: receiptCheckError } = await supabase
        .from('receipts')
        .select('id, receipt_number')
        .eq('receipt_number', validatedData.financial_system_number)
        .maybeSingle()

      if (existingReceipt) {
        return NextResponse.json(
          { error: 'Duplicate receipt', message: `A receipt with FS No. ${validatedData.financial_system_number} has already been submitted.` },
          { status: 409 } // 409 Conflict status code
        )
      }

      if (receiptCheckError) {
        console.error('Error checking for duplicate receipt:', receiptCheckError)
      }
    }

    // Get company configuration - bypass RLS by using direct SQL query
    console.log('Looking up company with ID:', validatedData.company_id)

    // Use raw SQL to bypass RLS issues with the service role
    const { data: companyResult, error: companyError } = await supabase
      .rpc('get_company_config', { company_id: validatedData.company_id })

    console.log('Company lookup result:', { companyResult, companyError })

    // If the RPC function doesn't exist, fallback to direct query with RLS bypass
    let company = companyResult?.[0]
    if (companyError || !company) {
      console.log('RPC function not found, trying direct query...')

      // Try direct query with explicit RLS bypass
      const { data: directResult, error: directError } = await supabase
        .from('companies')
        .select('points_earning_ratio, id, name')
        .eq('id', validatedData.company_id)
        .single()

      console.log('Direct query result:', { directResult, directError })

      if (directError || !directResult) {
        console.log('Company configuration not found:', directError)
        return NextResponse.json({
          error: 'Company configuration not found',
          details: directError?.message
        }, { status: 404 })
      }

      company = directResult
    }

    if (!company) {
      console.log('Company configuration not found after all attempts')
      return NextResponse.json({ error: 'Company configuration not found' }, { status: 404 })
    }

    const results: Array<{
      type: 'EARN' | 'REDEEM'
      transaction: Transaction
      reward?: Reward
      points_change: number
    }> = []

    // Step 1: Calculate base points for earning transaction using subtotal (before tax)
    const amountForPoints = validatedData.subtotal || validatedData.total_amount
    const basePointsEarned = validatedData.points_earned ||
      Math.floor(amountForPoints * (company.points_earning_ratio || 1))

    // Step 1.25: Save receipt information in the receipts table if FS No. is provided
    let createdReceiptData: { id: string } | null = null;

    if (validatedData.financial_system_number) {
      // Get member loyalty_id for receipt association
      const { data: memberData } = await supabase
        .from('loyalty_members')
        .select('loyalty_id')
        .eq('id', validatedData.member_id)
        .single()

      const transactionDate = new Date(validatedData.transaction_date)

      // Insert receipt record
      const { data: receiptData, error: receiptError } = await supabase
        .from('receipts')
        .insert({
          receipt_number: validatedData.financial_system_number,
          member_id: validatedData.member_id,
          loyalty_id: memberData?.loyalty_id,
          purchase_date: transactionDate.toISOString(),
          total_amount: validatedData.total_amount,
          subtotal: validatedData.subtotal || validatedData.total_amount, // Use actual subtotal if provided
          tax_amount: validatedData.tax_amount,
          service_description: validatedData.description,
          points_awarded: basePointsEarned,
          receipt_image_url: validatedData.receipt_image_url,
          business_name: validatedData.business_name,
          company_id: validatedData.company_id,
          created_at: new Date().toISOString(),
          uploader_telegram_id: 'web-app' // Default value for web uploads
        })
        .select()
        .single()

      if (receiptError) {
        console.error('Error saving receipt information:', receiptError)
        console.error('Receipt error details:', {
          code: receiptError.code,
          message: receiptError.message,
          details: receiptError.details,
          hint: receiptError.hint
        })

        // Check if this is a duplicate receipt error (unique constraint violation)
        if (receiptError.code === '23505' ||
            receiptError.message?.toLowerCase().includes('duplicate') ||
            receiptError.message?.toLowerCase().includes('unique') ||
            receiptError.message?.toLowerCase().includes('already exists')) {
          console.log('Detected duplicate receipt during creation, returning 409 error')
          return NextResponse.json(
            { error: 'Duplicate receipt', message: `A receipt with FS No. ${validatedData.financial_system_number} has already been submitted.` },
            { status: 409 }
          )
        }

        // For other errors, continue with transaction creation
        console.log('Non-duplicate receipt error, continuing with transaction processing')
      } else {
        console.log('Receipt saved successfully:', receiptData)
        createdReceiptData = receiptData; // Store for linking to points transaction

        // STEP 1.5: Create receipt_items for analytics using OCR data if available
        try {
          console.log('[UnifiedAPI] Processing receipt items for analytics...');

          const { processReceiptItems, processReceiptItemsFromOCR } = await import('@/lib/receipt-items-processor');

          let result;

          // Use enhanced OCR processing if OCR data is available
          if (validatedData.receipt_ocr_data) {
            console.log('[UnifiedAPI] Using OCR data for receipt items processing');
            result = await processReceiptItemsFromOCR(
              receiptData.id,
              validatedData.company_id,
              validatedData.receipt_ocr_data,
              validatedData.total_amount
            );
          } else {
            console.log('[UnifiedAPI] Using generic receipt items processing');
            result = await processReceiptItems(
              receiptData.id,
              validatedData.company_id,
              validatedData.description || 'General Purchase',
              validatedData.total_amount,
              validatedData.total_amount
            );
          }

          if (result.success) {
            console.log(`[UnifiedAPI] Receipt items processed successfully: ${result.items_created} items created`);
          } else {
            console.error('[UnifiedAPI] Receipt items processing failed:', result.error);
          }
        } catch (processingError) {
          console.error('[UnifiedAPI] Exception during receipt items processing:', processingError);
          // Continue with transaction creation even if analytics processing fails
        }
      }
    }

    // Step 1.5: Create earning transaction using custom function to bypass RLS and triggers
    const expirationDate = new Date(Date.now() + (company.points_expiration_days || 365) * 24 * 60 * 60 * 1000).toISOString().split('T')[0]

    const { data: earningTransaction, error: earningError } = await supabase
      .rpc('create_points_transaction', {
        p_member_id: validatedData.member_id,
        p_company_id: validatedData.company_id,
        p_transaction_type: 'EARN',
        p_points_change: basePointsEarned, // Use base points (multipliers will be applied through redemptions)
        p_description: validatedData.description ||
          `Purchase at ${validatedData.business_name || 'Unknown Business'} - ${basePointsEarned} points earned`,
        p_transaction_date: (() => {
          // If transaction_date looks like a date-only string (YYYY-MM-DD),
          // use current timestamp instead to capture the actual time
          if (validatedData.transaction_date && /^\d{4}-\d{2}-\d{2}$/.test(validatedData.transaction_date)) {
            return new Date().toISOString()
          }
          // Otherwise use provided timestamp or current time
          return validatedData.transaction_date ?
            new Date(validatedData.transaction_date).toISOString() :
            new Date().toISOString()
        })(),
        p_expiration_date: expirationDate,
        p_total_amount: validatedData.total_amount,
        p_business_name: validatedData.business_name,
        p_receipt_number: validatedData.financial_system_number,
        p_receipt_ocr_confidence: validatedData.receipt_ocr_confidence,
        p_receipt_processing_status: 'completed',
        p_receipt_image_url: validatedData.receipt_image_url,
        p_receipt_id: createdReceiptData?.id || null, // Link to receipt record
        p_receipt_ocr_data: validatedData.receipt_ocr_data || null // Store OCR data for analytics
      })

    console.log('Create transaction result:', { earningTransaction, earningError })

    if (earningError || !earningTransaction || earningTransaction.length === 0) {
      console.error('Error creating earning transaction:', earningError)
      console.error('Full error details:', JSON.stringify(earningError, null, 2))
      return NextResponse.json({ error: 'Failed to create earning transaction' }, { status: 500 })
    }

    results.push({
      type: 'EARN',
      transaction: earningTransaction[0], // RPC returns array, take first element
      points_change: basePointsEarned
    })

    // Step 2: Process reward redemptions if any
    if (validatedData.applied_rewards && validatedData.applied_rewards.length > 0) {
      // We know there's only one reward due to schema validation with max(1)
      const rewardApplication = validatedData.applied_rewards[0];

      // Get reward details
      const { data: reward, error: rewardError } = await supabase
        .from('rewards')
        .select('*')
        .eq('id', rewardApplication.reward_id)
        .eq('company_id', validatedData.company_id)
        .single()

      if (rewardError || !reward) {
        console.error('Reward not found:', rewardApplication.reward_id)
        return NextResponse.json({ error: 'Reward not found' }, { status: 404 })
      }

      // Check if member has already redeemed this reward (one-time usage constraint)
      const { data: existingRedemption, error: redemptionCheckError } = await supabase
        .from('reward_redemptions')
        .select('id, redemption_date')
        .eq('reward_id', rewardApplication.reward_id)
        .eq('member_id', validatedData.member_id)
        .eq('company_id', validatedData.company_id)
        .maybeSingle()

      if (redemptionCheckError) {
        console.error('Error checking existing redemption:', redemptionCheckError)
        return NextResponse.json({ error: 'Error validating reward eligibility' }, { status: 500 })
      }

      if (existingRedemption) {
        console.log('Member has already redeemed this reward:', {
          memberId: validatedData.member_id,
          rewardId: rewardApplication.reward_id,
          previousRedemption: existingRedemption.redemption_date
        })
        return NextResponse.json({
          error: 'Reward already redeemed',
          message: `This member has already redeemed "${reward.title}" on ${new Date(existingRedemption.redemption_date).toLocaleDateString()}. Each reward can only be used once per member.`,
          code: 'REWARD_ALREADY_REDEEMED'
        }, { status: 409 })
      }

      // Check if member has enough points
      const { data: memberPoints, error: pointsError } = await supabase
        .from('loyalty_members')
        .select('lifetime_points, redeemed_points, expired_points')
        .eq('id', validatedData.member_id)
        .single()

        if (pointsError || !memberPoints) {
        console.error('Error fetching member points:', pointsError)
        return NextResponse.json({ error: 'Error fetching member points' }, { status: 500 })
      }

      const availablePoints = memberPoints.lifetime_points - memberPoints.redeemed_points - memberPoints.expired_points

      if (availablePoints < reward.points_required) {
        console.error('Insufficient points for reward:', reward.id, 'Available:', availablePoints, 'Required:', reward.points_required)
        return NextResponse.json({ error: 'Insufficient points for reward' }, { status: 400 })
      }

      // Handle different reward types
      let rewardDescription = `Redeemed: ${reward.title}`

      if (reward.reward_value_type === 'DOUBLE_POINTS') {
        // For double points rewards, don't deduct points, but apply bonus points
        const bonusPoints = Math.floor(basePointsEarned * (reward.reward_value - 1)) // Extra points from multiplier
        rewardDescription = `${reward.title}: +${bonusPoints} bonus points (${reward.reward_value}x multiplier)`

        // Create a bonus points transaction
        const { data: bonusTransaction, error: bonusError } = await supabase
          .rpc('create_points_transaction', {
            p_member_id: validatedData.member_id,
            p_company_id: validatedData.company_id,
            p_transaction_type: 'EARN',
            p_points_change: bonusPoints,
            p_description: rewardDescription,
            p_transaction_date: validatedData.transaction_date,
            p_expiration_date: expirationDate,
            p_total_amount: null,
            p_business_name: null,
            p_receipt_number: null,
            p_receipt_ocr_confidence: null,
            p_receipt_processing_status: null,
            p_receipt_image_url: null,
            p_receipt_id: null,
            p_receipt_ocr_data: null
          })

        if (bonusError || !bonusTransaction || bonusTransaction.length === 0) {
          console.error('Error creating bonus points transaction:', bonusError)
          return NextResponse.json({ error: 'Error creating bonus points transaction' }, { status: 500 })
        }

        // Add the bonus transaction to results
        results.push({
          type: 'EARN',
          transaction: bonusTransaction[0],
          reward: reward,
          points_change: bonusPoints
        })
      } else {
        // For regular rewards (percentage/fixed), deduct points as usual
        // Only create redemption transaction if points are actually required
        if (reward.points_required > 0) {
          // Create redemption transaction using the same RPC function to bypass triggers
          const { data: redemptionTransaction, error: redemptionError } = await supabase
            .rpc('create_points_transaction', {
              p_member_id: validatedData.member_id,
              p_company_id: validatedData.company_id,
              p_transaction_type: 'REDEEM',
              p_points_change: -reward.points_required,
              p_description: rewardDescription,
              p_transaction_date: validatedData.transaction_date,
              p_expiration_date: new Date().toISOString().split('T')[0],
              p_total_amount: null,
              p_business_name: null,
              p_receipt_number: null,
              p_receipt_ocr_confidence: null,
              p_receipt_processing_status: null,
              p_receipt_image_url: null,
              p_receipt_id: null,
              p_receipt_ocr_data: null
            })

          if (redemptionError || !redemptionTransaction || redemptionTransaction.length === 0) {
            console.error('Error creating redemption transaction:', redemptionError)
            return NextResponse.json({ error: 'Error creating redemption transaction' }, { status: 500 })
          }

          results.push({
            type: 'REDEEM',
            transaction: redemptionTransaction[0],
            reward: reward,
            points_change: -reward.points_required
          })
        } else {
          // For zero-point rewards (like "new customer reward"), just track the redemption without a transaction
          console.log('Zero-point reward applied, skipping redemption transaction:', reward.title)
        }
      }

      // Also create a record in the reward_redemptions table for proper tracking
      const { error: redemptionRecordError } = await supabase
        .from('reward_redemptions')
        .insert({
          reward_id: reward.id,
          member_id: validatedData.member_id,
          company_id: validatedData.company_id,
          redemption_date: validatedData.transaction_date,
          points_used: reward.reward_value_type === 'DOUBLE_POINTS' ? 0 : reward.points_required,
          applied_value: reward.reward_value,
          status: 'REDEEMED',
          notes: `Unified transaction redemption: ${reward.title}`
        })

      if (redemptionRecordError) {
        console.error('Error creating redemption record:', redemptionRecordError)
        // Don't fail the entire transaction for this, but log the error
      }
    }

    // Step 3: Calculate final member stats
    const { data: finalMemberStats, error: memberStatsError } = await supabase
      .from('loyalty_members')
      .select('lifetime_points, redeemed_points, expired_points')
      .eq('id', validatedData.member_id)
      .single()

    if (memberStatsError) {
      console.error('Error fetching member stats:', memberStatsError)
      console.error('Full member stats error details:', JSON.stringify(memberStatsError, null, 2))
    }

    // Calculate available points
    const memberStats = finalMemberStats ? {
      available_points: finalMemberStats.lifetime_points - finalMemberStats.redeemed_points - finalMemberStats.expired_points,
      lifetime_points: finalMemberStats.lifetime_points
    } : null

    // Step 4: Send Telegram notifications for successful transactions
    try {
      // Get member's Telegram chat ID
      const { data: memberTelegram } = await supabase
        .from('loyalty_members')
        .select('telegram_chat_id, name')
        .eq('id', validatedData.member_id)
        .single()

      if (memberTelegram?.telegram_chat_id) {
        // Send points earned notification
        const totalPointsEarned = basePointsEarned + (results.filter(r => r.type === 'EARN' && r.reward).reduce((sum, r) => sum + r.points_change, 0))
        if (totalPointsEarned > 0) {
          await sendPointsNotification(
            memberTelegram.telegram_chat_id,
            totalPointsEarned,
            validatedData.business_name || company.name || 'Your Business',
            validatedData.member_id
          )
        }

        // Send reward redemption notifications
        if (validatedData.applied_rewards && validatedData.applied_rewards.length > 0) {
          for (const rewardApplication of validatedData.applied_rewards) {
            const rewardResult = results.find(r => r.reward?.id === rewardApplication.reward_id)
            if (rewardResult?.reward) {
              await sendRewardRedemptionNotification(
                memberTelegram.telegram_chat_id,
                rewardResult.reward.title,
                rewardApplication.points_used,
                validatedData.member_id
              )
            }
          }
        }
      }
    } catch (notificationError) {
      console.error('Error sending Telegram notifications:', notificationError)
      // Don't fail the transaction if notifications fail
    }

    return NextResponse.json({
      success: true,
      data: {
        transactions: results,
        member_stats: memberStats,
        summary: {
          total_transactions: results.length,
          points_earned: basePointsEarned + (results.filter(r => r.type === 'EARN' && r.reward).reduce((sum, r) => sum + r.points_change, 0)),
          total_points_used: validatedData.applied_rewards?.reduce((sum, r) => sum + r.points_used, 0) || 0,
          rewards_applied: validatedData.applied_rewards?.length || 0
        }
      }
    })

  } catch (error) {
    console.error('Unified transaction processing error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      error: 'Internal server error'
    }, { status: 500 })
  }
}
