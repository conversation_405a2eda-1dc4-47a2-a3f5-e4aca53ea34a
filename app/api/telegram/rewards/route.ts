import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// API key for Telegram integration security
// In a production environment, store this in an environment variable
const TELEGRAM_API_KEY = 'telegram-loyal-integration-key'

/**
 * GET /api/telegram/rewards - Get active rewards for a company
 * This endpoint does not require auth but uses an API key for security
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const apiKey = searchParams.get('api_key')
    const companyId = searchParams.get('company_id')
    const memberId = searchParams.get('member_id') // Add member_id parameter
    const activeOnly = searchParams.get('active') === 'true'
    
    // Check API key
    if (apiKey !== TELEGRAM_API_KEY) {
      return NextResponse.json({ 
        error: 'Unauthorized', 
        details: 'Invalid or missing API key'
      }, { status: 401 })
    }
    
    // Check company ID
    if (!companyId) {
      return NextResponse.json({ 
        error: 'Bad request', 
        details: 'Company ID is required'
      }, { status: 400 })
    }
    
    const supabase = getServiceRoleClient() // Use server-side client
    
    // Get already redeemed rewards for this member if member_id is provided
    let redeemedRewardIds: string[] = []
    if (memberId) {
      const { data: redemptions } = await supabase
        .from('reward_redemptions')
        .select('reward_id')
        .eq('member_id', memberId)
        .eq('company_id', companyId)
      
      redeemedRewardIds = redemptions?.map(r => r.reward_id) || []
    }
    
    // Fetch rewards
    let query = supabase
      .from('rewards')
      .select('*')
      .eq('company_id', companyId)
    
    // Add active filter if requested
    if (activeOnly) {
      query = query.eq('is_active', true)
    }
    
    // Add expiration date filter
    query = query.or(`expiration_date.is.null,expiration_date.gt.${new Date().toISOString()}`)
    
    // Execute query
    const { data, error } = await query.order('points_required', { ascending: true })
    
    if (error) {
      return NextResponse.json({ 
        error: 'Database error', 
        details: error.message
      }, { status: 500 })
    }
    
    // Filter out already redeemed rewards if member_id was provided
    const filteredData = memberId 
      ? data?.filter(reward => !redeemedRewardIds.includes(reward.id)) 
      : data
    
    // Get member's available points if member_id is provided
    let memberAvailablePoints = 0
    let isBirthdayEligible = false
    if (memberId) {
      // Get member's points
      const { data: memberData } = await supabase
        .from('loyalty_members')
        .select('lifetime_points, redeemed_points, expired_points')
        .eq('id', memberId)
        .single()
      
      if (memberData) {
        memberAvailablePoints = memberData.lifetime_points - memberData.redeemed_points - memberData.expired_points
      }
      
      // Check birthday eligibility
      const { data: eligibilityResult } = await supabase
        .rpc('is_member_birthday_eligible', { member_id: memberId })
      
      isBirthdayEligible = !!eligibilityResult
    }
    
    // Separate birthday rewards from regular rewards
    const regularRewards = filteredData?.filter(reward => reward.reward_type !== 'BIRTHDAY') || []
    const birthdayRewards = filteredData?.filter(reward => reward.reward_type === 'BIRTHDAY') || []
    
    // Only include birthday rewards if member is birthday eligible
    const availableRewards = isBirthdayEligible 
      ? [...regularRewards, ...birthdayRewards]
      : regularRewards
    
    // Add insufficient points indicator to each reward
    const rewardsWithPointsInfo = availableRewards.map(reward => ({
      ...reward,
      member_has_sufficient_points: memberAvailablePoints >= reward.points_required,
      points_needed: Math.max(0, reward.points_required - memberAvailablePoints),
      member_available_points: memberAvailablePoints
    }))
    
    return NextResponse.json({ 
      data: rewardsWithPointsInfo,
      member_available_points: memberAvailablePoints,
      birthday_eligible: isBirthdayEligible,
      birthday_rewards_count: birthdayRewards.length
    })
  } catch (error) {
    console.error('Error fetching rewards:', error)
    return NextResponse.json({ 
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
