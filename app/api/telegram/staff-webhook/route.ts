import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Interface for Telegram webhook payload
interface TelegramMessage {
  message_id: number
  from: {
    id: number
    first_name: string
    last_name?: string
    username?: string
  }
  chat: {
    id: number
    type: string
    first_name?: string
    last_name?: string
    username?: string
  }
  date: number
  text?: string
}

interface TelegramUpdate {
  update_id: number
  message?: TelegramMessage
}

// Staff bot webhook handler
export async function POST(request: NextRequest) {
  try {
    const body: TelegramUpdate = await request.json()
    console.log('Staff Bot Webhook received:', JSON.stringify(body, null, 2))

    if (!body.message) {
      console.log('[Staff Bot] No message in webhook body')
      return NextResponse.json({ ok: true })
    }

    const { message } = body
    const chatId = message.chat.id
    const text = message.text || ''
    const firstName = message.from.first_name
    const username = message.from.username

    console.log(`[<PERSON> Bot] Processing message:`)
    console.log(`[<PERSON> Bot] - Chat ID: ${chatId}`)
    console.log(`[Staff Bot] - Text: "${text}"`)
    console.log(`[Staff Bot] - Text type: ${typeof text}`)
    console.log(`[Staff Bot] - First name: ${firstName}`)
    console.log(`[Staff Bot] - Username: ${username}`)

    console.log(`Staff Bot - Message from ${firstName} (@${username}): ${text}`)

    // Add environment debugging
    console.log(`[Staff Bot] Environment check:`, {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      supabaseUrlPrefix: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20),
      serviceKeyPrefix: process.env.SUPABASE_SERVICE_ROLE_KEY?.substring(0, 20)
    })

    // Handle different commands
    if (text.startsWith('/start')) {
      await handleStaffStart(chatId, text, firstName)
    } else if (text.startsWith('/help')) {
      await handleStaffHelp(chatId)
    } else if (text.startsWith('/dashboard')) {
      await handleStaffDashboard(chatId)
    } else if (text.startsWith('/invite')) {
      // Check if it's a manual invitation token input
      const inviteParts = text.split(' ')
      if (inviteParts.length > 1 && inviteParts[1].length > 20) {
        // Treat as manual invitation token
        console.log(`[Staff Bot] Manual invitation token provided: ${inviteParts[1]}`)
        await handleStaffInvitation(chatId, inviteParts[1], firstName)
      } else {
        await handleStaffInvite(chatId, text)
      }
    } else {
      await sendStaffMessage(chatId, "I didn't understand that command. Type /help to see available commands.")
    }

    return NextResponse.json({ ok: true })
  } catch (error) {
    console.error('Staff Bot Webhook error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle /start command for staff bot
async function handleStaffStart(chatId: number, text: string, firstName: string) {
  console.log(`[Staff Bot] handleStaffStart called with text: "${text}"`)
  console.log(`[Staff Bot] Text length: ${text.length}`)
  console.log(`[Staff Bot] Text characters: ${JSON.stringify(text.split(''))}`)

  const parts = text.split(' ')
  console.log(`[Staff Bot] Split parts: ${JSON.stringify(parts)}`)
  console.log(`[Staff Bot] Number of parts: ${parts.length}`)

  if (parts.length > 1) {
    // Handle invitation token
    const token = parts[1]
    console.log(`[Staff Bot] Received token: "${token}"`)
    console.log(`[Staff Bot] Token length: ${token.length}`)
    if (token.startsWith('invite_')) {
      // Extract actual token by removing 'invite_' prefix
      const actualToken = token.substring(7) // Remove 'invite_' prefix
      console.log(`[Staff Bot] Extracted token: "${actualToken}"`)
      await handleStaffInvitation(chatId, actualToken, firstName)
      return
    } else {
      console.log(`[Staff Bot] Token does not start with 'invite_': "${token}"`)
    }
  } else {
    console.log(`[Staff Bot] No token found in text, parts length: ${parts.length}`)
  }

  // Regular start message
  const welcomeMessage = `👋 Welcome to Loyal ET Staff Bot, ${firstName}!

🏢 This bot is designed for business staff members to manage operations.

⚠️ **Missing Invitation Link?**
If you clicked an invitation link but don't see the invitation processing, this might be due to your Telegram client. Please try:

1. **Copy the invitation link** and paste it in your browser
2. **Use Telegram Desktop** or **mobile app** instead of web
3. **Contact your manager** for a new invitation link

Or send me your invitation token manually in this format:
\`/invite YOUR_TOKEN_HERE\`

Available commands:
/help - Show all commands
/dashboard - View business metrics
/invite - Generate staff invitation links
/members - Member management (coming soon)
/settings - Business settings (coming soon)

⚠️ Note: You need to be registered as staff member to use most features.`

  await sendStaffMessage(chatId, welcomeMessage)
}

// Handle staff invitation acceptance
async function handleStaffInvitation(chatId: number, token: string, firstName: string) {
  try {
    console.log(`[Staff Bot] Processing invitation with token: ${token}`)
    console.log(`[Staff Bot] Token length: ${token.length}`)
    console.log(`[Staff Bot] Chat ID: ${chatId}`)

    const supabase = getServiceRoleClient()
    console.log(`[Staff Bot] Got Supabase client`)

    // Test database connection first
    try {
      const { error: testError } = await supabase
        .from('cashier_invitations')
        .select('count')
        .limit(1)

      console.log(`[Staff Bot] Database connection test:`, {
        success: !testError,
        error: testError?.message
      })
    } catch (dbError) {
      console.error(`[Staff Bot] Database connection failed:`, dbError)
      await sendStaffMessage(chatId, '❌ Database connection error. Please try again later.')
      return
    }

    // First, let's check if ANY invitations exist for debugging
    const { data: allInvitations, error: allError } = await supabase
      .from('cashier_invitations')
      .select('invitation_token, email, expires_at, used_at')
      .limit(5)

    console.log(`[Staff Bot] All invitations check:`, {
      count: allInvitations?.length || 0,
      error: allError,
      tokens: allInvitations?.map(inv => inv.invitation_token.substring(0, 8) + '...') || []
    })

    // Now try the specific query
    const currentTime = new Date().toISOString()
    console.log(`[Staff Bot] Current time: ${currentTime}`)

    const { data: invitation, error } = await supabase
      .from('cashier_invitations')
      .select('*')
      .eq('invitation_token', token)
      .is('used_at', null)
      .gt('expires_at', currentTime)
      .single()

    console.log(`[Staff Bot] Database lookup result:`, {
      invitation: !!invitation,
      error: error?.message || error,
      invitationId: invitation?.id,
      invitationEmail: invitation?.email,
      expiresAt: invitation?.expires_at
    })

    if (error || !invitation) {
      console.log(`[Staff Bot] Invalid invitation - error:`, error)
      console.log(`[Staff Bot] Searched for token: "${token}"`)
      await sendStaffMessage(chatId, '❌ Invalid or expired invitation link.')
      return
    }

    // Update invitation with Telegram chat ID (but don't mark as used yet)
    const { error: updateError } = await supabase
      .from('cashier_invitations')
      .update({
        telegram_chat_id: chatId.toString(),
        telegram_sent_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    if (updateError) {
      console.error('Error updating invitation:', updateError)
      await sendStaffMessage(chatId, '❌ Error processing invitation. Please try again.')
      return
    }

    await sendStaffMessage(chatId, `✅ Invitation Connected, ${firstName}!

🎯 Your Telegram is now linked to the cashier invitation.

📧 **Next Steps:**
1. Check your email (${invitation.email}) for the account setup link
2. Complete your cashier account registration
3. Return here for staff features

⚠️ **Important:** You need to complete the web registration to activate your account.

Commands available after registration:
/dashboard - View business metrics
/help - Show all commands`)

  } catch (error) {
    console.error('Error handling staff invitation:', error)
    await sendStaffMessage(chatId, '❌ Error processing invitation. Please contact your manager.')
  }
}

// Handle /help command
async function handleStaffHelp(chatId: number) {
  const helpMessage = `🤖 Loyal ET Staff Bot Commands:

👥 **Staff Management:**
/invite <role> <email> - Generate staff invitation
/dashboard - View business metrics
/members - Member management (coming soon)

⚙️ **Settings:**
/settings - Business configuration (coming soon)
/notifications - Manage alerts (coming soon)

📊 **Analytics:**
/reports - Generate reports (coming soon)
/transactions - Recent transactions (coming soon)

❓ **Support:**
/help - Show this message
/contact - Contact support (coming soon)

💡 **Tip:** Use /dashboard to get started with business overview.`

  await sendStaffMessage(chatId, helpMessage)
}

// Handle /dashboard command
async function handleStaffDashboard(chatId: number) {
  try {
    // TODO: Verify user is staff member
    // For now, show basic info
    const dashboardMessage = `📊 **Business Dashboard**

📈 **Quick Stats:**
• Total Members: Loading...
• Points Issued: Loading...
• Active Rewards: Loading...

🔄 **Recent Activity:**
• New members today: Loading...
• Transactions: Loading...

⚡ **Quick Actions:**
/members - Manage members
/invite - Invite new staff
/reports - View detailed reports

*Full dashboard integration coming soon!*`

    await sendStaffMessage(chatId, dashboardMessage)
  } catch (error) {
    console.error('Error in staff dashboard:', error)
    await sendStaffMessage(chatId, '❌ Error loading dashboard. Please try again.')
  }
}

// Handle /invite command
async function handleStaffInvite(chatId: number, text: string) {
  const parts = text.split(' ')

  if (parts.length < 3) {
    await sendStaffMessage(chatId, `📧 **Staff Invitation Generator**

Usage: /invite <role> <email>

**Available Roles:**
• cashier - Can process transactions
• manager - Can manage staff and settings

**Example:**
/<NAME_EMAIL>`)
    return
  }

  const role = parts[1].toLowerCase()
  const email = parts[2]

  if (!['cashier', 'manager'].includes(role)) {
    await sendStaffMessage(chatId, '❌ Invalid role. Use: cashier or manager')
    return
  }

  // TODO: Generate invitation link
  await sendStaffMessage(chatId, `✅ **Invitation Generated**

📧 Email: ${email}
👤 Role: ${role}

📱 Invitation link:
t.me/Loyal_ET_staff_bot?start=invite_SAMPLE_TOKEN

*Full implementation coming soon!*`)
}

// Send message using staff bot token
async function sendStaffMessage(chatId: number, text: string) {
  const token = process.env.TELEGRAM_STAFF_BOT_TOKEN

  if (!token) {
    console.error('Staff bot token not configured')
    return
  }

  try {
    const response = await fetch(`https://api.telegram.org/bot${token}/sendMessage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        chat_id: chatId,
        text,
        parse_mode: 'Markdown'
      }),
    })

    if (!response.ok) {
      console.error('Failed to send staff message:', await response.text())
    }
  } catch (error) {
    console.error('Error sending staff message:', error)
  }
}
