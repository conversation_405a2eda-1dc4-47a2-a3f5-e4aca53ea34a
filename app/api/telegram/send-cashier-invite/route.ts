import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN

interface TelegramMessageOptions {
  reply_markup?: object
  disable_web_page_preview?: boolean
  disable_notification?: boolean
}

async function sendTelegramMessage(chatId: string, text: string, options?: TelegramMessageOptions) {
  if (!TELEGRAM_BOT_TOKEN) {
    throw new Error('Telegram bot token not configured')
  }

  const url = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      chat_id: chatId,
      text,
      parse_mode: 'HTML',
      ...options,
    }),
  })

  if (!response.ok) {
    const error = await response.text()
    throw new Error(`Telegram API error: ${error}`)
  }

  return response.json()
}

export async function POST(request: Request) {
  try {
    const {
      telegramChatId,
      ownerName,
      companyName,
      inviteData
    } = await request.json()

    if (!telegramChatId || !ownerName || !companyName || !inviteData) {
      return NextResponse.json({
        error: 'Missing required fields: telegramChatId, ownerName, companyName, inviteData'
      }, { status: 400 })
    }

    const supabase = await createClient()

    // Verify the invitation exists and is valid
    const { data: invitation, error: inviteError } = await supabase
      .from('cashier_invitations')
      .select(`
        *,
        companies!inner(name, id)
      `)
      .eq('id', inviteData.id)
      .eq('status', 'pending')
      .single()

    if (inviteError || !invitation) {
      return NextResponse.json({
        error: 'Invalid or expired invitation'
      }, { status: 404 })
    }

    // Generate the invitation accept link
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://loyal-et.vercel.app'
    const acceptLink = `${baseUrl}/cashiers/accept?token=${invitation.token}`

    // Create QR code data URL (we'll generate this on the frontend)
    const qrCodeUrl = `${baseUrl}/api/qr-code?data=${encodeURIComponent(acceptLink)}`

    // Prepare Telegram message
    const message = `🎉 <b>Cashier Invitation</b> 🎉

Hello! ${ownerName} has invited you to join <b>${companyName}</b> as a cashier.

<b>Invitation Details:</b>
📧 Email: ${invitation.email}
🏢 Company: ${companyName}
👤 Role: Cashier
⏰ Expires: ${new Date(invitation.expires_at).toLocaleDateString()}

<b>What you can do as a cashier:</b>
• Process customer transactions
• Award loyalty points
• Handle reward redemptions
• View customer information
• Access sales reports

To accept this invitation, click the button below and complete your registration.`

    const replyMarkup = {
      inline_keyboard: [
        [
          {
            text: '✅ Accept Invitation',
            url: acceptLink
          }
        ],
        [
          {
            text: '📱 View QR Code',
            url: qrCodeUrl
          }
        ]
      ]
    }

    // Send the invitation message
    await sendTelegramMessage(telegramChatId, message, {
      reply_markup: replyMarkup
    })

    // Update invitation with telegram delivery info
    await supabase
      .from('cashier_invitations')
      .update({
        telegram_sent_at: new Date().toISOString(),
        telegram_chat_id: telegramChatId
      })
      .eq('id', invitation.id)

    return NextResponse.json({
      success: true,
      message: 'Cashier invitation sent successfully via Telegram',
      invitation: {
        id: invitation.id,
        email: invitation.email,
        acceptLink,
        qrCodeUrl
      }
    })

  } catch (error) {
    console.error('Error sending Telegram cashier invitation:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Failed to send invitation'
    }, { status: 500 })
  }
}
