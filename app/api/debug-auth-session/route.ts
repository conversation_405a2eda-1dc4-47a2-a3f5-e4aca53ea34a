import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error } = await supabase.auth.getUser()

    // Get all cookies for debugging
    const cookies = request.cookies.getAll()

    console.log('=== DEBUG AUTH ===')
    console.log('All cookies:', cookies.map(c => ({ name: c.name, value: c.value.substring(0, 20) + '...' })))
    console.log('User from getUser():', user ? { id: user.id, email: user.email, app_metadata: user.app_metadata } : null)
    console.log('Error:', error)
    console.log('==================')

    if (error || !user) {
      return NextResponse.json({
        error: 'Not authenticated',
        cookies: cookies.length,
        supabaseError: error?.message
      }, { status: 401 })
    }

    const isSuperAdmin = user.app_metadata?.is_super_admin === true ||
                        user.email === '<EMAIL>'

    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        app_metadata: user.app_metadata
      },
      isSuperAdmin,
      cookieCount: cookies.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Debug auth error:', error)
    return NextResponse.json({
      error: 'Server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
