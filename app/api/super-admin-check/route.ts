import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({
        isSuperAdmin: false,
        user: null
      })
    }

    // Check if user is super admin from app_metadata
    const isSuperAdmin = user.app_metadata?.is_super_admin === true ||
                        user.email === '<EMAIL>' // Hardcode your email as fallback

    return NextResponse.json({
      isSuperAdmin,
      user: {
        id: user.id,
        email: user.email
      }
    })

  } catch (error) {
    console.error('Error checking super admin status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
