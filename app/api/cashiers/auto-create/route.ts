import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { generateTemporaryPassword } from '@/lib/temp-password-generator'

// Validation schema for auto-creating cashier accounts
const autoCreateSchema = z.object({
  token: z.string().min(1, 'Invitation token is required'),
})

/**
 * POST /api/cashiers/auto-create
 * Auto-creates a cashier account with a temporary password
 */
export async function POST(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()

    const body = await request.json()
    const validation = autoCreateSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { token } = validation.data

    // Find the invitation
    const { data: invitation, error: invitationError } = await serviceClient
      .from('cashier_invitations')
      .select(`
        id,
        company_id,
        email,
        expires_at,
        used_at,
        invited_by
      `)
      .eq('invitation_token', token)
      .maybeSingle()

    if (invitationError || !invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' },
        { status: 400 }
      )
    }

    // Check if invitation is already used
    if (invitation.used_at) {
      return NextResponse.json(
        { error: 'This invitation has already been used' },
        { status: 400 }
      )
    }

    // Check if invitation is expired
    if (new Date(invitation.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'This invitation has expired' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const { data: existingUsers } = await serviceClient.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === invitation.email)

    let userId: string
    let tempPassword: string = ''

    if (existingUser) {
      // User exists, generate a new temporary password and update their password
      userId = existingUser.id
      tempPassword = generateTemporaryPassword(12)

      // Check if they're already associated with this company
      const { data: existingAdmin } = await serviceClient
        .from('company_administrators')
        .select('role')
        .eq('administrator_id', userId)
        .eq('company_id', invitation.company_id)
        .maybeSingle()

      if (existingAdmin) {
        return NextResponse.json(
          { error: 'User is already associated with this company' },
          { status: 400 }
        )
      }

      // Update existing user's password with new temporary password
      const { error: updatePasswordError } = await serviceClient.auth.admin.updateUserById(
        userId,
        {
          password: tempPassword,
          user_metadata: {
            ...existingUser.user_metadata,
            password_change_required: true,
            company_id: invitation.company_id
          }
        }
      )

      if (updatePasswordError) {
        console.error('Error updating user password:', updatePasswordError)
        return NextResponse.json(
          { error: 'Failed to update user password' },
          { status: 500 }
        )
      }

      // Ensure user exists in administrators table
      await serviceClient
        .from('administrators')
        .upsert({
          id: userId,
          email: invitation.email,
          name: existingUser.user_metadata?.full_name || invitation.email.split('@')[0],
          is_active: true
        })
        .select()
        .single()
    } else {
      // Generate a secure temporary password
      tempPassword = generateTemporaryPassword(12)

      // Create new user account with temporary password
      const { data: newUser, error: authError } = await serviceClient.auth.admin.createUser({
        email: invitation.email,
        password: tempPassword,
        email_confirm: true,
        user_metadata: {
          role: 'cashier',
          password_change_required: true,
          company_id: invitation.company_id
        }
      })

      if (authError || !newUser.user) {
        console.error('Error creating user account:', authError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      userId = newUser.user.id

      // Add to administrators table
      await serviceClient
        .from('administrators')
        .insert({
          id: userId,
          email: invitation.email,
          name: newUser.user.user_metadata?.full_name || invitation.email.split('@')[0],
          is_active: true
        })
        .select()
        .single()
    }

    // Add user as cashier to the company
    const { error: adminError } = await serviceClient
      .from('company_administrators')
      .insert({
        administrator_id: userId,
        company_id: invitation.company_id,
        role: 'CASHIER',
        created_by: invitation.invited_by
      })

    if (adminError) {
      console.error('Error adding user as cashier:', adminError)
      return NextResponse.json(
        { error: 'Failed to add user as cashier' },
        { status: 500 }
      )
    }

    // Mark invitation as used
    const { error: updateError } = await serviceClient
      .from('cashier_invitations')
      .update({
        used_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    if (updateError) {
      console.error('Error marking invitation as used:', updateError)
      // Continue anyway, the important part is done
    }

    // Get company name separately to avoid join issues
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', invitation.company_id)
      .maybeSingle()

    // Log the temporary password for debugging (remove in production)
    console.log('Generated temporary password:', tempPassword)

    return NextResponse.json({
      success: true,
      message: 'Account created successfully',
      user: {
        email: invitation.email,
        tempPassword: tempPassword, // Ensure this is always included
        role: 'CASHIER',
        companyName: company?.name || 'Unknown Company',
        requiresPasswordChange: true
      }
    })

  } catch (error) {
    console.error('Error auto-creating cashier account:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
