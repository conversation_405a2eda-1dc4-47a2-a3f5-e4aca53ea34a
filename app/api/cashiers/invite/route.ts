import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { getUserRole, getUserIdFromSession } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'

// Validation schemas
const inviteSchema = z.object({
  email: z.string().email('Valid email is required'),
  companyId: z.string().uuid('Valid company ID is required'),
  telegramChatId: z.string().optional(), // Optional Telegram chat ID for direct invite
})

// POST /api/cashiers/invite - Business admin invites a cashier
export async function POST(request: NextRequest) {
  try {
    // Use server-side client for auth
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()

    const body = await request.json()
    const validation = inviteSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { email, companyId, telegramChatId } = validation.data

    // Get current user ID
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Verify user is an owner of the company
    const role = await getUserRole(serviceClient, companyId)
    if (role !== 'OWNER') {
      console.log(`POST /api/cashiers/invite - User ${userId} is not an owner of company ${companyId}`)
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Add debug logging
    console.log(`POST /api/cashiers/invite - User ${userId} is authorized as ${role} for company ${companyId}`)

    // Check if email is already a user in the system
    const { data: existingUsers } = await serviceClient.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === email)

    if (existingUser) {
      // Check if they're already associated with this company
      const { data: existingAdmin } = await serviceClient
        .from('company_administrators')
        .select('role')
        .eq('administrator_id', existingUser.id)
        .eq('company_id', companyId)
        .single()

      if (existingAdmin) {
        return NextResponse.json(
          { error: `User is already a ${existingAdmin.role.toLowerCase()} for this company` },
          { status: 400 }
        )
      }
    }

    // Generate invitation token
    const { data: tokenResult } = await serviceClient
      .rpc('generate_invitation_token')

    if (!tokenResult) {
      return NextResponse.json(
        { error: 'Failed to generate invitation token' },
        { status: 500 }
      )
    }

    const invitationToken = tokenResult
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // Expires in 7 days

    // Create invitation record
    const { data: invitation, error: insertError } = await serviceClient
      .from('cashier_invitations')
      .insert({
        company_id: companyId,
        invited_by: userId,
        email,
        invitation_token: invitationToken,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single()

    if (insertError) {
      if (insertError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'Invitation already sent to this email for this company' },
          { status: 400 }
        )
      }

      console.error('Error creating invitation:', insertError)
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 }
      )
    }

    // Get company details for Telegram invitation
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', companyId)
      .single()

    // Get inviting user details
    const { data: invitingUser } = await serviceClient
      .from('profiles')
      .select('full_name')
      .eq('id', userId)
      .single()

    const invitationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/cashiers/accept?token=${invitationToken}`

    let telegramSent = false
    let telegramError = null

    // Send Telegram invitation if chat ID provided
    if (telegramChatId) {
      try {
        const telegramResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/telegram/send-cashier-invite`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            telegramChatId,
            ownerName: invitingUser?.full_name || 'Business Owner',
            companyName: company?.name || 'Company',
            inviteData: {
              id: invitation.id,
              token: invitationToken
            }
          }),
        })

        if (telegramResponse.ok) {
          telegramSent = true
        } else {
          const errorData = await telegramResponse.json()
          telegramError = errorData.error
        }
      } catch (error) {
        console.error('Failed to send Telegram invitation:', error)
        telegramError = error instanceof Error ? error.message : 'Failed to send Telegram invitation'
      }
    }

    return NextResponse.json({
      message: 'Invitation created successfully',
      invitation: {
        id: invitation.id,
        email: invitation.email,
        expiresAt: invitation.expires_at,
        invitationLink,
      },
      telegram: {
        sent: telegramSent,
        error: telegramError,
        chatId: telegramChatId
      }
    })

  } catch (error) {
    console.error('Error in cashier invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/cashiers/invite - List pending invitations for a company
export async function GET(request: NextRequest) {
  try {
    // Use server-side client for auth
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Get current user ID
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is a company owner
    const userRole = await getUserRole(supabase, companyId)
    if (userRole !== 'OWNER') {
      return NextResponse.json(
        { error: 'Only company owners can view invitations' },
        { status: 403 }
      )
    }

    // Get pending invitations
    const { data: invitations, error } = await serviceClient
      .from('cashier_invitations')
      .select(`
        id,
        email,
        created_at,
        expires_at,
        used_at
      `)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching invitations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch invitations' },
        { status: 500 }
      )
    }

    return NextResponse.json({ invitations })

  } catch (error) {
    console.error('Error fetching invitations:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/cashiers/invite/:id - Cancel an invitation
export async function DELETE(request: NextRequest) {
  try {
    // Use server-side client for auth
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()

    const url = new URL(request.url)
    const invitationId = url.pathname.split('/').pop()

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      )
    }

    // Get current user ID
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get invitation details to verify permissions
    const { data: invitation, error: fetchError } = await serviceClient
      .from('cashier_invitations')
      .select('company_id, email, used_at')
      .eq('id', invitationId)
      .single()

    if (fetchError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      )
    }

    // Check if user is a company owner
    const userRole = await getUserRole(supabase, invitation.company_id)
    if (userRole !== 'OWNER') {
      return NextResponse.json(
        { error: 'Only company owners can cancel invitations' },
        { status: 403 }
      )
    }

    // Check if invitation is already used
    if (invitation.used_at) {
      return NextResponse.json(
        { error: 'Cannot cancel an invitation that has already been accepted' },
        { status: 400 }
      )
    }

    // Delete the invitation
    const { error: deleteError } = await serviceClient
      .from('cashier_invitations')
      .delete()
      .eq('id', invitationId)

    if (deleteError) {
      console.error('Error deleting invitation:', deleteError)
      return NextResponse.json(
        { error: 'Failed to cancel invitation' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Invitation cancelled successfully'
    })

  } catch (error) {
    console.error('Error cancelling invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
