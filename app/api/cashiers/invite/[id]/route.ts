import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { getUserRole, getUserIdFromSession } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'

// DELETE /api/cashiers/invite/[id] - Cancel an invitation
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create server-side client for auth session
    const supabase = await createClient()
    // Use service client for admin operations
    const serviceClient = getServiceRoleClient()
    
    const resolvedParams = await params
    const invitationId = resolvedParams.id
    
    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      )
    }

    console.log(`DELETE /api/cashiers/invite/${invitationId} - Processing request`)

    // Get current user ID from the session
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      console.log(`DELETE /api/cashiers/invite/${invitationId} - Unauthorized, no user ID`)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get invitation details to verify permissions
    const { data: invitation, error: fetchError } = await serviceClient
      .from('cashier_invitations')
      .select('company_id, email, used_at')
      .eq('id', invitationId)
      .single()

    if (fetchError || !invitation) {
      console.log(`DELETE /api/cashiers/invite/${invitationId} - Invitation not found`)
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      )
    }

    // Check if user is a company owner
    const userRole = await getUserRole(supabase, invitation.company_id)
    if (userRole !== 'OWNER') {
      console.log(`DELETE /api/cashiers/invite/${invitationId} - User ${userId} is not an owner`)
      return NextResponse.json(
        { error: 'Only company owners can cancel invitations' },
        { status: 403 }
      )
    }

    // Check if invitation has already been used
    if (invitation.used_at) {
      console.log(`DELETE /api/cashiers/invite/${invitationId} - Invitation already used`)
      return NextResponse.json(
        { error: 'Cannot cancel an invitation that has already been used' },
        { status: 400 }
      )
    }

    // Delete the invitation
    const { error: deleteError } = await serviceClient
      .from('cashier_invitations')
      .delete()
      .eq('id', invitationId)

    if (deleteError) {
      console.error(`DELETE /api/cashiers/invite/${invitationId} - Error deleting:`, deleteError)
      return NextResponse.json(
        { error: 'Failed to delete invitation' },
        { status: 500 }
      )
    }

    console.log(`DELETE /api/cashiers/invite/${invitationId} - Successfully deleted invitation`)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in DELETE /api/cashiers/invite/[id]:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
