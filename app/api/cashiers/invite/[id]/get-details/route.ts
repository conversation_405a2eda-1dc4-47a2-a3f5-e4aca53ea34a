import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { getUserRole, getUserIdFromSession } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'

// GET /api/cashiers/invite/[id]/get-details - Get invitation details for viewing
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Create server-side client for auth session
    const supabase = await createClient()
    // Use service client for admin operations
    const serviceClient = getServiceRoleClient()

    const resolvedParams = await params
    const invitationId = resolvedParams.id

    if (!invitationId) {
      return NextResponse.json(
        { error: 'Invitation ID is required' },
        { status: 400 }
      )
    }

    console.log(`GET /api/cashiers/invite/${invitationId}/get-details - Processing request`)

    // Get current user ID from the session
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      console.log(`GET /api/cashiers/invite/${invitationId}/get-details - Unauthorized, no user ID`)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get invitation details
    const { data: invitation, error: fetchError } = await serviceClient
      .from('cashier_invitations')
      .select('id, company_id, email, invitation_token, created_at, expires_at, used_at')
      .eq('id', invitationId)
      .single()

    if (fetchError || !invitation) {
      console.log(`GET /api/cashiers/invite/${invitationId}/get-details - Invitation not found`)
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      )
    }

    // Check if user is a company owner
    const userRole = await getUserRole(supabase, invitation.company_id)
    if (userRole !== 'OWNER') {
      console.log(`GET /api/cashiers/invite/${invitationId}/get-details - User ${userId} is not an owner`)
      return NextResponse.json(
        { error: 'Only company owners can view invitation details' },
        { status: 403 }
      )
    }

    // Get company details for Telegram invitation
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', invitation.company_id)
      .single()

    // Generate invitation link
    const invitationLink = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/cashiers/accept?token=${invitation.invitation_token}`

    // Generate Telegram link
    const telegramLink = `https://t.me/Loyal_ET_staff_bot?start=invite_${invitation.invitation_token}`

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitation.id,
        email: invitation.email,
        createdAt: invitation.created_at,
        expiresAt: invitation.expires_at,
        usedAt: invitation.used_at,
        invitationLink,
        telegramLink,
        companyName: company?.name || 'Your Business'
      }
    })
  } catch (error) {
    console.error('Error in GET /api/cashiers/invite/[id]/get-details:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
