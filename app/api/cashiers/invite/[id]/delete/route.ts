import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()
    const { id: invitationId } = await params

    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get invitation details to verify ownership
    const { data: invitation, error: invitationError } = await supabase
      .from('cashier_invitations')
      .select('id, company_id, email, used_at, invited_by')
      .eq('id', invitationId)
      .single()

    if (invitationError || !invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      )
    }

    // Verify user has permission to delete this invitation
    const { data: userRole } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('administrator_id', user.id)
      .eq('company_id', invitation.company_id)
      .single()

    if (!userRole || userRole.role !== 'OWNER') {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // If invitation was used, also remove the user from company_administrators
    if (invitation.used_at) {
      // Find the user who accepted this invitation
      const { data: invitedUser } = await supabase.auth.admin.listUsers()
      const acceptedUser = invitedUser.users.find(u => u.email === invitation.email)
      
      if (acceptedUser) {
        // Remove from company_administrators
        await supabase
          .from('company_administrators')
          .delete()
          .eq('administrator_id', acceptedUser.id)
          .eq('company_id', invitation.company_id)

        // Optionally remove from administrators table if they're not in other companies
        const { data: otherCompanies } = await supabase
          .from('company_administrators')
          .select('id')
          .eq('administrator_id', acceptedUser.id)

        if (!otherCompanies || otherCompanies.length === 0) {
          await supabase
            .from('administrators')
            .delete()
            .eq('id', acceptedUser.id)
        }
      }
    }

    // Delete the invitation
    const { error: deleteError } = await supabase
      .from('cashier_invitations')
      .delete()
      .eq('id', invitationId)

    if (deleteError) {
      console.error('Error deleting invitation:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete invitation' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: invitation.used_at 
        ? 'Invitation deleted and user access revoked successfully'
        : 'Invitation deleted successfully'
    })

  } catch (error) {
    console.error('Error in delete invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
