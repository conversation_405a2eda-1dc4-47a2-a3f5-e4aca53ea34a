import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'
import { getUserRole, getUserIdFromSession } from '@/lib/auth'
import { createClient } from '@/lib/supabase/server'
import { randomBytes } from 'crypto'

// Validation schema for seamless cashier invitation
const seamlessInviteSchema = z.object({
  email: z.string().email('Valid email is required'),
  name: z.string().optional(),
  companyId: z.string().uuid('Valid company ID is required'),
})

// POST /api/cashiers/invite-seamless - Create seamless cashier invitation
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const serviceClient = getServiceRoleClient()

    const body = await request.json()
    const validation = seamlessInviteSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { email, companyId } = validation.data

    // Get current user ID
    const userId = await getUserIdFromSession(supabase)
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is a company owner
    const userRole = await getUserRole(supabase, companyId)
    if (userRole !== 'OWNER') {
      return NextResponse.json(
        { error: 'Only company owners can invite cashiers' },
        { status: 403 }
      )
    }

    // Check if email is already a user in the system
    const { data: existingUsers } = await serviceClient.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === email)

    if (existingUser) {
      // Check if they're already associated with this company
      const { data: existingAdmin } = await serviceClient
        .from('company_administrators')
        .select('role')
        .eq('administrator_id', existingUser.id)
        .eq('company_id', companyId)
        .single()

      if (existingAdmin) {
        return NextResponse.json(
          { error: `User is already a ${existingAdmin.role.toLowerCase()} for this company` },
          { status: 400 }
        )
      }
    }

    // Generate unique invitation token
    const invitationToken = randomBytes(16).toString('hex')
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7) // Expires in 7 days

    // Create invitation record
    const { data: invitation, error: insertError } = await serviceClient
      .from('cashier_invitations')
      .insert({
        company_id: companyId,
        invited_by: userId,
        email,
        invitation_token: invitationToken,
        expires_at: expiresAt.toISOString(),
      })
      .select()
      .single()

    if (insertError) {
      if (insertError.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'Invitation already sent to this email for this company' },
          { status: 400 }
        )
      }

      console.error('Failed to create cashier invitation:', insertError)
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 }
      )
    }

    // Create Telegram deep link using the staff bot for cashier invitations
    const botUsername = process.env.TELEGRAM_STAFF_BOT_USERNAME || 'Loyal_ET_staff_bot'
    if (!botUsername) {
      return NextResponse.json(
        { error: 'Staff bot not configured' },
        { status: 500 }
      )
    }

    // Use staff bot with invite prefix for cashier invitations
    const telegramLink = `https://t.me/${botUsername}?start=invite_${invitationToken}`
    
    // Create direct invitation link for cashier signup
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    const invitationLink = `${baseUrl}/cashiers/accept?token=${invitationToken}`

    // Get company info for notification purposes
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', companyId)
      .single()

    return NextResponse.json({
      success: true,
      invitation: {
        id: invitation.id,
        email,
        expiresAt: invitation.expires_at,
        invitationToken,
        invitationLink,
        telegramLink
      },
      message: `Seamless cashier invitation created for ${email}`,
      companyName: company?.name || 'Unknown Company'
    })

  } catch (error) {
    console.error('Seamless cashier invitation error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}

// GET /api/cashiers/invite-seamless - Check invitation status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json({ error: 'Token required' }, { status: 400 })
    }

    const serviceClient = getServiceRoleClient()

    // Check if invitation exists and is valid
    const { data: invitation, error } = await serviceClient
      .from('cashier_invitations')
      .select(`
        *,
        companies(name),
        company_administrators!invited_by(users(name, email))
      `)
      .eq('invitation_token', token)
      .gt('expires_at', new Date().toISOString())
      .is('used_at', null)
      .single()

    if (error || !invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      valid: true,
      invitation: {
        id: invitation.id,
        email: invitation.email,
        companyName: invitation.companies.name,
        invitedBy: invitation.company_administrators.users.name,
        expiresAt: invitation.expires_at
      }
    })

  } catch (error) {
    console.error('Invitation check error:', error)
    return NextResponse.json(
      { error: 'Internal error' },
      { status: 500 }
    )
  }
}
