import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { getServiceRoleClient } from '@/lib/supabase'

const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Invitation token is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
})

// POST /api/cashiers/accept - Accept a cashier invitation
export async function POST(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()

    const body = await request.json()
    const validation = acceptInvitationSchema.safeParse(body)

    if (!validation.success) {
      return NextResponse.json(
        { error: 'Invalid input', details: validation.error.format() },
        { status: 400 }
      )
    }

    const { token, password, name } = validation.data

    // Find the invitation
    const { data: invitation, error: invitationError } = await serviceClient
      .from('cashier_invitations')
      .select(`
        id,
        company_id,
        email,
        expires_at,
        used_at
      `)
      .eq('invitation_token', token)
      .maybeSingle()

    if (invitationError || !invitation) {
      return NextResponse.json(
        { error: 'Invalid or expired invitation token' },
        { status: 400 }
      )
    }

    // Check if invitation is already used
    if (invitation.used_at) {
      return NextResponse.json(
        { error: 'This invitation has already been used' },
        { status: 400 }
      )
    }

    // Check if invitation is expired
    if (new Date(invitation.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'This invitation has expired' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const { data: existingUsers } = await serviceClient.auth.admin.listUsers()
    const existingUser = existingUsers?.users?.find(user => user.email === invitation.email)

    let userId: string

    if (existingUser) {
      // User exists, just need to add them to the company
      userId = existingUser.id

      // Check if they're already associated with this company
      const { data: existingAdmin } = await serviceClient
        .from('company_administrators')
        .select('role')
        .eq('administrator_id', userId)
        .eq('company_id', invitation.company_id)
        .single()

      if (existingAdmin) {
        return NextResponse.json(
          { error: 'You are already associated with this company' },
          { status: 400 }
        )
      }
    } else {
      // Create new user account
      const { data: newUser, error: authError } = await serviceClient.auth.admin.createUser({
        email: invitation.email,
        password,
        email_confirm: true,
        user_metadata: {
          full_name: name,
          role: 'cashier'
        }
      })

      if (authError || !newUser.user) {
        console.error('Error creating user account:', authError)
        return NextResponse.json(
          { error: 'Failed to create user account' },
          { status: 500 }
        )
      }

      userId = newUser.user.id

      // Add to administrators table if it exists
      await serviceClient
        .from('administrators')
        .insert({
          id: userId,
          email: invitation.email,
          name,
          is_active: true
        })
        .select()
        .single()
    }

    // Add user as cashier to the company
    const { error: adminError } = await serviceClient
      .from('company_administrators')
      .insert({
        administrator_id: userId,
        company_id: invitation.company_id,
        role: 'CASHIER'
      })

    if (adminError) {
      console.error('Error adding user as cashier:', adminError)
      return NextResponse.json(
        { error: 'Failed to add user as cashier' },
        { status: 500 }
      )
    }

    // Mark invitation as used
    const { error: updateError } = await serviceClient
      .from('cashier_invitations')
      .update({
        used_at: new Date().toISOString()
      })
      .eq('id', invitation.id)

    if (updateError) {
      console.error('Error marking invitation as used:', updateError)
      // Continue anyway, the important part is done
    }

    // Get company name separately to avoid join issues
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', invitation.company_id)
      .maybeSingle()

    return NextResponse.json({
      message: 'Invitation accepted successfully',
      user: {
        email: invitation.email,
        name,
        role: 'CASHIER',
        companyName: company?.name || 'Unknown Company'
      }
    })

  } catch (error) {
    console.error('Error accepting invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/cashiers/accept?token=xxx - Get invitation details
export async function GET(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()

    const { searchParams } = new URL(request.url)
    const token = searchParams.get('token')

    if (!token) {
      return NextResponse.json(
        { error: 'Invitation token is required' },
        { status: 400 }
      )
    }

    // Find the invitation first
    const { data: invitation, error: invitationError } = await serviceClient
      .from('cashier_invitations')
      .select('email, expires_at, used_at, company_id')
      .eq('invitation_token', token)
      .maybeSingle()

    if (invitationError) {
      console.error('Error fetching invitation:', invitationError)
      return NextResponse.json(
        { error: 'Database error while fetching invitation' },
        { status: 500 }
      )
    }

    if (!invitation) {
      return NextResponse.json(
        { error: 'Invalid invitation token' },
        { status: 400 }
      )
    }

    // Check if invitation is already used
    if (invitation.used_at) {
      return NextResponse.json(
        { error: 'This invitation has already been used' },
        { status: 400 }
      )
    }

    // Check if invitation is expired
    if (new Date(invitation.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'This invitation has expired' },
        { status: 400 }
      )
    }

    // Get company name separately to avoid join issues
    const { data: company } = await serviceClient
      .from('companies')
      .select('name')
      .eq('id', invitation.company_id)
      .maybeSingle()

    return NextResponse.json({
      invitation: {
        email: invitation.email,
        companyName: company?.name || 'Unknown Company',
        expiresAt: invitation.expires_at,
        invitedBy: 'Admin' // We can enhance this later if needed
      }
    })

  } catch (error) {
    console.error('Error fetching invitation:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
