import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createClient as createServiceClient } from '@supabase/supabase-js'

// GET /api/cashiers - List all cashiers for a company
export async function GET(request: NextRequest) {
  try {
    // Use proper server-side client for authentication
    const supabase = await createClient()

    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    // Get current user with proper server auth
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      console.error('Auth error:', userError)
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('API: Authenticated user:', user.id, user.email)

    // Check if user is a company owner using the authenticated client
    const { data: userRole, error: roleError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('company_id', companyId)
      .eq('administrator_id', user.id)
      .single()

    console.log('API: User role check:', { userRole, roleError })

    if (roleError || !userRole || userRole.role !== 'OWNER') {
      return NextResponse.json(
        { error: 'Only company owners can view cashiers' },
        { status: 403 }
      )
    }

    // Use service role client to fetch cashiers (bypasses RLS)
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Get all cashiers for the company with manual join
    const { data: cashierRoles, error: cashierError } = await serviceClient
      .from('company_administrators')
      .select('id, created_at, role, administrator_id')
      .eq('company_id', companyId)
      .eq('role', 'CASHIER')
      .order('created_at', { ascending: false })

    if (cashierError) {
      console.error('Error fetching cashier roles:', cashierError)
      return NextResponse.json(
        { error: 'Failed to fetch cashiers' },
        { status: 500 }
      )
    }

    // Get administrator details for each cashier
    const cashiers = []
    if (cashierRoles && cashierRoles.length > 0) {
      const adminIds = cashierRoles.map(c => c.administrator_id)

      const { data: administrators, error: adminError } = await serviceClient
        .from('administrators')
        .select('id, name, email, is_active')
        .in('id', adminIds)

      if (adminError) {
        console.error('Error fetching administrator details:', adminError)
        return NextResponse.json(
          { error: 'Failed to fetch administrator details' },
          { status: 500 }
        )
      }

      // Combine the data
      for (const cashierRole of cashierRoles) {
        const admin = administrators?.find(a => a.id === cashierRole.administrator_id)
        cashiers.push({
          id: cashierRole.id,
          administratorId: cashierRole.administrator_id,
          name: admin?.name || 'Unknown',
          email: admin?.email || 'Unknown',
          isActive: admin?.is_active || false,
          createdAt: cashierRole.created_at,
        })
      }
    }

    console.log('API: Found cashiers:', cashiers)

    return NextResponse.json({
      cashiers
    })

  } catch (error) {
    console.error('Error fetching cashiers:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE /api/cashiers/:id - Remove a cashier from company
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()

    const url = new URL(request.url)
    const cashierId = url.pathname.split('/').pop()

    if (!cashierId) {
      return NextResponse.json(
        { error: 'Cashier ID is required' },
        { status: 400 }
      )
    }

    // Get current user with proper server auth
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Use service role client for operations
    const serviceClient = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    // Get cashier details to verify permissions
    const { data: cashier, error: fetchError } = await serviceClient
      .from('company_administrators')
      .select('company_id, role, administrator_id')
      .eq('id', cashierId)
      .single()

    if (fetchError || !cashier) {
      return NextResponse.json(
        { error: 'Cashier not found' },
        { status: 404 }
      )
    }

    // Check if user is a company owner
    const { data: userRole, error: roleError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('company_id', cashier.company_id)
      .eq('administrator_id', user.id)
      .single()

    if (roleError || !userRole || userRole.role !== 'OWNER') {
      return NextResponse.json(
        { error: 'Only company owners can remove cashiers' },
        { status: 403 }
      )
    }

    // Ensure we're only removing cashiers, not owners
    if (cashier.role !== 'CASHIER') {
      return NextResponse.json(
        { error: 'Can only remove cashiers, not company owners' },
        { status: 400 }
      )
    }

    // Remove the cashier
    const { error: deleteError } = await serviceClient
      .from('company_administrators')
      .delete()
      .eq('id', cashierId)

    if (deleteError) {
      console.error('Error removing cashier:', deleteError)
      return NextResponse.json(
        { error: 'Failed to remove cashier' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: 'Cashier removed successfully'
    })

  } catch (error) {
    console.error('Error removing cashier:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
