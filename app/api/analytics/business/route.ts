import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')
    const type = searchParams.get('type') || 'summary'

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Verify user has access to this company
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin of this company
    const { data: adminCheck } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('company_id', companyId)
      .eq('user_id', user.id)
      .maybeSingle()

    const { data: ownerCheck } = await supabase
      .from('companies')
      .select('administrator_id')
      .eq('id', companyId)
      .eq('administrator_id', user.id)
      .maybeSingle()

    if (!adminCheck && !ownerCheck) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    switch (type) {
      case 'summary':
      default:
        // Get basic business summary
        const { data: memberCount } = await supabase
          .from('loyalty_members')
          .select('id', { count: 'exact' })
          .eq('company_id', companyId)

        const { data: transactionCount } = await supabase
          .from('points_transactions')
          .select('id', { count: 'exact' })
          .eq('company_id', companyId)

        const { data: receiptData } = await supabase
          .from('receipts')
          .select('total_amount')
          .eq('company_id', companyId)

        const totalRevenue = receiptData?.reduce((sum, r) => sum + (r.total_amount || 0), 0) || 0
        const avgOrderValue = receiptData?.length ? totalRevenue / receiptData.length : 0

        return NextResponse.json({
          data: {
            total_members: memberCount?.length || 0,
            total_transactions: transactionCount?.length || 0,
            total_receipts: receiptData?.length || 0,
            total_revenue: totalRevenue,
            avg_order_value: avgOrderValue
          }
        })
    }
  } catch (error) {
    console.error('Business analytics API error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch business analytics' },
      { status: 500 }
    )
  }
}
