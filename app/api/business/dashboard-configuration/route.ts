import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

interface DashboardWidgetUpdate {
  widget_id: string
  is_visible?: boolean
  display_order?: number
  custom_settings?: Record<string, unknown>
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Verify user has access to this company
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin access to this company through company_administrators table
    const { data: adminAccess, error: adminError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('company_id', companyId)
      .eq('administrator_id', user.id)
      .single()

    if (adminError || !adminAccess) {
      // Fallback: check if user is the direct administrator
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id, administrator_id')
        .eq('id', companyId)
        .eq('administrator_id', user.id)
        .maybeSingle()

      if (companyError || !company) {
        return NextResponse.json({ error: 'Company not found or access denied' }, { status: 404 })
      }
    }

    // Get dashboard configuration
    const { data: configurations, error } = await supabase
      .from('dashboard_configurations')
      .select('*')
      .eq('company_id', companyId)
      .order('display_order', { ascending: true })

    if (error) {
      console.error('Error fetching dashboard configurations:', error)
      return NextResponse.json({ error: 'Failed to fetch configurations' }, { status: 500 })
    }

    return NextResponse.json({
      company_id: companyId,
      widgets: configurations || []
    })

  } catch (error) {
    console.error('Error in dashboard configuration GET:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Parse request body
    const body = await request.json()
    const { widgets } = body

    if (!Array.isArray(widgets)) {
      return NextResponse.json({ error: 'Widgets array is required' }, { status: 400 })
    }

    // Verify user has access to this company
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user has admin access to this company through company_administrators table
    const { data: adminAccess, error: adminError } = await supabase
      .from('company_administrators')
      .select('role')
      .eq('company_id', companyId)
      .eq('administrator_id', user.id)
      .single()

    if (adminError || !adminAccess) {
      // Fallback: check if user is the direct administrator
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('id, administrator_id')
        .eq('id', companyId)
        .eq('administrator_id', user.id)
        .maybeSingle()

      if (companyError || !company) {
        return NextResponse.json({ error: 'Company not found or access denied' }, { status: 404 })
      }
    }

    // Update configurations in a transaction-like manner
    const updatePromises = widgets.map(async (widget: DashboardWidgetUpdate) => {
      const { widget_id, is_visible, display_order, custom_settings } = widget

      return supabase
        .from('dashboard_configurations')
        .update({
          is_visible: is_visible ?? true,
          display_order: display_order ?? 0,
          custom_settings: custom_settings ?? {},
          updated_at: new Date().toISOString()
        })
        .eq('company_id', companyId)
        .eq('widget_id', widget_id)
    })

    // Execute all updates
    const results = await Promise.all(updatePromises)

    // Check for errors
    const errors = results.filter(result => result.error)
    if (errors.length > 0) {
      console.error('Error updating dashboard configurations:', errors)
      return NextResponse.json({ error: 'Failed to update some configurations' }, { status: 500 })
    }

    // Fetch updated configurations
    const { data: updatedConfigurations, error: fetchError } = await supabase
      .from('dashboard_configurations')
      .select('*')
      .eq('company_id', companyId)
      .order('display_order', { ascending: true })

    if (fetchError) {
      console.error('Error fetching updated configurations:', fetchError)
      return NextResponse.json({ error: 'Failed to fetch updated configurations' }, { status: 500 })
    }

    return NextResponse.json({
      company_id: companyId,
      widgets: updatedConfigurations || [],
      message: 'Dashboard configuration updated successfully'
    })

  } catch (error) {
    console.error('Error in dashboard configuration PUT:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
