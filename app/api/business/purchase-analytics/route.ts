import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { createServiceRoleClient } from '@/lib/supabase/server'
import { SupabaseClient } from '@supabase/supabase-js'

interface WeeklyItemData {
  quantity?: number;
  item_name?: string;
  receipts?: Array<{
    transaction_date: string;
    company_id: string;
  }>;
  transaction_date?: string;
  total_amount?: number;
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    // Create service role client for accessing points_transactions
    const serviceSupabase = createServiceRoleClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    // Verify company exists using service role client (bypass RLS)
    const { data: company, error: companyError } = await serviceSupabase
      .from('companies')
      .select('id, name')
      .eq('id', companyId)
      .maybeSingle()

    if (companyError || !company) {
      console.error('Company lookup failed:', companyError)
      return NextResponse.json({ error: 'Company not found' }, { status: 404 })
    }

    // Get business performance data using the SQL views
    const [
      popularItemsResult,
      revenueItemsResult,
      categoryPerformanceResult,
      recentActivityResult,
      monthlyTrendsResult
    ] = await Promise.all([
      // Most popular items by quantity
      supabase
        .from('popular_items_by_quantity')
        .select('*')
        .eq('company_id', companyId)
        .limit(10),

      // Most valuable items by revenue
      supabase
        .from('popular_items_by_revenue')
        .select('*')
        .eq('company_id', companyId)
        .limit(10),

      // Category performance
      supabase
        .from('category_performance')
        .select('*')
        .eq('company_id', companyId)
        .order('total_revenue', { ascending: false })
        .limit(8),

      // Recent activity (last 30 days)
      supabase
        .from('recent_activity_summary')
        .select('*')
        .eq('company_id', companyId)
        .maybeSingle(),

      // Monthly trends (last 12 months)
      supabase
        .from('monthly_trends')
        .select('*')
        .eq('company_id', companyId)
        .order('month', { ascending: false })
        .limit(12)
    ])

    // Check for errors
    if (popularItemsResult.error) {
      console.error('Error fetching popular items:', popularItemsResult.error)
    }
    if (revenueItemsResult.error) {
      console.error('Error fetching revenue items:', revenueItemsResult.error)
    }
    if (categoryPerformanceResult.error) {
      console.error('Error fetching category performance:', categoryPerformanceResult.error)
    }
    if (recentActivityResult.error) {
      console.error('Error fetching recent activity:', recentActivityResult.error)
    }
    if (monthlyTrendsResult.error) {
      console.error('Error fetching monthly trends:', monthlyTrendsResult.error)
    }

    // Get accurate business summary by calculating directly from points_transactions
    const { data: transactionsForSummary, error: transactionsForSummaryError } = await serviceSupabase
      .from('points_transactions')
      .select('id, total_amount, transaction_date')
      .eq('company_id', companyId)
      .not('total_amount', 'is', null)

    const { count: membersCount, error: membersCountError } = await supabase
      .from('loyalty_members')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', companyId)

    const { count: transactionsCount, error: transactionsCountError } = await serviceSupabase
      .from('points_transactions')
      .select('*', { count: 'exact', head: true })
      .eq('company_id', companyId)

    if (transactionsForSummaryError) {
      console.error('Error fetching transactions for summary:', transactionsForSummaryError)
    }
    if (membersCountError) {
      console.error('Error fetching members count:', membersCountError)
    }
    if (transactionsCountError) {
      console.error('Error fetching transactions count:', transactionsCountError)
    }

    // Calculate accurate totals from points_transactions
    const totalRevenue = (transactionsForSummary || []).reduce((sum: number, transaction: { total_amount?: number }) =>
      sum + (parseFloat(transaction.total_amount?.toString() || '0')), 0
    )
    const avgOrderValue = transactionsForSummary && transactionsForSummary.length > 0
      ? totalRevenue / transactionsForSummary.length
      : 0

    // Calculate accurate recent activity metrics (last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()

    const { data: recentTransactionsData, error: recentTransactionsError } = await serviceSupabase
      .from('points_transactions')
      .select('id, total_amount, member_id, transaction_date')
      .eq('company_id', companyId)
      .gte('transaction_date', thirtyDaysAgo)
      .not('total_amount', 'is', null)
      .order('transaction_date', { ascending: false })

    const { data: newMembersData, error: newMembersError } = await supabase
      .from('loyalty_members')
      .select('id')
      .eq('company_id', companyId)
      .gte('registration_date', thirtyDaysAgo)

    const { data: transactionsData, error: transactionsError } = await serviceSupabase
      .from('points_transactions')
      .select('id, member_id')
      .eq('company_id', companyId)
      .gte('transaction_date', thirtyDaysAgo)

    if (recentTransactionsError) {
      console.error('Error fetching recent transactions:', recentTransactionsError)
    }
    if (newMembersError) {
      console.error('Error fetching new members:', newMembersError)
    }
    if (transactionsError) {
      console.error('Error fetching recent transactions:', transactionsError)
    }

    // Get weekly trends and member analytics in parallel
    const [weeklyItemTrends, weeklySpenderTrends, memberAnalytics] = await Promise.all([
      getWeeklyItemTrends(serviceSupabase, companyId),
      getWeeklySpenderTrends(serviceSupabase, companyId),
      getMemberAnalytics(serviceSupabase, companyId)
    ])

    // Process monthly trends for chart data (fallback)
    const monthlyData = (monthlyTrendsResult.data || []).map(item => ({
      month: new Date(item.month).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short'
      }),
      revenue: item.revenue || 0,
      receipts: item.receipts_count || 0,
      customers: item.unique_customers || 0,
      avgOrderValue: item.avg_order_value || 0
    })).reverse() // Reverse to show chronological order

    // Calculate growth metrics
    const currentMonth = monthlyData[monthlyData.length - 1]
    const previousMonth = monthlyData[monthlyData.length - 2]

    const revenueGrowth = currentMonth && previousMonth
      ? ((currentMonth.revenue - previousMonth.revenue) / previousMonth.revenue) * 100
      : 0

    const customerGrowth = currentMonth && previousMonth
      ? ((currentMonth.customers - previousMonth.customers) / previousMonth.customers) * 100
      : 0

    // Calculate accurate recent activity metrics
    const recentTransactions = recentTransactionsData || []
    const totalRecentRevenue = recentTransactions.reduce((sum: number, transaction: { total_amount?: number }) =>
      sum + (parseFloat(transaction.total_amount?.toString() || '0')), 0
    )
    const avgRecentOrderValue = recentTransactions.length > 0
      ? totalRecentRevenue / recentTransactions.length
      : 0

    const uniqueActiveMembers = new Set((transactionsData || []).map((t: { member_id: string }) => t.member_id)).size

    const analytics = {
      company_id: companyId,
      company_name: company.name,

      // Summary metrics (using accurate calculations)
      summary: {
        total_members: membersCount || 0,
        total_receipts: (transactionsForSummary || []).length,
        total_revenue: totalRevenue,
        avg_order_value: avgOrderValue,
        total_items: (popularItemsResult.data || []).length,
        total_transactions: transactionsCount || 0
      },

      // Recent activity (30 days) - using accurate calculations
      recent_activity: {
        new_members: (newMembersData || []).length,
        receipts: recentTransactions.length,
        revenue: totalRecentRevenue,
        active_members: uniqueActiveMembers,
        avg_order_value: avgRecentOrderValue,
        transactions: (transactionsData || []).length
      },

      // Growth metrics
      growth: {
        revenue_growth: Math.round(revenueGrowth * 100) / 100,
        customer_growth: Math.round(customerGrowth * 100) / 100
      },

      // Top performing items
      top_items: {
        by_quantity: (popularItemsResult.data || []).map(item => ({
          id: item.id,
          name: item.item_name,
          category: item.item_category,
          subcategory: item.item_subcategory,
          quantity_sold: item.total_quantity_sold,
          revenue: item.total_revenue,
          orders: item.number_of_orders,
          avg_price: item.avg_selling_price,
          last_sold: item.last_sold_date
        })),
        by_revenue: (revenueItemsResult.data || []).map(item => ({
          id: item.id,
          name: item.item_name,
          category: item.item_category,
          subcategory: item.item_subcategory,
          quantity_sold: item.total_quantity_sold,
          revenue: item.total_revenue,
          orders: item.number_of_orders,
          avg_price: item.avg_selling_price,
          last_sold: item.last_sold_date
        }))
      },

      // Category performance
      category_performance: (categoryPerformanceResult.data || []).map(category => ({
        category: category.item_category,
        total_items: category.total_items,
        quantity_sold: category.total_quantity_sold,
        revenue: category.total_revenue,
        orders: category.number_of_orders,
        avg_price: category.avg_selling_price,
        avg_quantity_per_order: category.avg_quantity_per_order
      })),

      // Weekly item trends for charts
      weekly_item_trends: weeklyItemTrends,
      // Weekly spender trends for charts
      weekly_spender_trends: weeklySpenderTrends,
      // Monthly trends for charts (fallback)
      monthly_trends: monthlyData,

      // Member analytics
      member_analytics: {
        top_spending_members: memberAnalytics
      }
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error in business purchase analytics API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}




interface WeeklyItemTrendResult {
  week: string
  week_start: string
  item1?: number
  item1_name?: string
  item1_revenue?: number
  item2?: number
  item2_name?: string
  item2_revenue?: number
  item3?: number
  item3_name?: string
  item3_revenue?: number
  item4?: number
  item4_name?: string
  item4_revenue?: number
  item5?: number
  item5_name?: string
  item5_revenue?: number
}

interface WeeklySpenderTrendResult {
  week: string
  week_start: string
  spender1?: number
  spender1_name?: string
  spender1_transactions?: number
  spender2?: number
  spender2_name?: string
  spender2_transactions?: number
  spender3?: number
  spender3_name?: string
  spender3_transactions?: number
  spender4?: number
  spender4_name?: string
  spender4_transactions?: number
  spender5?: number
  spender5_name?: string
  spender5_transactions?: number
}

// Helper function to get weekly item trends
async function getWeeklyItemTrends(serviceSupabase: SupabaseClient, companyId: string): Promise<WeeklyItemTrendResult[]> {
  try {
    console.log('getWeeklyItemTrends: Starting for company:', companyId)

    // First, try to get actual receipt items data
    const { data: receiptItemsData } = await serviceSupabase
      .from('receipt_items')
      .select(`
        quantity,
        item_name,
        receipts!inner (
          transaction_date,
          company_id
        )
      `)
      .eq('receipts.company_id', companyId)
      .gte('receipts.transaction_date', new Date(Date.now() - 8 * 7 * 24 * 60 * 60 * 1000).toISOString())
      .not('item_name', 'is', null)
      .order('receipts.transaction_date', { ascending: false })

    let weeklyData = receiptItemsData
    let dataSource = 'receipt_items'

    // Fallback to transaction descriptions if no receipt items data
    if (!receiptItemsData || receiptItemsData.length === 0) {
      console.log('No receipt items found, using transaction descriptions as fallback')

      const { data: transactionData, error: transactionError } = await serviceSupabase
        .from('points_transactions')
        .select(`
          transaction_date,
          description,
          total_amount,
          business_name
        `)
        .eq('company_id', companyId)
        .gte('transaction_date', new Date(Date.now() - 8 * 7 * 24 * 60 * 60 * 1000).toISOString())
        .not('total_amount', 'is', null)
        .order('transaction_date', { ascending: false })

      if (transactionError) {
        console.error('Error fetching transaction data:', transactionError)
        return []
      }

      // Transform transaction data to match receipt items format
      weeklyData = (transactionData || []).map(transaction => {
        // Extract item name from description, avoiding business names
        let itemName = 'Service/Product'

        if (transaction.description) {
          // Clean up the description to extract actual item/service name
          const cleanDescription = transaction.description
            .replace('Points earned from ', '')
            .replace(' service', '')
            .replace(' purchase', '')
            .trim()

          // If the cleaned description is not just the business name, use it
          if (cleanDescription &&
              cleanDescription !== transaction.business_name &&
              cleanDescription.length > 3 &&
              !cleanDescription.toLowerCase().includes('transaction')) {
            itemName = cleanDescription
          } else if (transaction.business_name) {
            // If description is not helpful, use a generic service name for the business
            itemName = `${transaction.business_name} Service`
          }
        }

        return {
          quantity: 1,
          item_name: itemName,
          receipts: [{
            transaction_date: transaction.transaction_date,
            company_id: companyId
          }]
        }
      })
      dataSource = 'transactions'
    }

    if (!weeklyData || weeklyData.length === 0) {
      console.log('No data available for weekly item trends')
      return []
    }

    console.log(`Using ${dataSource} data with ${weeklyData.length} records`)

    // Group data by week and item name
    const weeklyGroups = {} as Record<string, Record<string, { count: number; revenue: number }>>

    weeklyData.forEach((item: WeeklyItemData) => {
      const transactionDate = item.receipts?.[0]?.transaction_date || item.transaction_date
      if (!transactionDate) return; // Skip items without dates

      const weekStart = new Date(transactionDate)
      weekStart.setDate(weekStart.getDate() - weekStart.getDay()) // Start of week (Sunday)
      const weekKey = weekStart.toISOString().split('T')[0]

      if (!weeklyGroups[weekKey]) {
        weeklyGroups[weekKey] = {}
      }

      const itemName = item.item_name || 'General Item'
      if (!weeklyGroups[weekKey][itemName]) {
        weeklyGroups[weekKey][itemName] = { count: 0, revenue: 0 }
      }

      weeklyGroups[weekKey][itemName].count += item.quantity || 1
      weeklyGroups[weekKey][itemName].revenue += parseFloat(item.total_amount?.toString() || '50') // Estimate for receipt items
    })

    // Convert to chart format - show top 5 items per week
    const chartData = Object.keys(weeklyGroups)
      .sort()
      .slice(-6) // Last 6 weeks
      .map(weekKey => {
        const weekData = weeklyGroups[weekKey]
        const topItems = Object.entries(weekData)
          .sort(([,a], [,b]) => (b as { count: number }).count - (a as { count: number }).count)
          .slice(0, 5)

        const result: WeeklyItemTrendResult = {
          week: new Date(weekKey).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          week_start: weekKey
        }

        topItems.forEach(([itemName, data], index) => {
          const cleanItemName = itemName.length > 20 ? itemName.substring(0, 17) + '...' : itemName

          if (index === 0) {
            result.item1 = data.count
            result.item1_name = cleanItemName
            result.item1_revenue = data.revenue
          } else if (index === 1) {
            result.item2 = data.count
            result.item2_name = cleanItemName
            result.item2_revenue = data.revenue
          } else if (index === 2) {
            result.item3 = data.count
            result.item3_name = cleanItemName
            result.item3_revenue = data.revenue
          } else if (index === 3) {
            result.item4 = data.count
            result.item4_name = cleanItemName
            result.item4_revenue = data.revenue
          } else if (index === 4) {
            result.item5 = data.count
            result.item5_name = cleanItemName
            result.item5_revenue = data.revenue
          }
        })

        return result
      })

    console.log('Weekly trends calculated:', chartData.length, 'weeks')
    return chartData

  } catch (error) {
    console.error('Error in getWeeklyItemTrends:', error)
    return []
  }
}

// Helper function to get weekly spender trends
async function getWeeklySpenderTrends(serviceSupabase: SupabaseClient, companyId: string): Promise<WeeklySpenderTrendResult[]> {
  try {
    console.log('getWeeklySpenderTrends: Starting for company:', companyId)

    // Get weekly spender data - first get transactions, then get member names separately
    const { data: transactionData, error } = await serviceSupabase
      .from('points_transactions')
      .select('transaction_date, member_id, total_amount')
      .eq('company_id', companyId)
      .gte('transaction_date', new Date(Date.now() - 8 * 7 * 24 * 60 * 60 * 1000).toISOString())
      .not('total_amount', 'is', null)
      .not('member_id', 'is', null)
      .order('transaction_date', { ascending: false })

    if (error) {
      console.error('Error fetching transaction data:', error)
      return []
    }

    // Get member names for all unique member IDs
    const memberIds = [...new Set((transactionData || []).map(t => t.member_id))]
    const { data: memberData, error: memberError } = await serviceSupabase
      .from('loyalty_members')
      .select('id, name')
      .in('id', memberIds)

    if (memberError) {
      console.error('Error fetching member data:', memberError)
      return []
    }

    // Create member lookup map
    const memberLookup = (memberData || []).reduce((acc, member) => {
      if (member.id && member.name) {
        acc[member.id] = member.name
      }
      return acc
    }, {} as Record<string, string>)

    console.log('Member lookup created with', Object.keys(memberLookup).length, 'members')

    // Combine transaction data with member names
    const weeklyData = (transactionData || []).map(transaction => {
      const memberName = memberLookup[transaction.member_id] ||
                        `Member ${transaction.member_id?.slice(-4) || 'Unknown'}`
      return {
        ...transaction,
        member_name: memberName
      }
    })

    console.log('Weekly spender data fetched:', weeklyData?.length, 'records')
    if (weeklyData?.length > 0) {
      console.log('Sample record:', weeklyData[0])
      console.log('Member lookup sample:', Object.entries(memberLookup).slice(0, 3))
    }

    // Group data by week and member
    const weeklyGroups = {} as Record<string, Record<string, { spent: number; transactions: number; name: string }>>

    (weeklyData || []).forEach((transaction: { member_id: string; member_name: string; transaction_date: string; total_amount: number | string }) => {
      const weekStart = new Date(transaction.transaction_date)
      weekStart.setDate(weekStart.getDate() - weekStart.getDay()) // Start of week (Sunday)
      const weekKey = weekStart.toISOString().split('T')[0]

      if (!weeklyGroups[weekKey]) {
        weeklyGroups[weekKey] = {}
      }

      const memberId = transaction.member_id
      const memberName = transaction.member_name || 'Unknown Member'

      if (!weeklyGroups[weekKey][memberId]) {
        weeklyGroups[weekKey][memberId] = { spent: 0, transactions: 0, name: memberName }
      }

      weeklyGroups[weekKey][memberId].spent += parseFloat(transaction.total_amount?.toString() || '0')
      weeklyGroups[weekKey][memberId].transactions += 1
    })

    // Convert to chart format - show top 5 spenders per week
    const chartData = Object.keys(weeklyGroups)
      .sort()
      .slice(-6) // Last 6 weeks
      .map(weekKey => {
        const weekData = weeklyGroups[weekKey]
        const topSpenders = Object.entries(weekData)
          .sort(([,a], [,b]) => b.spent - a.spent)
          .slice(0, 5)

        const result: WeeklySpenderTrendResult = {
          week: new Date(weekKey).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          week_start: weekKey
        }

        topSpenders.forEach(([, data], index) => {
          if (index === 0) {
            result.spender1 = data.spent
            result.spender1_name = data.name.substring(0, 20)
            result.spender1_transactions = data.transactions
          } else if (index === 1) {
            result.spender2 = data.spent
            result.spender2_name = data.name.substring(0, 20)
            result.spender2_transactions = data.transactions
          } else if (index === 2) {
            result.spender3 = data.spent
            result.spender3_name = data.name.substring(0, 20)
            result.spender3_transactions = data.transactions
          } else if (index === 3) {
            result.spender4 = data.spent
            result.spender4_name = data.name.substring(0, 20)
            result.spender4_transactions = data.transactions
          } else if (index === 4) {
            result.spender5 = data.spent
            result.spender5_name = data.name.substring(0, 20)
            result.spender5_transactions = data.transactions
          }
        })

        return result
      })

    console.log('Weekly spender trends calculated:', chartData.length, 'weeks')
    return chartData

  } catch (error) {
    console.error('Error in getWeeklySpenderTrends:', error)
    return []
  }
}

interface MemberData {
  id: string
  name: string
  phone_number: string
}

interface TransactionData {
  total_amount: number
}

interface MemberAnalyticsResult {
  member_id: string
  name: string
  phone_number: string
  total_spent: number
  transaction_count: number
  avg_order_value: number
  favorite_item: null
}

// Helper function to get member analytics
async function getMemberAnalytics(serviceSupabase: SupabaseClient, companyId: string): Promise<MemberAnalyticsResult[]> {
  try {
    console.log('getMemberAnalytics: Starting for company:', companyId)

    // Use a single query with aggregation for better performance
    const { data: memberAnalytics, error } = await serviceSupabase
      .rpc('get_member_spending_analytics', {
        p_company_id: companyId
      })

    if (error) {
      console.log('RPC function not available, falling back to manual calculation')

      // Fallback: Manual calculation using raw queries
      const { data: memberSpending, error: memberError } = await serviceSupabase
        .from('loyalty_members')
        .select('id, name, phone_number')
        .eq('company_id', companyId)

      if (memberError || !memberSpending) {
        console.error('Error fetching members:', memberError)
        return []
      }

      console.log('Found members:', memberSpending.length)

      // Get spending data for each member
      const memberAnalytics = await Promise.all(
        memberSpending.map(async (member: MemberData) => {
          const { data: transactions, error: txError } = await serviceSupabase
            .from('points_transactions')
            .select('total_amount')
            .eq('member_id', member.id)
            .not('total_amount', 'is', null)

          if (txError) {
            console.error('Error fetching transactions for member:', member.id, txError)
            return null
          }

          const totalSpent = (transactions || []).reduce((sum: number, t: TransactionData) => sum + (t.total_amount || 0), 0)
          const transactionCount = (transactions || []).length
          const avgOrderValue = transactionCount > 0 ? totalSpent / transactionCount : 0

          return {
            member_id: member.id,
            name: member.name,
            phone_number: member.phone_number,
            total_spent: totalSpent,
            transaction_count: transactionCount,
            avg_order_value: avgOrderValue,
            favorite_item: null
          }
        })
      )

      // Filter out null results and members with no spending
      const validAnalytics = (memberAnalytics
        .filter((member: MemberAnalyticsResult | null): member is MemberAnalyticsResult => member !== null && member.total_spent > 0))
        .sort((a, b) => b.total_spent - a.total_spent)
        .slice(0, 10)

      console.log('Member analytics calculated:', validAnalytics.length, 'members with spending')
      return validAnalytics
    }

    // If RPC worked, return the data
    console.log('RPC returned member analytics:', memberAnalytics?.length || 0)
    return memberAnalytics || []

  } catch (error) {
    console.error('Error in getMemberAnalytics:', error)
    return []
  }
}
