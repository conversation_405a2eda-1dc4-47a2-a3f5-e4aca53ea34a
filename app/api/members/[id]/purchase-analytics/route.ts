import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()
    const { searchParams } = new URL(request.url)
    const companyId = searchParams.get('companyId')
    const { id: memberId } = await params

    if (!companyId) {
      return NextResponse.json({ error: 'Company ID is required' }, { status: 400 })
    }

    if (!memberId) {
      return NextResponse.json({ error: 'Member ID is required' }, { status: 400 })
    }

    // Verify member belongs to company - use loyalty_members table
    const { data: member, error: memberError } = await supabase
      .from('loyalty_members')
      .select('id, name')
      .eq('id', memberId)
      .eq('company_id', companyId)
      .maybeSingle()

    console.log('Member lookup debug:', {
      memberId,
      companyId,
      memberError,
      member,
      memberFound: !!member
    })

    if (memberError || !member) {
      console.error('Member lookup failed:', memberError)
      return NextResponse.json({
        error: 'Member not found',
        debug: { memberId, companyId, memberError: memberError?.message }
      }, { status: 404 })
    }

    // Define receipt type with proper relational structure
    interface Receipt {
      id: string
      total_amount: number
      subtotal?: number
      created_at: string
      business_name?: string
      receipt_items?: Array<{
        id: string
        quantity: number
        unit_price: number
        total_price: number
        extracted_description?: string
        business_items?: {
          item_name: string
          item_category: string
        }
      }>
    }

    // Get receipts for this member with receipt items (left join for fallback)
    const { data: receipts, error: receiptsError } = await supabase
      .from('receipts')
      .select(`
        id,
        total_amount,
        subtotal,
        created_at,
        business_name,
        receipt_items (
          id,
          quantity,
          unit_price,
          total_price,
          extracted_description,
          business_items (
            item_name,
            item_category
          )
        )
      `)
      .eq('member_id', memberId)
      .eq('company_id', companyId)
      .order('created_at', { ascending: false })
      .returns<Receipt[]>()

    if (receiptsError) {
      console.error('Error fetching receipts:', receiptsError)
      return NextResponse.json({ error: 'Failed to fetch purchase data' }, { status: 500 })
    }

    if (!receipts || receipts.length === 0) {
      return NextResponse.json({
        member_id: memberId,
        member_name: member.name,
        total_purchases: 0,
        total_spent: 0,
        most_purchased_items: [],
        purchase_frequency: {
          daily_average: 0,
          monthly_total: 0,
          last_purchase_date: null
        }
      })
    }

    // Calculate analytics
    const totalPurchases = receipts.length
    const totalSpent = receipts.reduce((sum: number, receipt: Receipt) => {
      const amount = receipt.subtotal || receipt.total_amount || 0
      return sum + amount
    }, 0)

    // Process items from receipt_items relational structure
    const itemCounts = new Map<string, {
      count: number
      totalSpent: number
      category?: string
    }>()

    receipts.forEach((receipt: Receipt) => {
      if (receipt.receipt_items && Array.isArray(receipt.receipt_items) && receipt.receipt_items.length > 0) {
        receipt.receipt_items.forEach((receiptItem) => {
          // Use item name from business_items or fallback to extracted_description
          const itemName = receiptItem.business_items?.item_name || receiptItem.extracted_description || 'Unknown Item'
          const itemCategory = receiptItem.business_items?.item_category
          const quantity = receiptItem.quantity || 1
          const totalPrice = receiptItem.total_price || 0

          if (itemName) {
            const cleanItemName = itemName.trim()

            if (!itemCounts.has(cleanItemName)) {
              itemCounts.set(cleanItemName, {
                count: 0,
                totalSpent: 0,
                category: itemCategory
              })
            }

            const existing = itemCounts.get(cleanItemName)!
            existing.count += quantity
            existing.totalSpent += totalPrice
          }
        })
      } else {
        // For receipts without specific items, create a generic entry based on business name
        const itemName = `Purchase at ${receipt.business_name || 'Unknown Business'}`
        const amount = receipt.subtotal || receipt.total_amount || 0

        if (!itemCounts.has(itemName)) {
          itemCounts.set(itemName, {
            count: 0,
            totalSpent: 0,
            category: 'General'
          })
        }

        const existing = itemCounts.get(itemName)!
        existing.count += 1
        existing.totalSpent += amount
      }
    })

    // Convert to array and sort by purchase count
    const mostPurchasedItems = Array.from(itemCounts.entries())
      .map(([itemName, data]) => ({
        item_name: itemName,
        category: data.category,
        purchase_count: data.count,
        total_spent: data.totalSpent,
        average_price: data.totalSpent / data.count
      }))
      .sort((a, b) => b.purchase_count - a.purchase_count)
      .slice(0, 20) // Limit to top 20 items


    // Calculate purchase frequency
    const sortedReceipts = receipts.sort((a: Receipt, b: Receipt) =>
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    )

    const firstPurchase = new Date(sortedReceipts[0].created_at)
    const lastPurchase = new Date(sortedReceipts[sortedReceipts.length - 1].created_at)

    let monthlyAverage = 0

    if (totalPurchases === 1) {
      // For single purchase, calculate based on time since first purchase
      const now = new Date()
      const daysSinceFirstPurchase = (now.getTime() - firstPurchase.getTime()) / (1000 * 60 * 60 * 24)

      if (daysSinceFirstPurchase < 30) {
        // If purchased within last 30 days, show as 1 purchase per month
        monthlyAverage = 1
      } else {
        // Calculate monthly rate based on actual time elapsed
        const monthsSinceFirstPurchase = daysSinceFirstPurchase / 30
        monthlyAverage = 1 / monthsSinceFirstPurchase
      }
    } else {
      // For multiple purchases, calculate average per month across the entire period
      const timeDiffMs = lastPurchase.getTime() - firstPurchase.getTime()
      const monthsDiff = Math.max(1/30, timeDiffMs / (1000 * 60 * 60 * 24 * 30)) // Minimum 1 day = 1/30 month
      monthlyAverage = totalPurchases / monthsDiff
    }

    console.log('Purchase frequency debug:', {
      totalPurchases,
      firstPurchase: firstPurchase.toISOString(),
      lastPurchase: lastPurchase.toISOString(),
      monthlyAverage,
      memberId
    })

    const analytics = {
      member_id: memberId,
      member_name: member.name,
      total_purchases: totalPurchases,
      total_spent: Math.round(totalSpent),
      most_purchased_items: mostPurchasedItems,
      purchase_frequency: {
        monthly_average: Math.round(monthlyAverage * 100) / 100,
        last_purchase_date: lastPurchase.toISOString()
      }
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error in purchase analytics API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
