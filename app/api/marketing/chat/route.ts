import { google } from '@ai-sdk/google';
import { streamText } from 'ai';
import { NextRequest } from 'next/server';

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: NextRequest) {
  try {
    const { messages } = await req.json();

    const result = await streamText({
      model: google('gemini-2.0-flash-exp'),
      messages,
      system: `You are a helpful AI marketing assistant for a loyalty program platform. Your role is to help businesses create effective marketing campaigns.

Key Guidelines:
- Be friendly, professional, and enthusiastic about marketing
- Ask clarifying questions to understand campaign goals
- Provide specific, actionable suggestions
- When creating campaigns, include clear subject lines, engaging content, and strong calls-to-action
- Consider the Ethiopian market context when relevant
- Focus on loyalty program benefits and member engagement

When you generate a complete campaign, format it clearly with:
📧 Campaign: [Type]
Subject: "[Subject Line]"
Body: "[Campaign Content]"
CTA: "[Call to Action]"
Target: [Target Audience]
Objective: [Campaign Goal]

Always be helpful and guide users through the campaign creation process step by step.`,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('Error in marketing chat API:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}
