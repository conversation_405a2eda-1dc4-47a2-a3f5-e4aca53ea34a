import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET() {
  try {
    const supabase = await createClient()

    // Get current user with proper server auth
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    console.log('Debug API: Auth check result:', {
      hasUser: !!user,
      userId: user?.id,
      email: user?.email,
      error: userError
    })

    if (userError || !user) {
      return NextResponse.json({
        authenticated: false,
        error: userError?.message || 'No user found',
        details: { userError }
      })
    }

    // Try to get user role
    const { data: userRoles, error: roleError } = await supabase
      .from('company_administrators')
      .select('company_id, role')
      .eq('administrator_id', user.id)

    console.log('Debug API: Role check result:', { userRoles, roleError })

    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email
      },
      roles: userRoles || [],
      roleError: roleError?.message
    })
  } catch (error) {
    console.error('Debug API error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
