import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function GET() {
  try {
    const serviceClient = getServiceRoleClient()

    // Check current session
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set: () => {},
          remove: () => {},
        },
      }
    )

    const { data: { user: currentUser }, error: authError } = await serverSupabase.auth.getUser()

    if (!currentUser) {
      return NextResponse.json({
        error: 'No current user found',
        authError: authError?.message
      })
    }

    // Test the exact same query as the companies/current endpoint
    const { data: companyAssociation, error: associationError } = await serviceClient
      .from('company_administrators')
      .select(`
        company_id,
        role,
        companies (
          id,
          name,
          slug,
          logo_url,
          primary_color,
          points_expiration_days,
          points_earning_ratio,
          administrator_id,
          created_at,
          is_active,
          onboarding_completed,
          business_type,
          setup_wizard_step
        )
      `)
      .eq('administrator_id', currentUser.id)
      .maybeSingle()

    return NextResponse.json({
      currentUser: {
        id: currentUser.id,
        email: currentUser.email
      },
      query: {
        companyAssociation,
        associationError: associationError?.message,
        hasCompany: !!companyAssociation?.companies
      }
    })

  } catch (error) {
    return NextResponse.json({
      error: 'Debug failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
