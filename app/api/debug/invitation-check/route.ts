import { NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

export async function GET() {
  try {
    const supabase = getServiceRoleClient()

    // Test database connection
    const { data, error } = await supabase
      .from('cashier_invitations')
      .select('invitation_token, email, expires_at, used_at')
      .eq('invitation_token', 'fe910a087df736f6a964539546cef9c7')
      .single()

    return NextResponse.json({
      success: true,
      invitation: data,
      error: error?.message,
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      timestamp: new Date().toISOString()
    })
  } catch (err) {
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error',
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      timestamp: new Date().toISOString()
    })
  }
}
