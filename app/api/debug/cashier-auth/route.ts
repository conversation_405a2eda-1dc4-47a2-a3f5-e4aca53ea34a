import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Debug endpoint to check cashier authentication status
export async function GET(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()

    // Get email from query parameter
    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email') || '<EMAIL>'

    // Check current session
    const cookieStore = await cookies()
    const serverSupabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set: () => {},
          remove: () => {},
        },
      }
    )

    const { data: { user: currentUser } } = await serverSupabase.auth.getUser()

    // 1. Check administrators table
    const { data: adminRecord } = await serviceClient
      .from('administrators')
      .select('id, email, name, is_active, created_at')
      .eq('email', email)
      .maybeSingle()

    // 2. Check company_administrators table
    const { data: companyAdmin } = await serviceClient
      .from('company_administrators')
      .select(`
        id,
        administrator_id,
        company_id,
        role,
        created_at,
        companies (
          id,
          name
        )
      `)
      .eq('administrator_id', adminRecord?.id || 'none')
      .maybeSingle()

    // 3. Check Supabase auth users
    const { data: authUsers } = await serviceClient.auth.admin.listUsers()
    const authUser = authUsers?.users?.find(u => u.email === email)

    // 4. Check cashier invitation
    const { data: invitation } = await serviceClient
      .from('cashier_invitations')
      .select('id, email, used_at, expires_at, company_id')
      .eq('email', email)
      .maybeSingle()

    // 5. Test role API
    let roleResult = null
    if (adminRecord?.id && companyAdmin?.company_id) {
      try {
        const roleResponse = await fetch(`${request.nextUrl.origin}/api/user-role?companyId=${companyAdmin.company_id}`, {
          headers: {
            'Authorization': request.headers.get('Authorization') || '',
            'Cookie': request.headers.get('Cookie') || ''
          }
        })
        roleResult = roleResponse.ok ? await roleResponse.json() : { error: await roleResponse.text() }
      } catch (e) {
        roleResult = { error: e instanceof Error ? e.message : 'Unknown error' }
      }
    }

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      currentUser: {
        authenticated: !!currentUser,
        id: currentUser?.id,
        email: currentUser?.email
      },
      database: {
        adminRecord,
        companyAdmin,
        invitation
      },
      supabaseAuth: {
        userExists: !!authUser,
        id: authUser?.id,
        email: authUser?.email,
        confirmed: authUser?.email_confirmed_at ? true : false,
        lastSignIn: authUser?.last_sign_in_at
      },
      analysis: {
        uuidMatch: adminRecord?.id === authUser?.id,
        invitationUsed: !!invitation?.used_at,
        roleResolution: roleResult
      },
      recommendations: []
    })

  } catch (error) {
    console.error('Debug endpoint error:', error)
    return NextResponse.json({
      error: 'Debug endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
