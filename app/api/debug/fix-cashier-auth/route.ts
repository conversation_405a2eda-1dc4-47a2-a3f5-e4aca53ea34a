import { NextRequest, NextResponse } from 'next/server'
import { getServiceRoleClient } from '@/lib/supabase'

// Fix cashier authentication issues
export async function POST(request: NextRequest) {
  try {
    const serviceClient = getServiceRoleClient()
    const body = await request.json()
    const { email = '<EMAIL>' } = body

    // Get current admin record
    const { data: adminRecord } = await serviceClient
      .from('administrators')
      .select('id, email, name')
      .eq('email', email)
      .maybeSingle()

    if (!adminRecord) {
      return NextResponse.json({ error: 'Administrator record not found' }, { status: 404 })
    }

    // Get Supabase auth user
    const { data: authUsers } = await serviceClient.auth.admin.listUsers()
    const authUser = authUsers?.users?.find(u => u.email === email)

    if (!authUser) {
      return NextResponse.json({ error: 'Supabase auth user not found' }, { status: 404 })
    }

    const fixed: string[] = []

    // Fix 1: Update administrator record with correct UUID
    if (adminRecord.id !== authUser.id) {
      // First, update company_administrators to point to the correct UUID
      const { data: companyAdmin } = await serviceClient
        .from('company_administrators')
        .select('id, company_id, role')
        .eq('administrator_id', adminRecord.id)
        .maybeSingle()

      if (companyAdmin) {
        await serviceClient
          .from('company_administrators')
          .update({ administrator_id: authUser.id })
          .eq('id', companyAdmin.id)
        fixed.push('Updated company_administrators UUID')
      }

      // Delete old administrator record
      await serviceClient
        .from('administrators')
        .delete()
        .eq('id', adminRecord.id)

      // Create new administrator record with correct UUID
      await serviceClient
        .from('administrators')
        .insert({
          id: authUser.id,
          email: authUser.email!,
          name: adminRecord.name || 'Cashier',
          is_active: true
        })

      fixed.push('Updated administrators table UUID')
    }

    // Fix 2: Ensure user metadata is correct
    const { error: updateError } = await serviceClient.auth.admin.updateUserById(
      authUser.id,
      {
        user_metadata: {
          ...authUser.user_metadata,
          role: 'cashier',
          full_name: adminRecord.name || authUser.user_metadata?.full_name
        }
      }
    )

    if (!updateError) {
      fixed.push('Updated user metadata')
    }

    return NextResponse.json({
      message: 'Cashier authentication fixed',
      email,
      oldId: adminRecord.id,
      newId: authUser.id,
      fixed
    })

  } catch (error) {
    console.error('Fix endpoint error:', error)
    return NextResponse.json({
      error: 'Fix failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
