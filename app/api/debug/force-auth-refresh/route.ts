import { NextResponse } from 'next/server'

export async function POST() {
  try {
    // This endpoint can be called from the frontend to force a cache invalidation
    // The actual cache invalidation will happen on the client side
    return NextResponse.json({
      success: true,
      message: 'Auth refresh triggered',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error in force auth refresh:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
