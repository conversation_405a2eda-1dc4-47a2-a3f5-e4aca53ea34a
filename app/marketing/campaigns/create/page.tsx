'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { MessageSquare, ArrowLeft, ArrowRight } from 'lucide-react'

// Import wizard steps
import CampaignBasicsStep from './components/campaign-basics-step'
import TargetAudienceStep from './components/target-audience-step'
import MessageComposerStep from './components/message-composer-step'
import ReviewAndSendStep from './components/review-and-send-step'

interface CampaignData {
  name: string
  description?: string
  message_title?: string
  message_content: string
  target_type: 'all' | 'tier' | 'individual' | 'custom'
  target_criteria: Record<string, unknown>
  scheduled_at?: string
}

const steps = [
  {
    title: 'Campaign Details',
    description: 'Basic campaign information'
  },
  {
    title: 'Target Audience',
    description: 'Select who will receive your message'
  },
  {
    title: 'Compose Message',
    description: 'Create your marketing message'
  },
  {
    title: 'Review & Send',
    description: 'Review and send your campaign'
  }
]

export default function CreateCampaignPage() {
  useRequireAuth()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: '',
    description: '',
    message_title: '',
    message_content: '',
    target_type: 'all',
    target_criteria: {},
    scheduled_at: undefined
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [recipientCount, setRecipientCount] = useState(0)

  const updateCampaignData = (updates: Partial<CampaignData>) => {
    setCampaignData(prev => ({ ...prev, ...updates }))
  }

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      console.log(`🚶 Moving to step ${currentStep + 1}, current campaign data:`, campaignData)
      console.log(`📊 Current recipient count:`, recipientCount)
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const sendCampaign = async () => {
    try {
      setIsSubmitting(true)
      setError(null)

      // First create the campaign
      const createResponse = await fetch('/api/marketing/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(errorData.error || 'Failed to create campaign')
      }

      const createData = await createResponse.json()
      const campaignId = createData.campaign.id

      // Then send the campaign
      const sendResponse = await fetch(`/api/marketing/campaigns/${campaignId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ confirm: true })
      })

      if (!sendResponse.ok) {
        const errorData = await sendResponse.json()
        throw new Error(errorData.error || 'Failed to send campaign')
      }

      router.push(`/marketing/campaigns/${campaignId}`)
    } catch (error) {
      console.error('Error sending campaign:', error)
      setError(error instanceof Error ? error.message : 'Failed to send campaign')
    } finally {
      setIsSubmitting(false)
    }
  }

  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-7xl p-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/marketing/campaigns')}
                className="hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                  <MessageSquare className="h-8 w-8" />
                  Create Campaign
                </h1>
                <p className="text-gray-600 mt-1">Create a new marketing campaign to send to your members</p>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">
                Step {currentStep + 1} of {steps.length}: {steps[currentStep].title}
              </h3>
              <span className="text-sm text-muted-foreground">
                {Math.round(progress)}% complete
              </span>
            </div>
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-muted-foreground">
              {steps[currentStep].description}
            </p>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Step Content */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                        {currentStep === 0 && (
              <CampaignBasicsStep
                data={campaignData}
                updateData={updateCampaignData}
              />
            )}
            {currentStep === 1 && (
              <TargetAudienceStep
                data={campaignData}
                updateData={updateCampaignData}
                onRecipientCountChange={setRecipientCount}
              />
            )}
            {currentStep === 2 && (
              <MessageComposerStep
                data={campaignData}
                updateData={updateCampaignData}
              />
            )}
            {currentStep === 3 && (
              <ReviewAndSendStep
                data={campaignData}
                onSend={sendCampaign}
                isSubmitting={isSubmitting}
                recipientCount={recipientCount}
              />
            )}
          </div>

        {/* Navigation */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="hover:bg-gray-50"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>

            <Button
              onClick={nextStep}
              disabled={currentStep === steps.length - 1}
              className="hover:shadow-md transition-shadow"
            >
              Next
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
