import { openai } from '@ai-sdk/openai';
import { streamText, generateId } from 'ai';

// Conversation states for the campaign creation flow
export enum ConversationState {
  GREETING = 'greeting',
  INTENT_DISCOVERY = 'intent_discovery',
  CAMPAIGN_TYPE = 'campaign_type',
  TARGET_AUDIENCE = 'target_audience',
  CAMPAIGN_GOAL = 'campaign_goal',
  MESSAGE_TONE = 'message_tone',
  CONTENT_DETAILS = 'content_details',
  VALIDATION = 'validation',
  GENERATION = 'generation'
}

// Intent types for user messages
export type UserIntent = 'greeting' | 'campaign_creation' | 'exploration' | 'help' | 'unclear';

// Campaign requirements schema
export interface CampaignRequirements {
  intent: UserIntent;
  campaignType?: 'promotional' | 'announcement' | 'birthday' | 'loyalty' | 'custom';
  targetAudience?: {
    type: 'all' | 'tier' | 'segment' | 'individual';
    criteria?: Record<string, unknown>;
  };
  goal?: string;
  tone?: 'professional' | 'friendly' | 'urgent' | 'celebratory' | 'custom';
  contentDetails?: {
    subject?: string;
    mainMessage?: string;
    callToAction?: string;
    additionalInfo?: string;
  };
  completeness: number; // 0-100%
}

// Conversation context
export interface ConversationContext {
  state: ConversationState;
  requirements: CampaignRequirements;
  messageHistory: Array<{ role: 'user' | 'assistant'; content: string; timestamp: Date }>;
  sessionId: string;
}

// Validation result
export interface ValidationResult {
  isComplete: boolean;
  completeness: number;
  missingFields: string[];
  suggestions: string[];
}

// Intent detection with AI
export async function detectIntent(message: string, context: ConversationContext): Promise<{
  intent: UserIntent;
  confidence: number;
  reasoning: string;
}> {
  const result = await streamText({
    model: openai('gpt-4'),
    system: `You are an intent classifier for a marketing campaign assistant.
    Analyze the user's message and determine their intent based on the conversation context.
    
    Possible intents:
    - greeting: Simple hello, how are you, casual conversation starters
    - campaign_creation: Clear intent to create a marketing campaign
    - exploration: Wants to learn about campaign options or capabilities
    - help: Needs assistance, has questions, or is confused
    - unclear: Intent is ambiguous or unclear
    
    Current conversation state: ${context.state}
    Previous messages: ${context.messageHistory.slice(-3).map(m => `${m.role}: ${m.content}`).join('\n')}
    
    Be conservative - only classify as 'campaign_creation' if there's clear intent to create a campaign.
    Simple greetings like "hi", "hello", "how are you" should be classified as 'greeting'.
    
    Respond with valid JSON only: {"intent": "...", "confidence": 0.95, "reasoning": "..."}`,
    messages: [{ role: 'user', content: message }],
    maxTokens: 200
  });

  try {
    const response = await result.text;
    const parsed = JSON.parse(response);
    return {
      intent: parsed.intent as UserIntent,
      confidence: parsed.confidence || 0.5,
      reasoning: parsed.reasoning || 'No reasoning provided'
    };
  } catch (error) {
    console.error('Failed to parse intent detection response:', error);
    return {
      intent: 'unclear',
      confidence: 0.1,
      reasoning: 'Failed to parse AI response'
    };
  }
}

// Get system prompt based on conversation state
export function getSystemPrompt(state: ConversationState, requirements: CampaignRequirements): string {
  const basePrompt = `You are a helpful marketing campaign assistant. Your goal is to gather complete campaign requirements through natural conversation before creating any campaigns.

IMPORTANT RULES:
1. Never create a campaign from simple greetings or casual conversation
2. Always gather complete requirements using the CRAFT framework
3. Be conversational and helpful, not robotic
4. Ask one focused question at a time
5. Provide examples and suggestions to help users
6. Validate understanding before moving to the next step

Current requirements completeness: ${requirements.completeness}%`;

  switch (state) {
    case ConversationState.GREETING:
      return `${basePrompt}

You're in the greeting phase. Respond warmly and ask how you can help with their marketing campaigns. Don't assume they want to create a campaign yet.`;

    case ConversationState.INTENT_DISCOVERY:
      return `${basePrompt}

The user has shown some interest. Clarify what they want to do:
- Create a new marketing campaign
- Learn about campaign options
- Get help with existing campaigns
- Something else

Ask a clarifying question to understand their specific needs.`;

    case ConversationState.CAMPAIGN_TYPE:
      return `${basePrompt}

The user wants to create a campaign. Help them identify the campaign type:
- Promotional (sales, discounts, new products)
- Announcement (news, updates, events)
- Birthday/Special occasion
- Loyalty/Rewards program
- Custom/Other

Ask what type of campaign they have in mind and provide examples.`;

    case ConversationState.TARGET_AUDIENCE:
      return `${basePrompt}

Help them define their target audience:
- All members
- Specific tier (VIP, Gold, Silver, etc.)
- Custom segment (age, location, behavior)
- Individual members

Ask who they want to reach with this campaign.`;

    case ConversationState.CAMPAIGN_GOAL:
      return `${basePrompt}

Help them clarify their campaign goal:
- What do they want to achieve?
- What action should recipients take?
- How will they measure success?

Ask about their specific objectives for this campaign.`;

    case ConversationState.MESSAGE_TONE:
      return `${basePrompt}

Help them choose the right tone:
- Professional and formal
- Friendly and casual
- Urgent and action-oriented
- Celebratory and exciting
- Custom tone

Ask what tone would work best for their audience and goal.`;

    case ConversationState.CONTENT_DETAILS:
      return `${basePrompt}

Gather the content details:
- Subject line (if needed)
- Main message content
- Call-to-action
- Any additional information

Ask for the specific content they want to include.`;

    case ConversationState.VALIDATION:
      return `${basePrompt}

Review all gathered requirements with the user:
${JSON.stringify(requirements, null, 2)}

Confirm these details are correct and ask if they want to make any changes before generating the campaign.`;

    case ConversationState.GENERATION:
      return `${basePrompt}

All requirements are complete. Generate the campaign and transition to the audience selection step.`;

    default:
      return basePrompt;
  }
}

// Validate campaign completeness
export function validateCampaignCompleteness(requirements: CampaignRequirements): ValidationResult {
  const requiredFields = [
    'campaignType',
    'targetAudience.type',
    'goal',
    'contentDetails.mainMessage'
  ];

  const missingFields: string[] = [];
  
  if (!requirements.campaignType) missingFields.push('Campaign Type');
  if (!requirements.targetAudience?.type) missingFields.push('Target Audience');
  if (!requirements.goal) missingFields.push('Campaign Goal');
  if (!requirements.contentDetails?.mainMessage) missingFields.push('Main Message');

  const completeness = ((requiredFields.length - missingFields.length) / requiredFields.length) * 100;

  const suggestions = missingFields.map(field => {
    switch (field) {
      case 'Campaign Type':
        return 'What type of campaign do you want to create? (promotional, announcement, birthday, etc.)';
      case 'Target Audience':
        return 'Who should receive this campaign? (all members, specific tier, custom segment)';
      case 'Campaign Goal':
        return 'What do you want to achieve with this campaign?';
      case 'Main Message':
        return 'What message do you want to send to your audience?';
      default:
        return `Please provide information about ${field}`;
    }
  });

  return {
    isComplete: completeness >= 80,
    completeness,
    missingFields,
    suggestions
  };
}

// Determine next conversation state
export function getNextState(
  currentState: ConversationState,
  intent: UserIntent,
  requirements: CampaignRequirements
): ConversationState {
  switch (currentState) {
    case ConversationState.GREETING:
      if (intent === 'campaign_creation') return ConversationState.CAMPAIGN_TYPE;
      if (intent === 'exploration' || intent === 'help') return ConversationState.INTENT_DISCOVERY;
      return ConversationState.GREETING;

    case ConversationState.INTENT_DISCOVERY:
      if (intent === 'campaign_creation') return ConversationState.CAMPAIGN_TYPE;
      return ConversationState.INTENT_DISCOVERY;

    case ConversationState.CAMPAIGN_TYPE:
      if (requirements.campaignType) return ConversationState.TARGET_AUDIENCE;
      return ConversationState.CAMPAIGN_TYPE;

    case ConversationState.TARGET_AUDIENCE:
      if (requirements.targetAudience?.type) return ConversationState.CAMPAIGN_GOAL;
      return ConversationState.TARGET_AUDIENCE;

    case ConversationState.CAMPAIGN_GOAL:
      if (requirements.goal) return ConversationState.MESSAGE_TONE;
      return ConversationState.CAMPAIGN_GOAL;

    case ConversationState.MESSAGE_TONE:
      if (requirements.tone) return ConversationState.CONTENT_DETAILS;
      return ConversationState.CONTENT_DETAILS; // Skip tone if not provided

    case ConversationState.CONTENT_DETAILS:
      if (requirements.contentDetails?.mainMessage) return ConversationState.VALIDATION;
      return ConversationState.CONTENT_DETAILS;

    case ConversationState.VALIDATION:
      const validation = validateCampaignCompleteness(requirements);
      if (validation.isComplete) return ConversationState.GENERATION;
      return ConversationState.VALIDATION;

    case ConversationState.GENERATION:
      return ConversationState.GENERATION;

    default:
      return ConversationState.GREETING;
  }
}

// Create initial conversation context
export function createInitialContext(): ConversationContext {
  return {
    state: ConversationState.GREETING,
    requirements: {
      intent: 'unclear',
      completeness: 0
    },
    messageHistory: [],
    sessionId: generateId()
  };
}

// Update requirements based on user input and AI analysis
export async function updateRequirements(
  currentRequirements: CampaignRequirements
): Promise<CampaignRequirements> {
  // This is a simplified version - in practice, you'd use the AI SDK's tool calling
  // to extract structured information from the user's message
  const updatedRequirements = { ...currentRequirements };

  // Update completeness
  const validation = validateCampaignCompleteness(updatedRequirements);
  updatedRequirements.completeness = validation.completeness;

  return updatedRequirements;
}
