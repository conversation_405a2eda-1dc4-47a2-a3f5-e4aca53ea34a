'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Users, Target, Plus, X } from 'lucide-react';

interface AudienceCriteria {
  memberTiers?: string[];
  ageRange?: { min: number; max: number };
  location?: string[];
  purchaseHistory?: {
    minAmount?: number;
    maxAmount?: number;
    timeframe?: string;
  };
  loyaltyPoints?: {
    min?: number;
    max?: number;
  };
  tags?: string[];
}

interface AudienceSelectorProps {
  criteria: AudienceCriteria;
  onCriteriaChange: (criteria: AudienceCriteria) => void;
  estimatedReach?: number;
}

const MEMBER_TIERS = ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond'];
const LOCATIONS = ['Ad<PERSON> Ababa', '<PERSON><PERSON>', '<PERSON><PERSON> Dar', '<PERSON>was<PERSON>', '<PERSON><PERSON><PERSON>'];
const TIMEFRAMES = ['Last 30 days', 'Last 3 months', 'Last 6 months', 'Last year'];

export function AudienceSelector({ criteria, onCriteriaChange, estimatedReach = 0 }: AudienceSelectorProps) {
  const [newTag, setNewTag] = useState('');

  const updateCriteria = (field: keyof AudienceCriteria, value: unknown) => {
    onCriteriaChange({
      ...criteria,
      [field]: value
    });
  };

  const toggleTier = (tier: string) => {
    const currentTiers = criteria.memberTiers || [];
    const updatedTiers = currentTiers.includes(tier)
      ? currentTiers.filter(t => t !== tier)
      : [...currentTiers, tier];
    updateCriteria('memberTiers', updatedTiers);
  };

  const toggleLocation = (location: string) => {
    const currentLocations = criteria.location || [];
    const updatedLocations = currentLocations.includes(location)
      ? currentLocations.filter(l => l !== location)
      : [...currentLocations, location];
    updateCriteria('location', updatedLocations);
  };

  const addTag = () => {
    if (newTag.trim() && !(criteria.tags || []).includes(newTag.trim())) {
      updateCriteria('tags', [...(criteria.tags || []), newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    updateCriteria('tags', (criteria.tags || []).filter(t => t !== tag));
  };

  return (
    <Card className="flex flex-col h-full">
      <CardHeader className="flex-shrink-0 pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Target className="h-5 w-5 text-green-500" />
          Audience Targeting
        </CardTitle>
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Users className="h-4 w-4" />
          Estimated reach: <Badge variant="secondary">{estimatedReach.toLocaleString()} members</Badge>
        </div>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Member Tiers */}
        <div>
          <Label className="text-base font-medium">Member Tiers</Label>
          <p className="text-sm text-muted-foreground mb-3">Select which membership tiers to target</p>
          <div className="flex flex-wrap gap-2">
            {MEMBER_TIERS.map((tier) => (
              <Button
                key={tier}
                variant={criteria.memberTiers?.includes(tier) ? "default" : "outline"}
                size="sm"
                onClick={() => toggleTier(tier)}
              >
                {tier}
              </Button>
            ))}
          </div>
        </div>

        {/* Age Range */}
        <div>
          <Label className="text-base font-medium">Age Range</Label>
          <p className="text-sm text-muted-foreground mb-3">Target specific age groups</p>
          <div className="flex gap-2 items-center">
            <Input
              type="number"
              placeholder="Min age"
              value={criteria.ageRange?.min || ''}
              onChange={(e) => updateCriteria('ageRange', {
                ...criteria.ageRange,
                min: parseInt(e.target.value) || 0
              })}
              className="w-24"
            />
            <span className="text-muted-foreground">to</span>
            <Input
              type="number"
              placeholder="Max age"
              value={criteria.ageRange?.max || ''}
              onChange={(e) => updateCriteria('ageRange', {
                ...criteria.ageRange,
                max: parseInt(e.target.value) || 100
              })}
              className="w-24"
            />
          </div>
        </div>

        {/* Location */}
        <div>
          <Label className="text-base font-medium">Location</Label>
          <p className="text-sm text-muted-foreground mb-3">Target members in specific cities</p>
          <div className="flex flex-wrap gap-2">
            {LOCATIONS.map((location) => (
              <Button
                key={location}
                variant={criteria.location?.includes(location) ? "default" : "outline"}
                size="sm"
                onClick={() => toggleLocation(location)}
              >
                {location}
              </Button>
            ))}
          </div>
        </div>

        {/* Purchase History */}
        <div>
          <Label className="text-base font-medium">Purchase History</Label>
          <p className="text-sm text-muted-foreground mb-3">Target based on spending behavior</p>
          <div className="space-y-3">
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                placeholder="Min amount (ETB)"
                value={criteria.purchaseHistory?.minAmount || ''}
                onChange={(e) => updateCriteria('purchaseHistory', {
                  ...criteria.purchaseHistory,
                  minAmount: parseFloat(e.target.value) || 0
                })}
                className="flex-1"
              />
              <span className="text-muted-foreground">to</span>
              <Input
                type="number"
                placeholder="Max amount (ETB)"
                value={criteria.purchaseHistory?.maxAmount || ''}
                onChange={(e) => updateCriteria('purchaseHistory', {
                  ...criteria.purchaseHistory,
                  maxAmount: parseFloat(e.target.value) || 0
                })}
                className="flex-1"
              />
            </div>
            <select
              className="w-full p-2 border rounded-md"
              value={criteria.purchaseHistory?.timeframe || ''}
              onChange={(e) => updateCriteria('purchaseHistory', {
                ...criteria.purchaseHistory,
                timeframe: e.target.value
              })}
            >
              <option value="">Select timeframe</option>
              {TIMEFRAMES.map((timeframe) => (
                <option key={timeframe} value={timeframe}>
                  {timeframe}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Loyalty Points */}
        <div>
          <Label className="text-base font-medium">Loyalty Points</Label>
          <p className="text-sm text-muted-foreground mb-3">Target by points balance</p>
          <div className="flex gap-2 items-center">
            <Input
              type="number"
              placeholder="Min points"
              value={criteria.loyaltyPoints?.min || ''}
              onChange={(e) => updateCriteria('loyaltyPoints', {
                ...criteria.loyaltyPoints,
                min: parseInt(e.target.value) || 0
              })}
              className="flex-1"
            />
            <span className="text-muted-foreground">to</span>
            <Input
              type="number"
              placeholder="Max points"
              value={criteria.loyaltyPoints?.max || ''}
              onChange={(e) => updateCriteria('loyaltyPoints', {
                ...criteria.loyaltyPoints,
                max: parseInt(e.target.value) || 0
              })}
              className="flex-1"
            />
          </div>
        </div>

        {/* Custom Tags */}
        <div>
          <Label className="text-base font-medium">Custom Tags</Label>
          <p className="text-sm text-muted-foreground mb-3">Add custom audience tags</p>
          <div className="space-y-3">
            <div className="flex gap-2">
              <Input
                placeholder="Add a tag..."
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addTag()}
                className="flex-1"
              />
              <Button onClick={addTag} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {criteria.tags && criteria.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {criteria.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Quick Presets */}
        <div>
          <Label className="text-base font-medium">Quick Presets</Label>
          <p className="text-sm text-muted-foreground mb-3">Use common audience segments</p>
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCriteriaChange({
                memberTiers: ['Gold', 'Platinum', 'Diamond'],
                tags: ['High Value']
              })}
            >
              High-Value Members
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCriteriaChange({
                memberTiers: ['Bronze', 'Silver'],
                tags: ['New Member']
              })}
            >
              New Members
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onCriteriaChange({
                purchaseHistory: { timeframe: 'Last 30 days' },
                tags: ['Recent Buyer']
              })}
            >
              Recent Buyers
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
