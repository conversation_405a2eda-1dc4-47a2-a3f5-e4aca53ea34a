'use client'

import { useState } from 'react'
import { useChat } from 'ai/react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Loader2, Send, Sparkles, MessageSquare, Zap, Gift, TrendingUp, Users } from 'lucide-react'

interface Campaign {
  id?: string
  type: 'email' | 'sms' | 'push' | 'social'
  objective: string
  targetAudience: string
  content: {
    subject: string
    body: string
    cta: string
  }
  timing?: {
    sendDate?: Date
    timezone?: string
  }
  variations?: unknown[]
  status: 'draft' | 'review' | 'scheduled' | 'sent'
}

interface EnhancedCampaignAgentProps {
  onCampaignGenerated: (campaign: Campaign) => void
}

const quickStartOptions = [
  {
    icon: Gift,
    title: "Holiday Promotion",
    description: "Create a festive campaign for seasonal sales",
    prompt: "I want to create a holiday promotion campaign to boost sales during the festive season. Help me create an engaging email campaign with special offers."
  },
  {
    icon: Users,
    title: "Welcome Series",
    description: "Onboard new customers with a warm welcome",
    prompt: "I need to create a welcome email series for new members who just joined our loyalty program. Make it friendly and informative."
  },
  {
    icon: TrendingUp,
    title: "Product Launch",
    description: "Announce a new product or service",
    prompt: "We're launching a new product and need a compelling campaign to announce it to our customers. Help me create an exciting launch campaign."
  },
  {
    icon: Zap,
    title: "Re-engagement",
    description: "Win back inactive customers",
    prompt: "I want to re-engage customers who haven't visited us in a while. Help me create a compelling 'We miss you' campaign with incentives to come back."
  }
]

const systemPrompt = `You are a helpful AI assistant specializing in creating marketing campaigns. Your role is to:

1. Ask clarifying questions about the campaign goals, target audience, and message tone
2. Generate compelling campaign content including subject lines, body text, and call-to-action
3. Suggest appropriate campaign types (email, SMS, push, social)
4. Keep responses conversational and helpful

When you have enough information, generate a campaign in this exact JSON format:
{
  "type": "email|sms|push|social",
  "objective": "Brief description of campaign goal",
  "targetAudience": "Description of who should receive this",
  "content": {
    "subject": "Subject line or title",
    "body": "Main campaign message",
    "cta": "Call to action button text"
  },
  "status": "draft"
}

Always ask follow-up questions before generating the final campaign to ensure it meets the user's needs.`

export default function EnhancedCampaignAgent({ onCampaignGenerated }: EnhancedCampaignAgentProps) {
  const [conversationStage, setConversationStage] = useState<'initial' | 'chatting' | 'generating' | 'complete'>('initial')

  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    initialMessages: [
      {
        id: 'system',
        role: 'system',
        content: systemPrompt
      },
      {
        id: 'welcome',
        role: 'assistant',
        content: "Hi! I'm here to help you create an amazing marketing campaign. What type of campaign are you looking to create today? You can tell me about your goals, target audience, or what you're trying to promote, and I'll guide you through the process!"
      }
    ],
    onFinish: (message) => {
      // Try to parse campaign JSON from the message
      try {
        const content = message.content
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const campaignData = JSON.parse(jsonMatch[0])
          if (campaignData.type && campaignData.content) {
            setConversationStage('complete')
            onCampaignGenerated(campaignData as Campaign)
          }
        }
      } catch {
        console.log('No campaign JSON found in response, continuing conversation...')
      }
    }
  })

  const handleQuickStart = (prompt: string) => {
    setConversationStage('chatting')
    // Update input and submit
    handleInputChange({ target: { value: prompt } } as React.ChangeEvent<HTMLInputElement>)
    setTimeout(() => {
      const form = document.querySelector('form')
      if (form) {
        form.requestSubmit()
      }
    }, 100)
  }

  const handleFormSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (conversationStage === 'initial') {
      setConversationStage('chatting')
    }
    handleSubmit(e)
  }

  const userMessages = messages.filter(m => m.role === 'user')

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 mb-6">
        <div className="flex items-center gap-3 mb-2">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Sparkles className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold">AI Campaign Assistant</h2>
            <p className="text-muted-foreground text-sm">
              Tell me about your campaign goals and I&apos;ll help you create something amazing
            </p>
          </div>
        </div>

        {/* Conversation Progress */}
        {conversationStage !== 'initial' && (
          <div className="flex items-center gap-2 text-sm">
            <Badge variant="outline" className="gap-1">
              <MessageSquare className="h-3 w-3" />
              {userMessages.length} message{userMessages.length !== 1 ? 's' : ''}
            </Badge>
            {conversationStage === 'complete' && (
              <Badge className="gap-1 bg-green-100 text-green-700">
                <Sparkles className="h-3 w-3" />
                Campaign Ready
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Quick Start Options (only show initially) */}
      {conversationStage === 'initial' && (
        <div className="flex-1 space-y-6">
          <div>
            <h3 className="font-medium mb-4">Quick Start Options</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {quickStartOptions.map((option, index) => {
                const IconComponent = option.icon
                return (
                  <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <Button
                        variant="ghost"
                        className="w-full h-auto p-0 text-left"
                        onClick={() => handleQuickStart(option.prompt)}
                      >
                        <div className="flex items-start gap-3">
                          <div className="p-2 bg-purple-50 rounded-lg flex-shrink-0">
                            <IconComponent className="h-5 w-5 text-purple-600" />
                          </div>
                          <div>
                            <div className="font-medium text-sm">{option.title}</div>
                            <div className="text-xs text-muted-foreground mt-1">
                              {option.description}
                            </div>
                          </div>
                        </div>
                      </Button>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          </div>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or describe your campaign
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Chat Interface */}
      {conversationStage !== 'initial' && (
        <div className="flex-1 flex flex-col min-h-0">
          {/* Messages */}
          <div className="flex-1 pr-4 overflow-y-auto">
            <div className="space-y-4">
              {messages
                .filter(m => m.id !== 'system')
                .map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-2 ${
                        message.role === 'user'
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      <div className="text-sm whitespace-pre-wrap">
                        {message.content}
                      </div>
                    </div>
                  </div>
                ))}

              {isLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-100 rounded-lg px-4 py-2">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Thinking...
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Input Area */}
      {conversationStage !== 'complete' && (
        <div className="flex-shrink-0 mt-6">
          <form onSubmit={handleFormSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder={
                conversationStage === 'initial'
                  ? "Describe your campaign goals, target audience, or what you're promoting..."
                  : "Continue the conversation..."
              }
              disabled={isLoading}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>

          {conversationStage === 'initial' && (
            <p className="text-xs text-muted-foreground mt-2">
              Be specific about your goals, audience, and desired outcomes for the best results.
            </p>
          )}
        </div>
      )}

      {/* Campaign Complete State */}
      {conversationStage === 'complete' && (
        <div className="flex-shrink-0">
          <Card className="bg-green-50 border-green-200">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-green-800">
                <Sparkles className="h-5 w-5" />
                <span className="font-medium">Campaign Created Successfully!</span>
              </div>
              <p className="text-sm text-green-700 mt-1">
                Your campaign is ready. You can now select your target audience and review before sending.
              </p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
