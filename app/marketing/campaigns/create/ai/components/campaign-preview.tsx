'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Mail, MessageSquare, Bell, Share2, Target, Calendar, Sparkles } from 'lucide-react'

interface Campaign {
  id?: string
  type: 'email' | 'sms' | 'push' | 'social'
  objective: string
  targetAudience: string
  content: {
    subject: string
    body: string
    cta: string
  }
  timing?: {
    sendDate?: Date
    timezone?: string
  }
  variations?: unknown[]
  status: 'draft' | 'review' | 'scheduled' | 'sent'
}

interface CampaignPreviewProps {
  campaign: Campaign
}

const typeIcons = {
  email: Mail,
  sms: MessageSquare,
  push: Bell,
  social: Share2
}

const typeColors = {
  email: 'bg-blue-100 text-blue-700',
  sms: 'bg-green-100 text-green-700',
  push: 'bg-orange-100 text-orange-700',
  social: 'bg-purple-100 text-purple-700'
}

export function CampaignPreview({ campaign }: CampaignPreviewProps) {
  const TypeIcon = typeIcons[campaign.type]

  return (
    <div className="space-y-4">
      {/* Campaign Type & Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <TypeIcon className="h-5 w-5" />
          <Badge className={typeColors[campaign.type]}>
            {campaign.type.toUpperCase()}
          </Badge>
        </div>
        <Badge variant="outline" className="text-xs">
          AI Generated
        </Badge>
      </div>

      <Separator />

      {/* Campaign Objective */}
      <div>
        <h4 className="font-medium text-sm text-muted-foreground mb-2">Campaign Objective</h4>
        <p className="text-sm">{campaign.objective}</p>
      </div>

      <Separator />

      {/* Target Audience */}
      <div>
        <h4 className="font-medium text-sm text-muted-foreground mb-2 flex items-center gap-2">
          <Target className="h-4 w-4" />
          Target Audience
        </h4>
        <p className="text-sm">{campaign.targetAudience}</p>
      </div>

      <Separator />

      {/* Message Content */}
      <div>
        <h4 className="font-medium text-sm text-muted-foreground mb-3">Message Preview</h4>

        {/* Message Container */}
        <Card className="bg-gray-50 border-dashed">
          <CardContent className="p-4 space-y-3">
            {/* Subject/Title */}
            {campaign.content.subject && (
              <div>
                <div className="text-xs text-muted-foreground mb-1">
                  {campaign.type === 'email' ? 'Subject Line' : 'Title'}
                </div>
                <div className="font-medium text-sm border-l-4 border-blue-500 pl-2">
                  {campaign.content.subject}
                </div>
              </div>
            )}

            {/* Message Body */}
            <div>
              <div className="text-xs text-muted-foreground mb-1">Message</div>
              <div className="text-sm whitespace-pre-wrap bg-white p-3 rounded border">
                {campaign.content.body}
              </div>
            </div>

            {/* Call to Action */}
            {campaign.content.cta && (
              <div>
                <div className="text-xs text-muted-foreground mb-1">Call to Action</div>
                <div className="inline-block bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium">
                  {campaign.content.cta}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Timing (if available) */}
      {campaign.timing?.sendDate && (
        <>
          <Separator />
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Scheduled Timing
            </h4>
            <p className="text-sm">
              {campaign.timing.sendDate.toLocaleDateString()} at{' '}
              {campaign.timing.sendDate.toLocaleTimeString()}
              {campaign.timing.timezone && ` (${campaign.timing.timezone})`}
            </p>
          </div>
        </>
      )}

      {/* AI Insights */}
      <Separator />
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardContent className="p-3">
          <div className="flex items-start gap-2">
            <Sparkles className="h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0" />
            <div>
              <h5 className="font-medium text-sm text-purple-900 mb-1">AI Insights</h5>
              <p className="text-xs text-purple-700">
                This campaign was optimized for {campaign.type} delivery with personalized content
                tailored to your target audience. The AI considered engagement patterns and best
                practices for maximum impact.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaign Variations (if available) */}
      {campaign.variations && campaign.variations.length > 0 && (
        <>
          <Separator />
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">
              Variations Available ({campaign.variations.length})
            </h4>
            <p className="text-xs text-muted-foreground">
              The AI has generated {campaign.variations.length} variation(s) of this campaign for A/B testing.
            </p>
          </div>
        </>
      )}
    </div>
  )
}
