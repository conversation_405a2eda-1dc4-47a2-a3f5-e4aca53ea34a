'use client';

import { useChat } from 'ai/react';
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { <PERSON><PERSON><PERSON>, Send, Loader2, User, Bot } from 'lucide-react';

interface Campaign {
  type: 'email' | 'sms' | 'push' | 'social';
  objective: string;
  targetAudience: string;
  content: {
    subject: string;
    body: string;
    cta: string;
  };
  timing?: {
    sendDate?: Date;
    timezone?: string;
  };
  variations?: unknown[];
  status: 'draft' | 'review' | 'scheduled' | 'sent';
}

interface CampaignChatProps {
  onCampaignGenerated: (campaign: Campaign) => void;
}

export function CampaignChat({ onCampaignGenerated }: CampaignChatProps) {
  const [hasStarted, setHasStarted] = useState(false);

  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/marketing/chat',
    initialMessages: [
      {
        id: 'welcome',
        role: 'assistant',
        content: `Hi! I'm your AI marketing assistant. I'll help you create an amazing campaign that resonates with your audience.\n\nWhat kind of campaign are you looking to create today? For example:\n• Email campaign to promote new loyalty features\n• SMS campaign for a special offer\n• Push notification for event reminders\n• Social media campaign for brand awareness\n\nJust describe your idea in your own words!`
      }
    ],
    onFinish: (message) => {
      // Try to extract campaign data from AI response
      const campaignData = extractCampaignFromMessage(message.content);
      if (campaignData) {
        onCampaignGenerated(campaignData);
      }
    }
  });

  const extractCampaignFromMessage = (content: string): Campaign | null => {
    try {
      // Don't extract campaigns from simple greetings or short messages
      const simpleGreetings = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening'];
      const isSimpleGreeting = simpleGreetings.some(greeting => 
        content.toLowerCase().trim() === greeting || 
        content.toLowerCase().trim() === greeting + '!'
      );
      
      if (isSimpleGreeting || content.trim().length < 20) {
        return null;
      }

      // Look for explicit campaign creation intent
      const hasExplicitCampaignIntent = 
        content.toLowerCase().includes('create a campaign') ||
        content.toLowerCase().includes('make a campaign') ||
        content.toLowerCase().includes('campaign for') ||
        content.toLowerCase().includes('send a message') ||
        content.toLowerCase().includes('marketing message') ||
        content.toLowerCase().includes('promote') ||
        content.toLowerCase().includes('announce');
      
      // Look for campaign markers or structured content
      const hasStructuredContent = content.includes('📧') || 
                                  content.includes('Campaign:') || 
                                  content.includes('Subject:') ||
                                  content.includes('Message:') ||
                                  content.toLowerCase().includes('email campaign') ||
                                  content.toLowerCase().includes('sms campaign');
      
      if (hasExplicitCampaignIntent || hasStructuredContent) {
        // Extract campaign type
        let type: Campaign['type'] = 'email';
        const lowerContent = content.toLowerCase();
        if (lowerContent.includes('sms') || lowerContent.includes('text message')) type = 'sms';
        else if (lowerContent.includes('push notification') || lowerContent.includes('push')) type = 'push';
        else if (lowerContent.includes('social media') || lowerContent.includes('social')) type = 'social';

        // Extract subject line with improved patterns
        const subjectPatterns = [
          /Subject[:\s]*['"]([^'"]+)['"]/i,
          /Subject[:\s]*([^\n]+)/i,
          /Title[:\s]*['"]([^'"]+)['"]/i,
          /Title[:\s]*([^\n]+)/i
        ];
        let subject = 'Your Campaign Subject';
        for (const pattern of subjectPatterns) {
          const match = content.match(pattern);
          if (match && match[1]?.trim()) {
            subject = match[1].trim();
            break;
          }
        }

        // Extract main content with better patterns
        const bodyPatterns = [
          /(?:Body|Message|Content)[:\s]*['"]([^'"]+)['"]/i,
          /(?:Body|Message|Content)[:\s]*([^\n]+(?:\n[^\n]+)*?)(?=\n\n|$)/i,
          /Here's.*?campaign[:\s]*([^\n]+(?:\n[^\n]+)*?)(?=\n\n|$)/i
        ];
        let body = content.length > 100 ? content.substring(0, 200) + '...' : content;
        for (const pattern of bodyPatterns) {
          const match = content.match(pattern);
          if (match && match[1]?.trim()) {
            body = match[1].trim();
            break;
          }
        }

        // Extract CTA with improved patterns
        const ctaPatterns = [
          /(?:CTA|Call[- ]?to[- ]?action)[:\s]*['"]([^'"]+)['"]/i,
          /(?:CTA|Call[- ]?to[- ]?action)[:\s]*([^\n]+)/i,
          /Button[:\s]*['"]([^'"]+)['"]/i,
          /Click[:\s]*['"]([^'"]+)['"]/i
        ];
        let cta = 'Learn More';
        for (const pattern of ctaPatterns) {
          const match = content.match(pattern);
          if (match && match[1]?.trim()) {
            cta = match[1].trim();
            break;
          }
        }

        // Extract target audience
        const audiencePatterns = [
          /(?:Target|Audience)[:\s]*([^\n]+)/i,
          /(?:for|targeting)[:\s]*([^\n]+)/i
        ];
        let targetAudience = 'All members';
        for (const pattern of audiencePatterns) {
          const match = content.match(pattern);
          if (match && match[1]?.trim()) {
            targetAudience = match[1].trim();
            break;
          }
        }

        // Extract objective
        const objectivePatterns = [
          /(?:Objective|Goal|Purpose)[:\s]*([^\n]+)/i,
          /(?:to|for)[:\s]*(promote|increase|drive|boost|encourage)[^\n]+/i
        ];
        let objective = 'Increase engagement';
        for (const pattern of objectivePatterns) {
          const match = content.match(pattern);
          if (match && match[1]?.trim()) {
            objective = match[1].trim();
            break;
          }
        }

        return {
          type,
          objective,
          targetAudience,
          content: {
            subject,
            body,
            cta
          },
          status: 'draft' as const
        };
      }
    } catch (error) {
      console.error('Error extracting campaign data:', error);
    }
    return null;
  };

  const startConversation = () => {
    setHasStarted(true);
  };

  return (
    <Card className="flex flex-col h-full">
      <CardHeader className="flex-shrink-0 pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Sparkles className="h-5 w-5 text-purple-500" />
          AI Campaign Assistant
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col p-4 min-h-0">
        <div className="flex-1 overflow-y-auto mb-4 pr-2" style={{ minHeight: '300px' }}>
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex gap-3 ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`flex gap-3 max-w-[85%] ${
                    message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                  }`}
                >
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback>
                      {message.role === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4 text-purple-500" />
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div
                    className={`rounded-lg p-3 shadow-sm ${
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted border'
                    }`}
                  >
                    <div className="text-sm whitespace-pre-wrap leading-relaxed">
                      {message.content}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex gap-3 justify-start">
                <div className="flex gap-3 max-w-[85%]">
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarFallback>
                      <Bot className="h-4 w-4 text-purple-500" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-muted border rounded-lg p-3 shadow-sm">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      AI is thinking...
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex-shrink-0 space-y-3">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Describe your campaign idea..."
              className="flex-1"
              disabled={isLoading}
            />
            <Button type="submit" disabled={isLoading || !input.trim()} size="sm">
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>

          {!hasStarted && (
            <div className="text-center py-3 border-t">
              <p className="text-sm text-muted-foreground mb-3">
                Ready to create your next successful campaign?
              </p>
              <Button onClick={startConversation} className="w-full" size="sm">
                <Sparkles className="h-4 w-4 mr-2" />
                Start Creating with AI
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
