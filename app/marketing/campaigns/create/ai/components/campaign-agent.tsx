'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useChat } from 'ai/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Loader2, Send, User, CheckCircle, AlertCircle, Sparkles, MessageSquare } from 'lucide-react';
import { generateId } from 'ai';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Textarea } from '@/components/ui/textarea';

interface Campaign {
  id?: string;
  type: 'email' | 'sms' | 'push' | 'social';
  objective: string;
  targetAudience: string;
  content: {
    subject: string;
    body: string;
    cta: string;
  };
  timing?: {
    sendDate?: Date;
    timezone?: string;
  };
  variations?: unknown[];
  status: 'draft' | 'review' | 'scheduled' | 'sent';
}

interface CampaignAgentProps {
  onCampaignGenerated: (campaign: Campaign) => void;
}

interface ConversationState {
  state: 'gathering' | 'generation' | 'complete';
  requirements: Record<string, unknown>;
  validation: {
    isComplete: boolean;
    completeness: number;
    missingFields: string[];
    suggestions: string[];
  };
}

export default function CampaignAgent({ onCampaignGenerated }: CampaignAgentProps) {
  const [sessionId] = useState(() => generateId());
  const [conversationState, setConversationState] = useState<ConversationState | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [errorMessage, setError] = useState<string | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Define handleCampaignGeneration before it's used in useEffect
  const handleCampaignGeneration = useCallback((requirements: Record<string, unknown>) => {
    setIsGenerating(true);

    // Safely extract nested properties with type checking
    const contentDetails = requirements.contentDetails as Record<string, unknown> || {};
    const targetAudience = requirements.targetAudience as Record<string, unknown> || {};

    // Convert agent requirements to campaign format
    const campaignType = requirements.type as string;
    const validType = ['email', 'sms', 'push', 'social'].includes(campaignType) ?
      campaignType as 'email' | 'sms' | 'push' | 'social' : 'email';

    const campaign: Campaign = {
      type: validType,
      objective: (requirements.objective as string) || '',
      targetAudience: (targetAudience.description as string) || '',
      content: {
        subject: (contentDetails.subject as string) || '',
        body: (contentDetails.body as string) || '',
        cta: (contentDetails.cta as string) || '',
      },
      status: 'draft',
    };

    // Trigger campaign generation
    setTimeout(() => {
      setIsGenerating(false);
      onCampaignGenerated(campaign);
    }, 1000);
  }, [onCampaignGenerated]);

  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading
  } = useChat({
    api: '/api/marketing/campaigns/create/ai/agent-chat',
    id: sessionId,
    onResponse: (response) => {
      // Extract conversation state from custom header
      const stateHeader = response.headers.get('x-conversation-state');
      if (stateHeader) {
        try {
          const state = JSON.parse(stateHeader);
          setConversationState(state);
        } catch (e) {
          console.error('Failed to parse conversation state:', e);
        }
      }
    },
    onError: (err) => {
      console.error('Chat API error:', err);
      setError('Failed to connect to chat API. Please try again later.');
    }
  });

  // Fetch conversation state from server on component mount
  useEffect(() => {
    const fetchConversationState = async () => {
      try {
        const response = await fetch(`/api/marketing/campaigns/create/ai/agent-chat?sessionId=${sessionId}`);
        if (response.ok) {
          const state = await response.json();
          setConversationState(state);

          // Check if ready for campaign generation
          if (state.state === 'generation' && state.validation.isComplete) {
            handleCampaignGeneration(state.requirements);
          }
        }
      } catch (error) {
        console.error('Failed to fetch conversation state:', error);
        setError('Failed to fetch conversation state. Please try again later.');
      }
    };

    fetchConversationState();
  }, [sessionId, handleCampaignGeneration]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Initialize conversation with greeting
  useEffect(() => {
    if (messages.length === 0) {
      // Auto-send initial greeting to start the conversation
      // This would be handled by the agent automatically on first load
    }
  }, [messages.length]);

  return (
    <div className="flex flex-col h-[600px] bg-white rounded-xl shadow-lg border border-blue-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-white" />
            <h2 className="font-semibold text-white">Campaign Assistant</h2>
          </div>
          <Badge variant="outline" className="bg-white/10 hover:bg-white/20 text-white border-white/20">
            <Sparkles className="h-3 w-3 mr-1" />
            Gemini 2.5 Flash
          </Badge>
        </div>
      </div>

      {/* Campaign generation progress */}
      <div className="px-6 py-3 bg-blue-50 border-b border-blue-100">
        {conversationState && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-blue-800">
                Campaign Requirements
              </span>
              <Badge variant={conversationState.validation.isComplete ? "default" : "outline"} className={`text-xs ${conversationState.validation.isComplete ? "bg-green-500 hover:bg-green-600" : ""}`}>
                {conversationState.validation.completeness}% Complete
              </Badge>
            </div>

            <Progress
              value={conversationState.validation.completeness}
              className="h-2 bg-blue-100"
            />

            <AnimatePresence>
              {conversationState.validation.isComplete && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="flex items-center gap-1.5 text-xs text-green-600 mt-1"
                >
                  <CheckCircle className="h-4 w-4" />
                  <span className="font-medium">All requirements gathered!</span>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Messages */}
      <div ref={scrollAreaRef} className="flex-1 px-5 py-4 overflow-auto">
        <div className="space-y-6">
          {messages.length === 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 shadow-sm">
                <CardContent className="p-5">
                  <div className="flex items-start gap-4">
                    <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                      <Sparkles className="h-4 w-4 text-white" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="font-semibold text-blue-900">Campaign Assistant</h3>
                      <p className="text-blue-800">
                        Hello! I&apos;m your campaign creation assistant powered by Gemini 2.5 Flash.
                        I&apos;ll help you create effective marketing campaigns by asking a few questions.
                        Let&apos;s start by discussing what type of campaign you&apos;d like to create.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={cn(
                "flex",
                message.role === "user" ? "justify-end" : "justify-start"
              )}
            >
              <div className={cn(
                "max-w-[80%] rounded-2xl px-4 py-3",
                message.role === "user"
                  ? "bg-blue-600 text-white rounded-tr-none"
                  : "bg-gray-100 text-gray-800 rounded-tl-none"
              )}>
                <div className="flex items-start gap-3">
                  {message.role !== "user" && (
                    <div className="h-6 w-6 rounded-full bg-blue-600 flex items-center justify-center mt-1">
                      <Sparkles className="h-3 w-3 text-white" />
                    </div>
                  )}
                  <div>
                    {message.content}
                  </div>
                  {message.role === "user" && (
                    <div className="h-6 w-6 rounded-full bg-gray-600 flex items-center justify-center mt-1">
                      <User className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}

          {/* Error message */}
          {errorMessage && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex justify-center"
            >
              <div className="bg-red-50 text-red-800 px-4 py-2 rounded-lg flex items-center gap-2 text-sm">
                <AlertCircle className="h-4 w-4" />
                {errorMessage}
              </div>
            </motion.div>
          )}

          {/* Loading indicator */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center gap-2 text-blue-600 px-4 py-2"
            >
              <div className="flex items-center gap-1">
                <motion.span
                  animate={{ y: [0, -5, 0] }}
                  transition={{ repeat: Infinity, duration: 1, delay: 0 }}
                  className="h-2 w-2 bg-blue-600 rounded-full"
                />
                <motion.span
                  animate={{ y: [0, -5, 0] }}
                  transition={{ repeat: Infinity, duration: 1, delay: 0.2 }}
                  className="h-2 w-2 bg-blue-600 rounded-full"
                />
                <motion.span
                  animate={{ y: [0, -5, 0] }}
                  transition={{ repeat: Infinity, duration: 1, delay: 0.4 }}
                  className="h-2 w-2 bg-blue-600 rounded-full"
                />
              </div>
              <span className="text-base font-medium text-blue-800">Thinking...</span>
            </motion.div>
          )}

          {/* Campaign generation indicator */}
          {isGenerating && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-center gap-2 text-blue-600 px-4 py-2"
            >
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-base font-medium text-blue-800">Generating your campaign...</span>
            </motion.div>
          )}
        </div>
      </div>

      {/* Input area */}
      <div className="border-t border-blue-100 p-4 bg-blue-50">
        <form onSubmit={handleSubmit} className="relative">
          <Textarea
            value={input}
            onChange={handleInputChange}
            placeholder="Type your message..."
            disabled={isLoading}
            className="min-h-[80px] pr-12 resize-none bg-white border-blue-200 focus:border-blue-400 focus:ring-blue-400 text-base"
            onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (input.trim()) {
                  handleSubmit(e as unknown as React.FormEvent<HTMLFormElement>);
                }
              }
            }}
          />
          <Button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="absolute bottom-3 right-3 h-9 w-9 p-0 rounded-full bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Send className="h-4 w-4" />
          </Button>
        </form>
      </div>
    </div>
  );
}
