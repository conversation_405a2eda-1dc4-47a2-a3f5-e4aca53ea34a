import { openai } from '@ai-sdk/openai';
import { streamText } from 'ai';
import { z } from 'zod';
import { 
  ConversationContext, 
  ConversationState, 
  detectIntent,
  getSystemPrompt,
  getNextState,
  validateCampaignCompleteness,
  createInitialContext
} from '../../lib/agent-core';

export const maxDuration = 30;

// In-memory storage for conversation contexts (in production, use Redis or database)
const conversationContexts = new Map<string, ConversationContext>();

export async function POST(req: Request) {
  try {
    const { messages, sessionId } = await req.json();
    
    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    const lastMessage = messages[messages.length - 1];
    if (!lastMessage || lastMessage.role !== 'user') {
      return new Response('Last message must be from user', { status: 400 });
    }

    // Get or create conversation context
    let context = conversationContexts.get(sessionId);
    if (!context) {
      context = createInitialContext();
      context.sessionId = sessionId;
      conversationContexts.set(sessionId, context);
    }

    // Add user message to history
    context.messageHistory.push({
      role: 'user',
      content: lastMessage.content,
      timestamp: new Date()
    });

    // Detect user intent
    const intentResult = await detectIntent(lastMessage.content, context);
    context.requirements.intent = intentResult.intent;

    // Define tools for structured data extraction
    const extractionTools = {
      extractCampaignType: {
        description: 'Extract campaign type from user message',
        parameters: z.object({
          campaignType: z.enum(['promotional', 'announcement', 'birthday', 'loyalty', 'custom']),
          reasoning: z.string()
        }),
        execute: async ({ campaignType, reasoning }: { campaignType: string; reasoning: string }) => {
          context!.requirements.campaignType = campaignType as 'promotional' | 'announcement' | 'birthday' | 'loyalty' | 'custom';
          return { success: true, extracted: campaignType, reasoning };
        }
      },

      extractTargetAudience: {
        description: 'Extract target audience information from user message',
        parameters: z.object({
          type: z.enum(['all', 'tier', 'segment', 'individual']),
          criteria: z.string().optional(),
          reasoning: z.string()
        }),
        execute: async ({ type, criteria, reasoning }: { type: string; criteria?: string; reasoning: string }) => {
          context!.requirements.targetAudience = { type: type as 'all' | 'tier' | 'segment' | 'individual', criteria: criteria ? { description: criteria } : undefined };
          return { success: true, extracted: { type, criteria }, reasoning };
        }
      },

      extractGoal: {
        description: 'Extract campaign goal from user message',
        parameters: z.object({
          goal: z.string(),
          reasoning: z.string()
        }),
        execute: async ({ goal, reasoning }: { goal: string; reasoning: string }) => {
          context!.requirements.goal = goal;
          return { success: true, extracted: goal, reasoning };
        }
      },

      extractTone: {
        description: 'Extract message tone preference from user message',
        parameters: z.object({
          tone: z.enum(['professional', 'friendly', 'urgent', 'celebratory', 'custom']),
          reasoning: z.string()
        }),
        execute: async ({ tone, reasoning }: { tone: string; reasoning: string }) => {
          context!.requirements.tone = tone as 'professional' | 'friendly' | 'urgent' | 'celebratory' | 'custom';
          return { success: true, extracted: tone, reasoning };
        }
      },

      extractContent: {
        description: 'Extract content details from user message',
        parameters: z.object({
          subject: z.string().optional(),
          mainMessage: z.string().optional(),
          callToAction: z.string().optional(),
          additionalInfo: z.string().optional(),
          reasoning: z.string()
        }),
        execute: async ({ subject, mainMessage, callToAction, additionalInfo, reasoning }: { 
          subject?: string; 
          mainMessage?: string; 
          callToAction?: string; 
          additionalInfo?: string; 
          reasoning: string 
        }) => {
          if (!context!.requirements.contentDetails) {
            context!.requirements.contentDetails = {};
          }
          
          if (subject) context!.requirements.contentDetails.subject = subject;
          if (mainMessage) context!.requirements.contentDetails.mainMessage = mainMessage;
          if (callToAction) context!.requirements.contentDetails.callToAction = callToAction;
          if (additionalInfo) context!.requirements.contentDetails.additionalInfo = additionalInfo;
          
          return { 
            success: true, 
            extracted: { subject, mainMessage, callToAction, additionalInfo }, 
            reasoning 
          };
        }
      },

      generateCampaign: {
        description: 'Generate the final campaign when all requirements are complete',
        parameters: z.object({
          campaignData: z.object({
            type: z.string(),
            subject: z.string(),
            body: z.string(),
            cta: z.string().optional(),
            targetAudience: z.string(),
            objective: z.string()
          })
        }),
        execute: async ({ campaignData }: { campaignData: Record<string, unknown> }) => {
          // Mark as ready for generation
          context!.state = ConversationState.GENERATION;
          return { success: true, campaignData, readyForGeneration: true };
        }
      }
    };

    // Get system prompt for current state
    const systemPrompt = getSystemPrompt(context.state, context.requirements);

    // Stream response with tools
    const result = streamText({
      model: openai('gpt-4'),
      system: systemPrompt,
      messages: messages,
      tools: extractionTools,
      maxTokens: 1000,
      temperature: 0.7,
      onFinish: async ({ response }) => {
        // Add assistant message to history
        const lastMessage = response.messages[response.messages.length - 1];
        const content = typeof lastMessage?.content === 'string' 
          ? lastMessage.content 
          : Array.isArray(lastMessage?.content) 
            ? lastMessage.content.map(part => 'text' in part ? part.text : '').join('')
            : '';
        
        context!.messageHistory.push({
          role: 'assistant',
          content,
          timestamp: new Date()
        });

        // Update conversation state
        const nextState = getNextState(context!.state, context!.requirements.intent, context!.requirements);
        context!.state = nextState;

        // Update completeness
        const validation = validateCampaignCompleteness(context!.requirements);
        context!.requirements.completeness = validation.completeness;

        // Save updated context
        conversationContexts.set(sessionId, context!);
      }
    });

    return result.toTextStreamResponse({
      headers: {
        'X-Conversation-State': context.state,
        'X-Completeness': context.requirements.completeness.toString(),
        'X-Session-Id': sessionId
      }
    });

  } catch (error) {
    console.error('Agent chat error:', error);
    return new Response('Internal server error', { status: 500 });
  }
}

// GET endpoint to retrieve conversation context
export async function GET(req: Request) {
  const url = new URL(req.url);
  const sessionId = url.searchParams.get('sessionId');
  
  if (!sessionId) {
    return new Response('Session ID required', { status: 400 });
  }

  const context = conversationContexts.get(sessionId);
  if (!context) {
    return new Response('Session not found', { status: 404 });
  }

  return Response.json({
    state: context.state,
    requirements: context.requirements,
    messageHistory: context.messageHistory.slice(-10), // Last 10 messages
    validation: validateCampaignCompleteness(context.requirements)
  });
}
