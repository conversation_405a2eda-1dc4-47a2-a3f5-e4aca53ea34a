'use client'

import { useState } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON>rkles, ArrowLeft, Wand2, CheckCircle, Target, Send, ArrowRight } from 'lucide-react'
import EnhancedCampaignAgent from './components/enhanced-campaign-agent'
import { CampaignPreview } from './components/campaign-preview'
import TargetAudienceStep from '../components/target-audience-step'
import ReviewAndSendStep from '../components/review-and-send-step'

interface Campaign {
  id?: string
  type: 'email' | 'sms' | 'push' | 'social'
  objective: string
  targetAudience: string
  content: {
    subject: string
    body: string
    cta: string
  }
  timing?: {
    sendDate?: Date
    timezone?: string
  }
  variations?: unknown[]
  status: 'draft' | 'review' | 'scheduled' | 'sent'
}

interface TraditionalCampaignData {
  name: string
  description?: string
  message_title?: string
  message_content: string
  target_type: 'all' | 'tier' | 'individual' | 'custom'
  target_criteria: Record<string, unknown>
  scheduled_at?: string
}

type Step = 'chat' | 'audience' | 'review' | 'send'

const steps = [
  {
    id: 'chat',
    title: 'Create Campaign',
    description: 'Work with AI to design your campaign',
    icon: Sparkles
  },
  {
    id: 'audience',
    title: 'Select Audience',
    description: 'Choose who will receive your message',
    icon: Target
  },
  {
    id: 'review',
    title: 'Review & Send',
    description: 'Review and send your campaign',
    icon: Send
  }
]

export default function EnhancedAICampaignCreatePage() {
  useRequireAuth()
  const router = useRouter()
  const [currentCampaign, setCurrentCampaign] = useState<Campaign | null>(null)
  const [campaignData, setCampaignData] = useState<TraditionalCampaignData>({
    name: '',
    description: '',
    message_title: '',
    message_content: '',
    target_type: 'all',
    target_criteria: {},
    scheduled_at: undefined
  })
  const [recipientCount, setRecipientCount] = useState(0)
  const [currentStep, setCurrentStep] = useState<Step>('chat')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleCampaignGenerated = (campaign: Campaign) => {
    setCurrentCampaign(campaign)
    // Convert AI campaign to traditional format for compatibility
    setCampaignData({
      name: campaign.objective || 'AI Generated Campaign',
      description: `AI-generated ${campaign.type} campaign: ${campaign.objective}`,
      message_title: campaign.content.subject || campaign.objective,
      message_content: campaign.content.body || '',
      target_type: 'all',
      target_criteria: {},
      scheduled_at: undefined
    })
    // Move to audience selection step
    setCurrentStep('audience')
  }

  const updateCampaignData = (updates: Partial<TraditionalCampaignData>) => {
    setCampaignData(prev => ({ ...prev, ...updates }))
  }

  const handleNextStep = () => {
    if (currentStep === 'audience') {
      setCurrentStep('review')
    }
  }

  const handlePrevStep = () => {
    if (currentStep === 'review') {
      setCurrentStep('audience')
    } else if (currentStep === 'audience') {
      setCurrentStep('chat')
    }
  }

  const handleSendCampaign = async () => {
    try {
      setIsSubmitting(true)
      setError(null)

      // Create campaign using existing API
      const createResponse = await fetch('/api/marketing/campaigns', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(campaignData)
      })

      if (!createResponse.ok) {
        const errorData = await createResponse.json()
        throw new Error(errorData.error || 'Failed to create campaign')
      }

      const createData = await createResponse.json()
      const campaignId = createData.campaign.id

      // Send campaign
      const sendResponse = await fetch(`/api/marketing/campaigns/${campaignId}/send`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ confirm: true })
      })

      if (!sendResponse.ok) {
        const errorData = await sendResponse.json()
        throw new Error(errorData.error || 'Failed to send campaign')
      }

      router.push(`/marketing/campaigns/${campaignId}`)
    } catch (err) {
      console.error('Error sending campaign:', err)
      setError(err instanceof Error ? err.message : 'Failed to send campaign')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getCurrentStepIndex = () => {
    return steps.findIndex(step => step.id === currentStep)
  }

  const progress = ((getCurrentStepIndex() + 1) / steps.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-7xl p-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/marketing/campaigns')}
                className="hover:bg-gray-50"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                  <Sparkles className="h-8 w-8 text-purple-500" />
                  AI Campaign Assistant
                </h1>
                <p className="text-gray-600 mt-1">Create compelling marketing campaigns with the help of AI</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={() => router.push('/marketing/campaigns/create')}
                size="sm"
                className="hover:bg-gray-50"
              >
                <Wand2 className="h-4 w-4 mr-2" />
                Use Manual Creator
              </Button>
            </div>
          </div>
        </div>

        {/* Progress Indicator */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">
                Step {getCurrentStepIndex() + 1} of {steps.length}: {steps[getCurrentStepIndex()].title}
              </h3>
              <span className="text-sm text-muted-foreground">
                {Math.round(progress)}% complete
              </span>
            </div>
            <Progress value={progress} className="w-full" />
            <p className="text-sm text-muted-foreground">
              {steps[getCurrentStepIndex()].description}
            </p>
          </div>
        </div>

        {/* Step Progress Bar */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const StepIcon = step.icon
              const isActive = currentStep === step.id
              const isCompleted = getCurrentStepIndex() > index

              return (
                <div key={step.id} className="flex items-center">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-full ${
                      isCompleted ? 'bg-green-100 text-green-600' :
                      isActive ? 'bg-purple-100 text-purple-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <StepIcon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="hidden sm:block">
                      <Badge variant={isActive ? 'default' : isCompleted ? 'secondary' : 'outline'}>
                        {step.title}
                      </Badge>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-px mx-4 ${
                      isCompleted ? 'bg-green-300' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Left Column - Main Interface */}
          <div className="xl:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 h-[700px]">
                {currentStep === 'chat' && (
                  <EnhancedCampaignAgent onCampaignGenerated={handleCampaignGenerated} />
                )}

                {currentStep === 'audience' && (
                  <div className="h-full flex flex-col">
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-2">Select Your Target Audience</h3>
                      <p className="text-muted-foreground">
                        Choose who will receive your campaign. You can target all members, specific tiers, or create custom criteria.
                      </p>
                    </div>
                    <div className="flex-1">
                      <TargetAudienceStep
                        data={campaignData}
                        updateData={updateCampaignData}
                        onRecipientCountChange={setRecipientCount}
                      />
                    </div>
                  </div>
                )}

                {currentStep === 'review' && (
                  <div className="h-full">
                    <ReviewAndSendStep
                      data={campaignData}
                      onSend={handleSendCampaign}
                      isSubmitting={isSubmitting}
                      recipientCount={recipientCount}
                    />
                  </div>
                )}
              </div>
            </div>

          {/* Right Column - Campaign Preview */}
          <div className="xl:col-span-1">
            {currentCampaign && currentStep !== 'chat' && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 h-[700px]">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-500" />
                    Campaign Preview
                  </h3>
                </div>
                <div className="h-full overflow-y-auto">
                  <CampaignPreview campaign={currentCampaign} />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        {currentStep !== 'chat' && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
            <div className="flex justify-between items-center">
              <Button
                variant="outline"
                onClick={handlePrevStep}
                disabled={isSubmitting}
                className="hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>

              <div className="flex items-center gap-2">
                {currentStep === 'audience' && (
                  <Button
                    onClick={handleNextStep}
                    disabled={isSubmitting}
                    className="hover:shadow-md transition-shadow"
                  >
                    Review Campaign
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
