'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRequireAuth } from '@/hooks/use-auth'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  MessageSquare,
  Plus,
  Search,
  Filter,
  Edit,
  Eye,
  Copy,
  Trash2,
  Send,
  Sparkles
} from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'

interface Campaign {
  id: string
  name: string
  description?: string
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'failed'
  target_type: string
  total_recipients: number
  successful_sends: number
  failed_sends: number
  delivery_rate: number
  scheduled_at?: string
  sent_at?: string
  created_at: string
  updated_at: string
}

const StatusBadge = ({ status }: { status: Campaign['status'] }) => {
  const variants = {
    draft: 'secondary',
    scheduled: 'outline',
    sending: 'default',
    sent: 'default',
    failed: 'destructive'
  } as const

  const labels = {
    draft: 'Draft',
    scheduled: 'Scheduled',
    sending: 'Sending...',
    sent: 'Sent',
    failed: 'Failed'
  }

  return (
    <Badge variant={variants[status]}>
      {labels[status]}
    </Badge>
  )
}

export default function CampaignsPage() {
  const { user } = useRequireAuth()
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [filteredCampaigns, setFilteredCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      fetchCampaigns()
    }
  }, [user])

  const filterCampaigns = useCallback(() => {
    let filtered = campaigns

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(campaign =>
        campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        campaign.description?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(campaign => campaign.status === statusFilter)
    }

    setFilteredCampaigns(filtered)
  }, [campaigns, searchQuery, statusFilter])

  useEffect(() => {
    filterCampaigns()
  }, [filterCampaigns])

  const fetchCampaigns = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/marketing/campaigns')
      if (!response.ok) {
        throw new Error('Failed to fetch campaigns')
      }
      const data = await response.json()
      setCampaigns(data.campaigns || [])
    } catch (error) {
      console.error('Error fetching campaigns:', error)
      setError('Failed to load campaigns')
    } finally {
      setLoading(false)
    }
  }

  const deleteCampaign = async (campaignId: string) => {
    if (!confirm('Are you sure you want to delete this campaign?')) {
      return
    }

    try {
      const response = await fetch(`/api/marketing/campaigns/${campaignId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setCampaigns(campaigns.filter(c => c.id !== campaignId))
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to delete campaign')
      }
    } catch (error) {
      console.error('Error deleting campaign:', error)
      alert('Failed to delete campaign')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto max-w-7xl p-6 space-y-8">
          {/* Header Skeleton */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/3"></div>
            </div>
          </div>

          {/* Filters Skeleton */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse flex gap-4">
              <div className="h-10 bg-gray-200 rounded flex-1"></div>
              <div className="h-10 bg-gray-200 rounded w-48"></div>
            </div>
          </div>

          {/* Campaign Cards Skeleton */}
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="animate-pulse space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto max-w-7xl p-6 space-y-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <MessageSquare className="h-8 w-8" />
                Campaigns
              </h1>
              <p className="text-gray-600 mt-1">Manage your marketing campaigns</p>
            </div>
            <div className="flex items-center gap-3">
              <Link href="/marketing/campaigns/create">
                <Button className="flex items-center gap-2 hover:shadow-md transition-shadow">
                  <Plus className="h-4 w-4" />
                  New Campaign
                </Button>
              </Link>
              <Link href="/marketing/campaigns/create/ai">
                <Button variant="outline" className="flex items-center gap-2 hover:shadow-md transition-shadow">
                  <Sparkles className="h-4 w-4" />
                  AI Assistant
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search campaigns..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-full sm:w-48">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="sending">Sending</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Campaign List */}
        {error ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
            <p className="text-red-600">{error}</p>
            <Button onClick={fetchCampaigns} className="mt-4">
              Try Again
            </Button>
          </div>
        ) : filteredCampaigns.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
            {campaigns.length === 0 ? (
              <>
                <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No campaigns yet</h3>
                <p className="text-muted-foreground mb-4">
                  Create your first campaign to start sending targeted messages to your members.
                </p>
                <Link href="/marketing/campaigns/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Campaign
                  </Button>
                </Link>
              </>
            ) : (
              <>
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No campaigns found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your search or filter criteria.
                </p>
              </>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredCampaigns.map((campaign) => (
              <div key={campaign.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-3">
                      <h3 className="font-semibold text-lg">{campaign.name}</h3>
                      <StatusBadge status={campaign.status} />
                    </div>

                    {campaign.description && (
                      <p className="text-muted-foreground">{campaign.description}</p>
                    )}

                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <span className="capitalize">{campaign.target_type} targeting</span>
                      <span>•</span>
                      <span>{campaign.total_recipients} recipients</span>
                      {campaign.delivery_rate > 0 && (
                        <>
                          <span>•</span>
                          <span className="text-green-600">{campaign.delivery_rate}% delivered</span>
                        </>
                      )}
                      <span>•</span>
                      <span>Created {formatDistanceToNow(new Date(campaign.created_at))} ago</span>
                    </div>

                    {campaign.scheduled_at && (
                      <div className="text-sm text-blue-600">
                        Scheduled for {new Date(campaign.scheduled_at).toLocaleString()}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Link href={`/marketing/campaigns/${campaign.id}`}>
                      <Button variant="ghost" size="sm" title="View">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>

                    {campaign.status === 'draft' && (
                      <>
                        <Link href={`/marketing/campaigns/${campaign.id}/edit`}>
                          <Button variant="ghost" size="sm" title="Edit">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>

                        <Button
                          variant="ghost"
                          size="sm"
                          title="Send Campaign"
                          className="text-green-600 hover:text-green-700"
                          onClick={() => {
                            // TODO: Implement quick send
                            alert('Quick send feature coming soon!')
                          }}
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </>
                    )}

                    <Button variant="ghost" size="sm" title="Duplicate">
                      <Copy className="h-4 w-4" />
                    </Button>

                    {campaign.status === 'draft' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        title="Delete"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => deleteCampaign(campaign.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {filteredCampaigns.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>
                Showing {filteredCampaigns.length} of {campaigns.length} campaigns
              </span>
              <span>
                Total messages sent: {campaigns.reduce((sum, c) => sum + c.successful_sends, 0).toLocaleString()}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
