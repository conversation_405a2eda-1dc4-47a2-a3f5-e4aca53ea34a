'use client'

import { useEffect, use } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompany } from '@/contexts/company-context'
import { ClientOnly } from '@/lib/hydration-safe'
import { format } from 'date-fns'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import { ArrowLeft, Edit, Plus, Minus, Gift, BarChart3 } from 'lucide-react'
import { useMember } from '@/hooks/use-members'
import { TierBenefitsCard } from './components/tier-benefits-card'
import { OptimizedTelegramLinkGenerator } from '@/components/optimized-telegram-link-generator'
import { MemberPurchaseAnalytics } from '@/components/member-purchase-analytics'

// Define the Transaction type used in the component
interface TransactionData {
  id: string
  member_id: string
  points_change: number
  transaction_type: string
  description: string
  created_at: string
}

// Custom hook to fetch member transactions using TanStack Query
function useMemberTransactions(memberId: string | undefined, companyId: string | undefined) {
  return useQuery({
    queryKey: ['memberTransactions', memberId, companyId],
    queryFn: async () => {
      if (!memberId || !companyId) return { data: [] as TransactionData[] };

      const response = await fetch(`/api/transactions/member?memberId=${memberId}&companyId=${companyId}&limit=50`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to fetch transactions');
      }
      const result = await response.json();
      return result as { data: TransactionData[] };
    },
    enabled: !!memberId && !!companyId, // Only run when both values exist
    staleTime: 60 * 1000, // Cache for 1 minute
    refetchOnWindowFocus: false, // Prevent refetch on tab focus
    retry: 1, // Only retry once on failure
  });
}

export default function MemberDetailPage({ params }: { params: Promise<{ id: string }> }) {
  // Unwrap params with React.use()
  const unwrappedParams = use(params);
  const memberId = unwrappedParams.id;

  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const router = useRouter()

  // Use React Query hooks for data fetching with optimized configuration
  const {
    data: member,
    isLoading: memberLoading,
    refetch: refetchMember
  } = useMember(memberId);

  const {
    data: transactionsData,
    isLoading: transactionsLoading
  } = useMemberTransactions(memberId, company?.id);

  // Force a refetch when the component mounts to ensure fresh data
  useEffect(() => {
    if (memberId && !memberLoading) {
      // Refetch the member data to ensure we have the latest data
      refetchMember();
    }
  }, [memberId, refetchMember, memberLoading]);

  // Properly sorted transactions
  const transactions = transactionsData?.data || [];

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login')
    }
  }, [authLoading, user, router])

  // Combined loading state
  const isLoading = authLoading || companyLoading || memberLoading

  // Render loading skeleton during loading
  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="mb-6">
          <div className="h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded-md mb-4"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <Card>
              <CardHeader>
                <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="h-6 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                </div>
                <div className="space-y-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="flex justify-between">
                      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="p-3 border rounded-md">
                      <div className="flex justify-between mb-2">
                        <div className="h-5 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                      <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  if (!company) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">No Company Account Found</h2>
        <p className="text-muted-foreground mb-6">You need to create a company profile before viewing member details.</p>
        <Button onClick={() => router.push('/company/create')}>
          Create Company Profile
        </Button>
      </div>
    )
  }

  if (!member) {
    return (
      <div className="container mx-auto py-8 text-center">
        <h2 className="text-2xl font-semibold mb-4">Member Not Found</h2>
        <p className="text-muted-foreground mb-6">The member you&apos;re looking for doesn&apos;t exist or doesn&apos;t belong to your company.</p>
        <Button onClick={() => router.push('/members')}>
          Back to Members
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      {/* Header */}
      <div className="mb-8">
        <Button variant="outline" asChild className="mb-4">
          <Link href="/members">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Members
          </Link>
        </Button>

        {/* Member Header Info */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-gray-200 bg-gray-100">
            {member.profile_image_url ? (
              <Image
                src={member.profile_image_url}
                alt={`${member.name} profile`}
                className="w-full h-full object-cover"
                width={64}
                height={64}
                unoptimized={true}
                priority={true}
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  const fallback = e.currentTarget.parentElement?.querySelector('[data-initials]');
                  if (fallback) {
                    fallback.classList.remove('hidden');
                    fallback.classList.add('flex');
                  }
                }}
              />
            ) : null}
            <div
              data-initials="true"
              className={`absolute inset-0 flex items-center justify-center text-sm font-semibold text-gray-600 ${
                member.profile_image_url ? 'hidden' : 'flex'
              }`}
            >
              {member.name
                .split(' ')
                .map((n: string) => n[0])
                .join('')}
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold">{member.name}</h1>
            <div className="flex items-center gap-3 mt-1">
              <Badge variant={member.loyalty_tier ? "default" : "secondary"}>
                {member.loyalty_tier || 'Basic'}
              </Badge>
              <span className="text-muted-foreground">ID: {member.loyalty_id}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid - Improved balanced layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Left Column - Personal Info & Points */}
        <div className="space-y-6">

          {/* Contact Information & Points Summary Combined */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
            <Card>
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Email:</span>
                  <span className="text-sm font-medium">{member.email || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Phone:</span>
                  <span className="text-sm font-medium">{member.phone_number || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Birthday:</span>
                  <ClientOnly fallback="Loading...">
                    {() => (
                      <span className="text-sm font-medium">
                        {member.birthday_month_day ? (() => {
                          // birthday_month_day is in MM-DD format, e.g., "03-09"
                          const [month, day] = member.birthday_month_day.split('-')
                          const currentYear = new Date().getFullYear()
                          const date = new Date(currentYear, parseInt(month) - 1, parseInt(day))
                          return format(date, 'MMM d')
                        })() : 'N/A'}
                      </span>
                    )}
                  </ClientOnly>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Joined:</span>
                  <ClientOnly fallback="Loading...">
                    {() => (
                      <span className="text-sm font-medium">
                        {format(new Date(member.registration_date), 'MMM d, yyyy')}
                      </span>
                    )}
                  </ClientOnly>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 border-blue-200 dark:border-blue-800">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg text-blue-900 dark:text-blue-100">Points Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-muted-foreground">Available Points</span>
                  <span className="font-bold text-2xl text-green-600 dark:text-green-400">
                    {member.available_points || 0}
                  </span>
                </div>
                <Separator />
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Lifetime:</span>
                    <span className="font-semibold">{member.lifetime_points || 0}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Redeemed:</span>
                    <span className="text-red-600 dark:text-red-400">{member.redeemed_points || 0}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Expired:</span>
                    <span className="text-gray-500">{member.expired_points || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions - Enhanced */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <Button asChild variant="outline" className="h-12 justify-start">
                  <Link href={`/members/${member.id}/edit`}>
                    <Edit className="mr-3 h-4 w-4" />
                    <span>Edit Member</span>
                  </Link>
                </Button>
                <Button asChild variant="outline" className="h-12 justify-start">
                  <Link href={`/members/${member.id}/add-points`}>
                    <Plus className="mr-3 h-4 w-4" />
                    <span>Add Points</span>
                  </Link>
                </Button>
              </div>
              <Button asChild variant="outline" className="w-full h-12 mt-3 justify-start">
                <Link href={`/members/${member.id}/deduct-points`}>
                  <Minus className="mr-3 h-4 w-4" />
                  <span>Deduct Points</span>
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Tier Benefits */}
          <TierBenefitsCard
            memberTier={member.loyalty_tier || 'Basic'}
            lifetimePoints={member.lifetime_points || 0}
          />

        </div>

        {/* Right Column - Telegram & Activity */}
        <div className="space-y-6">

          {/* Telegram Integration */}
          <OptimizedTelegramLinkGenerator
            memberId={member.id}
            memberName={member.name}
            memberPhone={member.phone_number}
          />

          {/* Member Purchase Analytics */}
          <MemberPurchaseAnalytics
            memberId={member.id}
            memberName={member.name}
            className="mb-6"
          />

          {/* Recent Transactions */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Recent Transactions</CardTitle>
              <CardDescription>Latest point activity for this member</CardDescription>
            </CardHeader>
            <CardContent>
              {transactionsLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="p-3 border rounded-lg animate-pulse">
                      <div className="flex justify-between mb-2">
                        <div className="h-5 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                      <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : transactions.length === 0 ? (
                <div className="text-center py-12">
                  <BarChart3 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-muted-foreground font-medium">No transactions found</p>
                  <p className="text-sm text-muted-foreground mt-1">This member hasn&apos;t earned or redeemed any points yet.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {transactions.slice(0, 6).map((transaction: TransactionData) => (
                    <div key={transaction.id} className="p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                      {/* Improved responsive layout */}
                      <div className="flex flex-col space-y-2">
                        {/* Top row: Icon, title, and points */}
                        <div className="flex items-center justify-between gap-3">
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            {transaction.transaction_type === 'earn' ? (
                              <div className="p-1.5 bg-green-100 dark:bg-green-900/30 rounded-full flex-shrink-0">
                                <Plus className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
                              </div>
                            ) : transaction.transaction_type === 'redeem' ? (
                              <div className="p-1.5 bg-red-100 dark:bg-red-900/30 rounded-full flex-shrink-0">
                                <Minus className="h-3.5 w-3.5 text-red-600 dark:text-red-400" />
                              </div>
                            ) : (
                              <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-full flex-shrink-0">
                                <Gift className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
                              </div>
                            )}
                            <div className="font-medium text-sm truncate">
                              {transaction.transaction_type === 'earn' ? 'Points Earned' :
                               transaction.transaction_type === 'redeem' ? 'Points Redeemed' :
                               'Points Adjusted'}
                            </div>
                          </div>
                          <div className="flex items-center gap-1 flex-shrink-0">
                            <span className={`font-bold text-sm ${
                              transaction.points_change > 0 ? 'text-green-600 dark:text-green-400' :
                              transaction.points_change < 0 ? 'text-red-600 dark:text-red-400' : 'text-gray-600'
                            }`}>
                              {transaction.points_change > 0 ? '+' : ''}{transaction.points_change}
                            </span>
                            <span className="text-xs text-muted-foreground">pts</span>
                          </div>
                        </div>

                        {/* Bottom row: Description and date */}
                        <div className="flex items-center justify-between gap-3 text-xs">
                          <p className="text-muted-foreground truncate flex-1 min-w-0">
                            {transaction.description}
                          </p>
                          <div className="text-muted-foreground flex-shrink-0">
                            <ClientOnly fallback="Loading...">
                              {() => format(new Date(transaction.created_at), 'MMM d, h:mm a')}
                            </ClientOnly>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {transactions.length > 6 && (
                    <div className="text-center pt-4 border-t">
                      <Button variant="outline" size="sm" className="w-full sm:w-auto">
                        <BarChart3 className="mr-2 h-4 w-4" />
                        View All {transactions.length} Transactions
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

        </div>
      </div>
    </div>
  )
}
