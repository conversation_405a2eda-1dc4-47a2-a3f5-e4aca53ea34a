'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { motion } from 'framer-motion'
import { useTheme } from 'next-themes'
import { MoonIcon, SunIcon, ArrowLeft, CheckCircle, Phone, Mail, MessageCircle } from 'lucide-react'

export default function SignupPage() {
  const { user } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [companyName, setCompanyName] = useState('')
  const [businessType, setBusinessType] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const { theme, setTheme } = useTheme()

  // Check if we're in development mode
  const isDevelopment = process.env.NODE_ENV === 'development'

  // Business type options
  const businessTypes = [
    { value: 'restaurant', label: 'Restaurant' },
    { value: 'salon', label: 'Salon/Beauty' },
    { value: 'retail', label: 'Retail Store' },
    { value: 'service', label: 'Service Business' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'fitness', label: 'Fitness/Gym' },
    { value: 'other', label: 'Other' }
  ]

  useEffect(() => {
    // Only redirect if user is authenticated AND we're not in the success state
    if (user && !success) {
      router.push('/dashboard')
    }
  }, [user, router, success])

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            company_name: companyName,
            business_type: businessType,
          }
        }
      })

      if (error) throw error

      setSuccess(true)
      // Don't redirect automatically - let user see the verification message
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred during signup'
      setError(errorMessage)
      setLoading(false)
    }
  }

  // Production contact page
  if (!isDevelopment) {
    return (
      <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300 relative overflow-hidden">
        {/* Abstract SVG art background */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <svg className="absolute top-0 left-0 w-64 h-64 text-purple-200 dark:text-purple-900/20 opacity-40 transform -translate-x-1/4 -translate-y-1/4" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 20 L80 20 L80 80 L20 80 Z" stroke="currentColor" strokeWidth="2" />
            <path d="M35 35 L65 65" stroke="currentColor" strokeWidth="2" />
            <path d="M35 65 L65 35" stroke="currentColor" strokeWidth="2" />
          </svg>
          <svg className="absolute bottom-0 right-0 w-80 h-80 text-amber-200 dark:text-amber-900/20 opacity-40 transform translate-x-1/4 translate-y-1/4" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="20" y="20" width="60" height="60" rx="4" stroke="currentColor" strokeWidth="2" />
            <path d="M20 50 L80 50" stroke="currentColor" strokeWidth="2" />
            <path d="M50 20 L50 80" stroke="currentColor" strokeWidth="2" />
          </svg>
        </div>

        {/* Theme toggle */}
        <button
          onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
          className="absolute top-4 right-4 p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 z-10"
          aria-label="Toggle dark mode"
        >
          {theme === 'dark' ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
        </button>

        {/* Back button */}
        <Link href="/" className="absolute top-4 left-4 p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 z-10 hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors">
          <ArrowLeft className="h-5 w-5" />
        </Link>

        {/* Center content */}
        <div className="w-full flex flex-col justify-center items-center p-6 md:p-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="w-full max-w-2xl text-center"
          >
            <Card className="border-gray-300 dark:border-gray-700 shadow-xl bg-white dark:bg-gray-800">
              <CardHeader className="text-center pb-8">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="flex justify-center mb-6"
                >
                  <div className="h-20 w-20 bg-gradient-to-r from-purple-100 to-amber-100 dark:from-purple-900/30 dark:to-amber-900/30 rounded-full flex items-center justify-center">
                    <MessageCircle className="h-12 w-12 text-purple-600 dark:text-purple-400" />
                  </div>
                </motion.div>
                <CardTitle className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Ready to Join Loyal?
                </CardTitle>
                <CardDescription className="text-lg text-gray-700 dark:text-gray-300">
                  We&apos;re excited to help you build an amazing loyalty program for your business!
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="text-center">
                  <p className="text-gray-600 dark:text-gray-400 mb-8">
                    To create your business account and get started with Loyal, please contact us directly. 
                    Our team will help you set up your loyalty program and get you running in no time.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-lg border border-purple-200 dark:border-purple-700"
                  >
                    <div className="flex items-center mb-4">
                      <Phone className="h-6 w-6 text-purple-600 dark:text-purple-400 mr-3" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Call Us</h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      Speak directly with our team
                    </p>
                    <a 
                      href="tel:+251962423262"
                      className="text-xl font-bold text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300 transition-colors"
                    >
                      +251 96 242 3262
                    </a>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="bg-gradient-to-r from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 p-6 rounded-lg border border-amber-200 dark:border-amber-700"
                  >
                    <div className="flex items-center mb-4">
                      <Mail className="h-6 w-6 text-amber-600 dark:text-amber-400 mr-3" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Email Us</h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-3">
                      Send us your business details
                    </p>
                    <a 
                      href="mailto:<EMAIL>"
                      className="text-xl font-bold text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 transition-colors"
                    >
                      <EMAIL>
                    </a>
                  </motion.div>
                </div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  className="bg-gray-50 dark:bg-gray-800/50 p-6 rounded-lg border border-gray-200 dark:border-gray-700"
                >
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">What to expect:</h4>
                  <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Quick setup consultation (15-30 minutes)
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Customized loyalty program design
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Full onboarding and training
                    </li>
                    <li className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      Ongoing support and assistance
                    </li>
                  </ul>
                </motion.div>

                <div className="text-center">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Already have an account?{' '}
                    <Link href="/login" className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 font-medium">
                      Sign in here
                    </Link>
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  if (success) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-xl text-center max-w-md w-full"
        >
          <div className="flex justify-center mb-6">
            <div className="h-20 w-20 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <CheckCircle className="h-12 w-12 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Check Your Email!</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            We&apos;ve sent a verification link to <strong>{email}</strong>
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-8">
            Please click the link in your email to verify your account, then return to login.
          </p>
          <Button
            onClick={() => router.push('/login?message=verification-sent')}
            className="w-full bg-gradient-to-r from-purple-600 to-amber-500 hover:from-purple-700 hover:to-amber-600 text-white"
          >
            Go to Login
          </Button>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300 relative overflow-hidden">
      {/* Abstract SVG art background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Top-left geometric shapes */}
        <svg className="absolute top-0 left-0 w-64 h-64 text-purple-200 dark:text-purple-900/20 opacity-40 transform -translate-x-1/4 -translate-y-1/4" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M20 20 L80 20 L80 80 L20 80 Z" stroke="currentColor" strokeWidth="2" />
          <path d="M35 35 L65 65" stroke="currentColor" strokeWidth="2" />
          <path d="M35 65 L65 35" stroke="currentColor" strokeWidth="2" />
          <path d="M50 20 L50 35" stroke="currentColor" strokeWidth="2" />
          <path d="M50 65 L50 80" stroke="currentColor" strokeWidth="2" />
          <path d="M20 50 L35 50" stroke="currentColor" strokeWidth="2" />
          <path d="M65 50 L80 50" stroke="currentColor" strokeWidth="2" />
        </svg>

        {/* Bottom-right geometric shapes */}
        <svg className="absolute bottom-0 right-0 w-80 h-80 text-amber-200 dark:text-amber-900/20 opacity-40 transform translate-x-1/4 translate-y-1/4" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="20" y="20" width="60" height="60" rx="4" stroke="currentColor" strokeWidth="2" />
          <path d="M20 50 L80 50" stroke="currentColor" strokeWidth="2" />
          <path d="M50 20 L50 80" stroke="currentColor" strokeWidth="2" />
        </svg>

        {/* Middle decorative elements */}
        <svg className="absolute top-1/3 right-1/4 w-32 h-32 text-gray-300 dark:text-gray-700 opacity-30" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M25 25 L75 25 L75 75 L25 75 Z" stroke="currentColor" strokeWidth="2" />
          <path d="M40 40 L60 60" stroke="currentColor" strokeWidth="2" />
          <path d="M40 60 L60 40" stroke="currentColor" strokeWidth="2" />
        </svg>
      </div>

      {/* Theme toggle */}
      <button
        onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        className="absolute top-4 right-4 p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 z-10"
        aria-label="Toggle dark mode"
      >
        {theme === 'dark' ? <SunIcon className="h-5 w-5" /> : <MoonIcon className="h-5 w-5" />}
      </button>

      {/* Back button */}
      <Link href="/" className="absolute top-4 left-4 p-2 rounded-full bg-gray-200 dark:bg-gray-800 text-gray-800 dark:text-gray-200 z-10 hover:bg-gray-300 dark:hover:bg-gray-700 transition-colors">
        <ArrowLeft className="h-5 w-5" />
      </Link>

      {/* Left side - signup form */}
      <div className="w-full lg:w-1/2 flex flex-col justify-center items-center p-6 md:p-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="border-gray-300 dark:border-gray-700 shadow-xl bg-white dark:bg-gray-800">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">Create your account</CardTitle>
              <CardDescription className="text-gray-700 dark:text-gray-300">
                Sign up to start managing your loyalty program
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSignup} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="company" className="text-gray-900 dark:text-gray-100">Company Name</Label>
                  <Input
                    id="company"
                    type="text"
                    placeholder="Your Company"
                    value={companyName}
                    onChange={(e) => setCompanyName(e.target.value)}
                    required
                    className="border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessType" className="text-gray-900 dark:text-gray-100">Business Type</Label>
                  <select
                    id="businessType"
                    value={businessType}
                    onChange={(e) => setBusinessType(e.target.value)}
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="">Select your business type</option>
                    {businessTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-900 dark:text-gray-100">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-gray-900 dark:text-gray-100">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  />
                </div>

                {error && (
                  <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-md text-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-purple-600 to-amber-500 hover:from-purple-700 hover:to-amber-600 text-white"
                  disabled={loading}
                >
                  {loading ? 'Creating account...' : 'Sign up'}
                </Button>
              </form>
            </CardContent>
            <div className="px-6 pb-6 text-center">
              <p className="text-sm text-gray-700 dark:text-gray-300">
                Already have an account?{' '}
                <Link href="/login" className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 font-medium">
                  Sign in
                </Link>
              </p>
            </div>
          </Card>
        </motion.div>
      </div>

      {/* Right side - decorative */}
      <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-amber-500 to-purple-600 dark:from-amber-700 dark:to-purple-900 relative overflow-hidden">
        <div className="absolute inset-0 bg-pattern opacity-10"
          style={{
            backgroundImage: 'linear-gradient(to right, white 1px, transparent 1px), linear-gradient(to bottom, white 1px, transparent 1px)',
            backgroundSize: '20px 20px'
          }}>
        </div>

        {/* Decorative SVG elements */}
        <svg className="absolute top-1/4 right-1/4 w-32 h-32 text-white opacity-20" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M25 25 L75 25 L75 75 L25 75 Z" stroke="currentColor" strokeWidth="2" />
          <path d="M40 40 L60 60" stroke="currentColor" strokeWidth="2" />
          <path d="M40 60 L60 40" stroke="currentColor" strokeWidth="2" />
        </svg>

        <svg className="absolute bottom-1/4 left-1/4 w-40 h-40 text-white opacity-20" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="30" y="30" width="40" height="40" rx="8" stroke="currentColor" strokeWidth="2" />
          <circle cx="50" cy="50" r="12" stroke="currentColor" strokeWidth="2" />
        </svg>

        <div className="relative z-10 flex flex-col justify-center items-center w-full p-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-5xl md:text-6xl font-bold mb-6 text-white">Loyal</h1>
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-white/90 text-xl text-center max-w-md mb-12"
          >
            Join thousands of businesses elevating their customer loyalty experience
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-6 w-full max-w-md"
          >
            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="flex items-start">
                <div className="h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                  <span className="text-white text-lg font-medium">1</span>
                </div>
                <div>
                  <div className="text-white mb-2 text-sm font-medium">Create Your Account</div>
                  <p className="text-white/80 text-sm">Sign up in minutes and start building your loyalty program</p>
                </div>
              </div>
            </div>

            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="flex items-start">
                <div className="h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                  <span className="text-white text-lg font-medium">2</span>
                </div>
                <div>
                  <div className="text-white mb-2 text-sm font-medium">Customize Your Program</div>
                  <p className="text-white/80 text-sm">Design rewards, tiers, and points systems that match your brand</p>
                </div>
              </div>
            </div>

            <div className="p-6 bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg">
              <div className="flex items-start">
                <div className="h-10 w-10 rounded-full bg-white/20 flex items-center justify-center mr-4">
                  <span className="text-white text-lg font-medium">3</span>
                </div>
                <div>
                  <div className="text-white mb-2 text-sm font-medium">Launch and Grow</div>
                  <p className="text-white/80 text-sm">Engage your customers and watch your business grow</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}
