import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { TierDefinition, CreateTierInput, UpdateTierInput } from "../types";
import { useCompany } from "@/contexts/company-context";
import { CACHE_TIMES, COMMON_QUERY_OPTIONS } from "@/lib/query-config";
import { queryKeys } from '@/lib/query-config';

// Fetch all tiers for the current company
export function useTiers() {
  const { company } = useCompany();
  const companyId = company?.id;

  return useQuery<TierDefinition[], Error>({
    queryKey: queryKeys.tiers(companyId || ''),
    queryFn: async () => {
      if (!companyId) {
        return [];
      }

      const response = await fetch(`/api/tiers/list?companyId=${companyId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tiers");
      }

      const result = await response.json();

      // Return data from API (which now includes member_count)
      return (result.data || []) as TierDefinition[];
    },
    enabled: !!companyId,
    staleTime: CACHE_TIMES.STATIC,
    gcTime: CACHE_TIMES.STATIC * 2,
    ...COMMON_QUERY_OPTIONS,
  });
}

// Fetch a single tier by ID
export function useTier(id: string) {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useQuery<TierDefinition, Error>({
    queryKey: queryKeys.tiers(companyId || '').concat(['single', id]),
    queryFn: async () => {
      if (!companyId || !id) {
        throw new Error("Company ID and tier ID are required");
      }

      // First try to get the tier from the cache
      const cachedTiers = queryClient.getQueryData<TierDefinition[]>(queryKeys.tiers(companyId));
      if (cachedTiers) {
        const cachedTier = cachedTiers.find(tier => tier.id === id);
        if (cachedTier) return cachedTier;
      }

      // If not in cache, fetch from API
      const response = await fetch(`/api/tiers/${id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tier");
      }

      const data = await response.json();
      return {
        ...data,
        member_count: 0 // Default value since we don't have the relationship
      } as TierDefinition;
    },
    enabled: !!companyId && !!id,
    staleTime: CACHE_TIMES.STATIC,
    gcTime: CACHE_TIMES.STATIC * 2,
    ...COMMON_QUERY_OPTIONS,
  });
}

// Create a new tier
export function useCreateTier() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newTier: CreateTierInput) => {
      console.log("=== TIER CREATION MUTATION STARTED ===");
      console.log("Company:", company);
      console.log("CompanyId:", companyId);
      console.log("NewTier input:", newTier);

      if (!companyId) {
        console.error("ERROR: Company ID is missing!");
        throw new Error("Company ID is required");
      }

      const requestBody = {
        ...newTier,
        companyId: companyId,
      };

      console.log("Frontend sending tier data:", JSON.stringify(requestBody, null, 2));

      const response = await fetch("/api/tiers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(requestBody),
      });

      console.log("Response status:", response.status);
      console.log("Response ok:", response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error response:", errorText);
        throw new Error("Failed to create tier");
      }

      return response.json() as Promise<TierDefinition>;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tiers(companyId || '') });
    }
  });
}

// Update an existing tier
export function useUpdateTier() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updatedTier: UpdateTierInput) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const { id, ...tierData } = updatedTier;
      const response = await fetch(`/api/tiers/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          ...tierData,
          companyId: companyId,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update tier");
      }

      return response.json() as Promise<TierDefinition>;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tiers(companyId || '') });
      queryClient.invalidateQueries({ queryKey: queryKeys.tiers(companyId || '').concat(['single', variables.id]) });
    }
  });
}

// Delete a tier
export function useDeleteTier() {
  const { company } = useCompany();
  const companyId = company?.id;
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      if (!companyId) {
        throw new Error("Company ID is required");
      }

      const response = await fetch(`/api/tiers/${id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to delete tier");
      }

      return id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tiers(companyId || '') });
    }
  });
}
