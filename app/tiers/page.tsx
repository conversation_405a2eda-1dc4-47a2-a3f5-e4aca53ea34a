'use client'

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useRequireAuth } from '@/hooks/use-auth';
import { useUserRole } from '@/hooks/use-user-role';
import { TiersTable } from "./components/tiers-table";
import { PageHeader } from "@/components/page-header";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@radix-ui/react-icons";
import Link from "next/link";
import { toast } from 'sonner';

export default function TiersPage() {
  const { isLoading } = useRequireAuth();
  const userRole = useUserRole();
  const router = useRouter();

  // Redirect cashiers away from tiers page
  useEffect(() => {
    if (!isLoading && userRole.isCashier) {
      toast.error('Access denied. Cashiers cannot manage tiers.')
      router.push('/dashboard')
      return
    }
  }, [isLoading, userRole.isCashier, router])

  if (isLoading || userRole.isCashier) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
        <div className="animate-shimmer w-48 h-12 rounded-lg"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <PageHeader
          heading="Tier Management"
          subheading="Define and manage loyalty program tiers and benefits"
        />
        <Link href="/tiers/new">
          <Button>
            <PlusIcon className="mr-2 h-4 w-4" />
            New Tier
          </Button>
        </Link>
      </div>
      <TiersTable />
    </div>
  );
}
