'use client'

import { Suspense, useState } from 'react'

import { useSearchParams } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'
import Image from 'next/image'
import { formatToEastAfricanTime } from '@/lib/date-utils'
import type { EnhancedReceiptData } from '@/lib/receipt-ocr-enhanced'
import {
  CheckCircle,
  ArrowLeft,
  User,
  Receipt,
  TrendingUp,
  Gift,
  CreditCard,
  Trophy,
  Sparkles,
  ArrowRight,
  Building2,
  Eye,
  Trash2
} from 'lucide-react'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogClose } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { ReceiptSummary } from '@/components/ui/receipt-summary'
import { useDeleteTransaction } from '@/hooks/use-transactions'
import { useRole } from '@/hooks/use-role'

interface TransactionSummaryData {
  transactions: Array<{
    id: string
    type: 'EARN' | 'REDEEM' | 'DOUBLE_POINTS'
    points_change: number
    description: string
    transaction_date: string
    created_at: string
    total_amount?: number
    business_name?: string
    receipt_number?: string
    receipt_image_url?: string
    receipt_ocr_data?: EnhancedReceiptData
    receipt_ocr_confidence?: number
    amount_due?: number
    reward?: {
      id: string
      title: string
      description?: string
      points_required: number
      reward_value: number
      reward_value_type: string
      applied_value?: number
    }
  }>
  member: {
    id: string
    name: string
    email?: string
    phone?: string
    profile_image_url?: string
    loyalty_id: string
    available_points: number
    lifetime_points: number
    current_tier?: {
      name: string
      color: string
    }
  }
  summary: {
    total_transactions: number
    points_earned: number
    total_points_used: number
    rewards_applied: number
    net_points_change: number
  }
  company: {
    name: string
    business_type?: string
  }
}

function TransactionSuccessContent() {
  const searchParams = useSearchParams()
  const transactionId = searchParams.get('id')
  const [isReceiptDialogOpen, setIsReceiptDialogOpen] = useState(false)

  // Role and delete transaction hooks
  const { isOwner } = useRole()
  const deleteTransactionMutation = useDeleteTransaction()

  // Fetch transaction summary data
  const { data: summaryData, isLoading, error } = useQuery({
    queryKey: ['transaction-summary', transactionId],
    queryFn: async (): Promise<TransactionSummaryData> => {
      if (!transactionId) throw new Error('Transaction ID is required')

      const response = await fetch(`/api/transactions/summary/${transactionId}`, {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      })
      if (!response.ok) {
        throw new Error('Failed to fetch transaction summary')
      }
      return response.json()
    },
    enabled: !!transactionId,
    staleTime: 0, // Always fresh for success page
  })

  if (!transactionId) {
    return (
      <div className="container mx-auto py-12 text-center">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-destructive mb-4">Invalid Transaction</h1>
          <p className="text-muted-foreground mb-6">No transaction ID provided.</p>
          <Button asChild>
            <Link href="/transactions">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Transactions
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-12">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-32 bg-muted rounded"></div>
            <div className="h-24 bg-muted rounded"></div>
            <div className="h-24 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error || !summaryData) {
    return (
      <div className="container mx-auto py-12 text-center">
        <div className="max-w-md mx-auto">
          <h1 className="text-2xl font-bold text-destructive mb-4">Error Loading Transaction</h1>
          <p className="text-muted-foreground mb-6">
            {error instanceof Error ? error.message : 'Unable to load transaction details.'}
          </p>
          <Button asChild>
            <Link href="/transactions">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Transactions
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  const { transactions, member, summary } = summaryData
  const primaryTransaction = transactions[0]

  // For unified transactions, find the amount due from any transaction that has it
  const transactionWithAmountDue = transactions.find(t => t.amount_due !== null)
  const finalAmountDue = transactionWithAmountDue?.amount_due

  // For discount display, only use non-double-points rewards
  const discountRewardTransaction = transactions.find(t =>
    t.reward !== null && t.reward?.reward_value_type !== 'DOUBLE_POINTS'
  )
  const discountRewardInfo = discountRewardTransaction?.reward

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Success Header */}
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="relative">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-pulse">
              <Sparkles className="h-3 w-3 text-white" />
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-green-700">Transaction Successful!</h1>
          <p className="text-lg text-muted-foreground">
            Your transaction has been processed successfully
          </p>
        </div>
      </div>

      <div className="max-w-4xl mx-auto grid gap-6 lg:grid-cols-3">
        {/* Main Transaction Summary */}
        <div className="lg:col-span-2 space-y-6">
          {/* Transaction Details Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Transaction Details
              </CardTitle>
              <CardDescription>
                Processed on {formatToEastAfricanTime(primaryTransaction.created_at)}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Primary Transaction */}
              <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    primaryTransaction.type === 'EARN'
                      ? 'bg-green-100 text-green-600'
                      : 'bg-blue-100 text-blue-600'
                  }`}>
                    {primaryTransaction.type === 'EARN' ? (
                      <TrendingUp className="h-5 w-5" />
                    ) : (
                      <Gift className="h-5 w-5" />
                    )}
                  </div>
                  <div>
                    <div className="font-medium">
                      {primaryTransaction.type === 'EARN' ? 'Points Earned' : 'Reward Redeemed'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {primaryTransaction.description}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-bold ${
                    primaryTransaction.type === 'EARN' ? 'text-green-600' : 'text-blue-600'
                  }`}>
                    {primaryTransaction.type === 'EARN' ? '+' : '-'}{Math.abs(primaryTransaction.points_change)} pts
                  </div>
                  {primaryTransaction.total_amount && (
                    <div className="text-sm text-muted-foreground">
                      ETB {primaryTransaction.total_amount.toLocaleString()}
                    </div>
                  )}
                </div>
              </div>

              {/* Additional transactions (rewards) */}
              {transactions.length > 1 && (
                <div className="space-y-2">
                  <Separator />
                  <div className="text-sm font-medium text-muted-foreground">Rewards Applied:</div>
                  {transactions.slice(1).map((transaction, index) => (
                    <div key={index} className={`flex items-center justify-between p-3 rounded-lg ${
                      transaction.type === 'DOUBLE_POINTS'
                        ? 'bg-purple-50 border border-purple-200'
                        : 'bg-blue-50 border border-blue-200'
                    }`}>
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          transaction.type === 'DOUBLE_POINTS'
                            ? 'bg-purple-100 text-purple-600'
                            : 'bg-blue-100 text-blue-600'
                        }`}>
                          {transaction.type === 'DOUBLE_POINTS' ? (
                            <Sparkles className="h-4 w-4" />
                          ) : (
                            <Trophy className="h-4 w-4" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-sm">
                            {transaction.reward?.title || 'Reward Applied'}
                          </div>
                          {transaction.reward?.description && (
                            <div className="text-xs text-muted-foreground">
                              {transaction.reward.description}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        {transaction.type === 'DOUBLE_POINTS' ? (
                          <div className="text-sm font-bold text-purple-600">
                            +{transaction.points_change} pts
                          </div>
                        ) : (
                          <div className="text-sm font-bold text-blue-600">
                            -{Math.abs(transaction.points_change)} pts
                          </div>
                        )}
                        {transaction.reward && (
                          <div className="text-xs text-muted-foreground">
                            {transaction.type === 'DOUBLE_POINTS' ? (
                              `${transaction.reward.reward_value}x multiplier applied`
                            ) : transaction.reward.reward_value_type === 'PERCENTAGE' ? (
                              `${transaction.reward.reward_value}% off`
                            ) : (
                              `ETB ${transaction.reward.reward_value} value`
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Receipt Details */}
              {(primaryTransaction.business_name || primaryTransaction.total_amount || primaryTransaction.receipt_number || finalAmountDue) && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <h4 className="font-medium text-muted-foreground flex items-center gap-2">
                      <Receipt className="h-4 w-4" />
                      Receipt Details
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      {primaryTransaction.business_name && (
                        <div>
                          <div className="font-medium text-muted-foreground">Business</div>
                          <div className="flex items-center gap-2">
                            <Building2 className="h-4 w-4 text-muted-foreground" />
                            {primaryTransaction.business_name}
                          </div>
                        </div>
                      )}
                      {primaryTransaction.receipt_number && (
                        <div>
                          <div className="font-medium text-muted-foreground">Receipt #</div>
                          <div className="flex items-center gap-2">
                            <Receipt className="h-4 w-4 text-muted-foreground" />
                            {primaryTransaction.receipt_number}
                          </div>
                        </div>
                      )}

                      {primaryTransaction.receipt_image_url && (
                        <div className="col-span-2 mt-2">
                          <Dialog open={isReceiptDialogOpen} onOpenChange={setIsReceiptDialogOpen}>
                            <DialogTrigger asChild>
                              <Button
                                variant="outline"
                                className="flex items-center gap-2 text-blue-600 border-blue-200 hover:bg-blue-50"
                              >
                                <Eye className="h-4 w-4" />
                                View Receipt Image
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="sm:max-w-md">
                              <DialogHeader>
                                <DialogTitle>Receipt Image</DialogTitle>
                              </DialogHeader>
                              <div className="relative w-full h-[500px] overflow-hidden rounded-md">
                                <Image
                                  src={primaryTransaction.receipt_image_url}
                                  alt="Receipt"
                                  fill
                                  className="object-contain"
                                />
                              </div>
                              <div className="flex justify-end">
                                <DialogClose asChild>
                                  <Button variant="outline">Close</Button>
                                </DialogClose>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      )}
                      {primaryTransaction.total_amount && (
                        <div>
                          <div className="font-medium text-muted-foreground">Original Amount</div>
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                            ETB {primaryTransaction.total_amount.toLocaleString()}
                          </div>
                        </div>
                      )}
                      {finalAmountDue && (
                        <div>
                          <div className="font-medium text-muted-foreground">Amount Due</div>
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-green-600" />
                            <span className="font-semibold text-green-600">
                              ETB {finalAmountDue.toLocaleString()}
                            </span>
                          </div>
                        </div>
                      )}
                      {discountRewardInfo?.applied_value && primaryTransaction.total_amount && (
                        <div className="col-span-2">
                          <div className="font-medium text-muted-foreground">Discount Applied</div>
                          <div className="flex items-center gap-2">
                            <Gift className="h-4 w-4 text-purple-600" />
                            <span className="font-semibold text-purple-600">
                              -{discountRewardInfo.reward_value_type === 'PERCENTAGE'
                                ? `${discountRewardInfo.applied_value}% (ETB ${((primaryTransaction.total_amount * discountRewardInfo.applied_value) / 100).toLocaleString()})`
                                : `ETB ${discountRewardInfo.applied_value.toLocaleString()}`
                              }
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Enhanced Receipt Summary */}
          {primaryTransaction.receipt_ocr_data && (() => {
            try {
              const parsedReceiptData = typeof primaryTransaction.receipt_ocr_data === 'string'
                ? JSON.parse(primaryTransaction.receipt_ocr_data)
                : primaryTransaction.receipt_ocr_data;

              return (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Receipt className="h-5 w-5" />
                      Receipt Details
                    </CardTitle>
                    <CardDescription>
                      AI-extracted receipt information
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ReceiptSummary
                      receiptData={parsedReceiptData}
                      confidence={primaryTransaction.receipt_ocr_confidence}
                      showItems={true}
                    />
                  </CardContent>
                </Card>
              );
            } catch (error) {
              console.error('Failed to parse receipt OCR data:', error);
              return null;
            }
          })()}

          {/* Summary Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Transaction Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    +{summary.points_earned}
                  </div>
                  <div className="text-sm text-muted-foreground">Points Earned</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    -{summary.total_points_used}
                  </div>
                  <div className="text-sm text-muted-foreground">Points Used</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {summary.rewards_applied}
                  </div>
                  <div className="text-sm text-muted-foreground">Rewards Applied</div>
                </div>
                <div className="text-center">
                  <div className={`text-2xl font-bold ${
                    summary.net_points_change >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {summary.net_points_change >= 0 ? '+' : ''}{summary.net_points_change}
                  </div>
                  <div className="text-sm text-muted-foreground">Net Change</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Member Info Sidebar */}
        <div className="space-y-6">
          {/* Member Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Member Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Member Avatar & Info */}
              <div className="flex items-center gap-3">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={member.profile_image_url} alt={member.name} />
                  <AvatarFallback>
                    {member.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="font-medium">{member.name}</div>
                  <div className="text-sm text-muted-foreground">
                    ID: {member.loyalty_id}
                  </div>
                  {member.current_tier && (
                    <Badge
                      variant="secondary"
                      className="mt-1 text-xs"
                      style={{ backgroundColor: `${member.current_tier.color}20`, color: member.current_tier.color }}
                    >
                      {member.current_tier.name}
                    </Badge>
                  )}
                </div>
              </div>

              <Separator />

              {/* Updated Points Balance */}
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Available Points</span>
                  <span className="font-bold text-green-600">
                    {member.available_points.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Lifetime Points</span>
                  <span className="font-bold text-blue-600">
                    {member.lifetime_points.toLocaleString()}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>What&apos;s Next?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/transactions/unified">
                  <Receipt className="h-4 w-4 mr-2" />
                  Process Another Transaction
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href={`/members/${member.id}`}>
                  <User className="h-4 w-4 mr-2" />
                  View Member Profile
                </Link>
              </Button>
              <Button asChild variant="default" className="w-full justify-start">
                <Link href="/transactions">
                  <ArrowRight className="h-4 w-4 mr-2" />
                  View All Transactions
                </Link>
              </Button>

              {/* Owner Delete Button */}
              {isOwner && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Transaction
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Transaction</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this transaction? This action cannot be undone and will:
                        <ul className="list-disc list-inside mt-2 space-y-1">
                          <li>Remove {Math.abs(primaryTransaction.points_change)} points from {member.name}</li>
                          <li>Delete any associated reward redemptions</li>
                          <li>Update the member&apos;s point balance</li>
                        </ul>
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => {
                          if (transactionId) {
                            deleteTransactionMutation.mutate(transactionId, {
                              onSuccess: () => {
                                // Redirect to transactions page after successful deletion
                                window.location.href = '/transactions'
                              }
                            })
                          }
                        }}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Delete Transaction
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Footer Actions */}
      <div className="text-center pt-6">
        <Button asChild variant="ghost">
          <Link href="/dashboard">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Link>
        </Button>
      </div>
    </div>
  )
}

export default function TransactionSuccessPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto py-12">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="h-32 bg-muted rounded"></div>
            <div className="h-24 bg-muted rounded"></div>
            <div className="h-24 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    }>
      <TransactionSuccessContent />
    </Suspense>
  )
}
