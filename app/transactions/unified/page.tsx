'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useRequireAuth } from '@/hooks/use-auth'
import { useCompanyAdminQuery } from '@/hooks/use-company-admin-query'
import { useCompany } from '@/contexts/company-context'
import { useMembers } from '@/hooks/use-members'
import { useRewards } from '@/hooks/use-rewards'
import { useCreateUnifiedTransaction, type DuplicateReceiptError, type RewardAlreadyRedeemedError } from '@/hooks/use-unified-transactions'
import { useMemberRedemptions } from '@/hooks/use-member-redemptions'
import { useProcessReceiptOCR } from '@/hooks/use-receipts'
import { uploadImage } from '@/lib/file-upload'
import { z } from 'zod'
import { useForm, useWatch } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { toast } from 'sonner'
import type { ReceiptData } from '@/lib/receipt-ocr'
import type { EnhancedReceiptData } from '@/lib/receipt-ocr-enhanced'

// UI Components
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { MemberCombobox } from '@/components/ui/member-combobox'
import { ReceiptSummary } from '@/components/ui/receipt-summary'
import { Controller } from 'react-hook-form'
import Link from 'next/link'
import { ArrowLeft, X, CheckCircle, Loader2, Camera, FileText, User, Calculator, Wand2, Sparkles, Trophy, CreditCard, AlertTriangle, Settings, ToggleLeft, ToggleRight } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

// Enhanced schema for unified transaction processing
const unifiedTransactionSchema = z.object({
  member_id: z.string().uuid({ message: 'Please select a member' }),
  transaction_type: z.enum(['EARN', 'REDEEM']).optional(), // Made optional since it's determined automatically
  points_change: z.coerce.number().optional(),
  description: z.string().optional(),
  transaction_date: z.string(),
  receipt_image: z.instanceof(File).optional(),
  receipt_image_url: z.string().url().optional().or(z.literal('')), // Allow empty string
  // Receipt/spending data - either total_amount OR subtotal must be provided
  total_amount: z.coerce.number().positive().optional(),
  subtotal: z.coerce.number().positive().optional(),
  tax_amount: z.coerce.number().optional(),
  business_name: z.string().optional(),
  financial_system_number: z.string().optional(),
  // Reward selection (for redemption)
  reward_id: z.string().optional(),
  // AI-generated fields
  ai_suggested_points: z.coerce.number().optional(),
  ai_suggested_rewards: z.array(z.string()).optional(),
}).refine((data) => {
  // Ensure either total_amount or subtotal is provided
  return data.total_amount && data.total_amount > 0 || data.subtotal && data.subtotal > 0
}, {
  message: 'Please enter either total amount or subtotal',
  path: ['total_amount']
})

type UnifiedTransactionFormValues = z.infer<typeof unifiedTransactionSchema>

export default function UnifiedTransactionPage() {
  const { user, isLoading: authLoading } = useRequireAuth()
  const { company, isLoading: companyLoading } = useCompany()
  const { isAdmin, isLoading: adminLoading } = useCompanyAdminQuery()
  const router = useRouter()

  // States
  const [isManualMode, setIsManualMode] = useState(false)
  const [receiptFile, setReceiptFile] = useState<File | null>(null)
  const [ocrData, setOcrData] = useState<ReceiptData | null>(null)
  const [enhancedOcrData, setEnhancedOcrData] = useState<EnhancedReceiptData | null>(null)
  const [showOcrResults, setShowOcrResults] = useState(false)
  const [isProcessingOCR, setIsProcessingOCR] = useState(false)
  const [processingStage, setProcessingStage] = useState<'upload' | 'ocr' | 'rewards' | 'complete'>('upload')
  const [submissionError, setSubmissionError] = useState<string | null>(null)
  const [recommendedRewards, setRecommendedRewards] = useState<Array<{
    id: string
    title: string
    description?: string
    points_required: number
    reward_value_type: string
    reward_value: number
    is_active: boolean
  }>>([])
  const [selectedRewards, setSelectedRewards] = useState<string[]>([])
  const [duplicateReceiptDialog, setDuplicateReceiptDialog] = useState<{
    isOpen: boolean
    message?: string
    fsNumber?: string
  }>({ isOpen: false })
  const [rewardAlreadyRedeemedDialog, setRewardAlreadyRedeemedDialog] = useState<{
    isOpen: boolean
    message?: string
    rewardTitle?: string
  }>({ isOpen: false })

  // Initialize form with default values
  const form = useForm<UnifiedTransactionFormValues>({
    resolver: zodResolver(unifiedTransactionSchema),
    defaultValues: {
      member_id: '',
      total_amount: undefined, // Changed from 0 to undefined to avoid button being disabled
      subtotal: undefined, // Changed from 0 to undefined to avoid button being disabled
      tax_amount: 0,
      business_name: company?.name || '',
      financial_system_number: '',
      transaction_date: new Date().toISOString().split('T')[0],
      description: '',
      points_change: 0,
      ai_suggested_points: 0,
      reward_id: '',
      receipt_image_url: ''
    }
  })

  // Auto-fill business name when company data loads
  useEffect(() => {
    if (company?.name && !form.getValues('business_name')) {
      form.setValue('business_name', company.name)
    }
  }, [company?.name, form])

  // Hooks
  const { data: membersData } = useMembers()
  const { data: rewardsData } = useRewards()
  const createUnifiedTransactionMutation = useCreateUnifiedTransaction()
  const processOCRMutation = useProcessReceiptOCR()

  // Get member redemptions to filter out already redeemed rewards
  const selectedMemberId = form.watch('member_id')
  const { data: memberRedemptionsData } = useMemberRedemptions(selectedMemberId)

  // Get selected member's available points
  const selectedMember = membersData?.data?.find((m: { id: string; available_points?: number }) => m.id === selectedMemberId)
  const memberAvailablePoints = selectedMember?.available_points || 0

  // Watch form values for AI processing
  const watchedValues = useWatch({
    control: form.control,
    name: ['member_id', 'total_amount', 'business_name']
  })

  // Watch subtotal for auto-calculation of tax and total
  const subtotal = useWatch({
    control: form.control,
    name: 'subtotal'
  })

  // Auto-calculate tax and total when subtotal changes
  useEffect(() => {
    if (subtotal && subtotal > 0) {
      // Ensure subtotal is treated as a number
      const subtotalNum = Number(subtotal)
      const taxAmount = Math.round(subtotalNum * 0.15 * 100) / 100 // 15% tax, rounded to 2 decimal places
      const totalAmount = Math.round((subtotalNum + taxAmount) * 100) / 100 // Total, rounded to 2 decimal places

      form.setValue('tax_amount', taxAmount)
      form.setValue('total_amount', totalAmount)

      console.log('🧮 [AUTO-CALC] Subtotal changed:', subtotal, 'type:', typeof subtotal)
      console.log('🧮 [AUTO-CALC] Subtotal as number:', subtotalNum)
      console.log('🧮 [AUTO-CALC] Calculated tax (15%):', taxAmount)
      console.log('🧮 [AUTO-CALC] Calculated total:', totalAmount)
    }
  }, [subtotal, form])

  // Clear submission errors when form values change
  useEffect(() => {
    if (submissionError) {
      setSubmissionError(null)
    }
  }, [watchedValues, submissionError])

  // Remove unused AI validation for now (can be added back later)
  // const {
  //   data: aiValidation,
  //   isLoading: isValidating,
  //   mutate: validateWithAI
  // } = useAIValidation()

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/login')
    }
  }, [authLoading, user, router])

  // Process receipt and get AI recommendations
  const handleReceiptUpload = async (file: File) => {
    if (!file) return

    setReceiptFile(file)
    setIsProcessingOCR(true)
    setProcessingStage('ocr')

    try {
      // Step 1: Upload image to storage
      toast.info('📤 Uploading receipt image...')
      const uploadResult = await uploadImage(file)
      const receiptImageUrl = uploadResult.url

      // Step 2: Process OCR
      toast.info('📸 Processing receipt with AI...')
      const ocrResult = await processOCRMutation.mutateAsync({
        file,
        companyId: company?.id
      })
      const extractedData = ocrResult.data.raw_ocr_data
      const enhancedData = ocrResult.data.enhanced_ocr_data

      setOcrData(extractedData)
      setEnhancedOcrData(enhancedData || null)
      setShowOcrResults(true)

      // Store receipt image URL for form submission
      form.setValue('receipt_image_url', receiptImageUrl)

      // Auto-populate form fields from OCR data
      if (extractedData.total_amount) {
        form.setValue('total_amount', extractedData.total_amount)
      }
      if (enhancedData?.subtotal) {
        form.setValue('subtotal', enhancedData.subtotal)
      }
      if (enhancedData?.tax_amount) {
        form.setValue('tax_amount', enhancedData.tax_amount)
      }
      if (extractedData.business_name) {
        form.setValue('business_name', extractedData.business_name)
      }
      if (extractedData.financial_system_number) {
        form.setValue('financial_system_number', extractedData.financial_system_number)
      }

      const itemCount = enhancedData?.items?.length || 0
      toast.success(`✅ Receipt processed! (${Math.round(extractedData.confidence * 100)}% confidence)${itemCount > 0 ? ` • ${itemCount} items detected` : ''}`)

      // Step 2: Calculate points earned using subtotal (before tax) if available
      if (company?.points_earning_ratio) {
        const amountForPoints = enhancedData?.subtotal || extractedData.total_amount
        if (amountForPoints) {
          const pointsEarned = Math.floor(amountForPoints * company.points_earning_ratio)
          form.setValue('ai_suggested_points', pointsEarned)
          form.setValue('points_change', pointsEarned)
          form.setValue('description', `Purchase at ${extractedData.business_name || 'Unknown Business'} - ${pointsEarned} points earned`)
        }
      }

      setProcessingStage('rewards')

    } catch (error) {
      console.error('Receipt processing error:', error)
      toast.error('Failed to process receipt. Please fill in manually.')
    } finally {
      setIsProcessingOCR(false)
    }
  }

  // Get AI reward recommendations when member is selected (and optionally amount is available)
  useEffect(() => {
    const [memberId, totalAmount] = watchedValues

    console.log('🎁 [REWARD EFFECT] Reward filtering effect triggered:', {
      memberId,
      totalAmount,
      isManualMode,
      hasOcrData: !!ocrData,
      hasRewardsData: !!rewardsData?.data
    })

    // Show rewards immediately when member is selected, regardless of amount or receipt
    // This allows admins to see what rewards a member qualifies for before processing any transaction
    const shouldShowRewards = memberId && rewardsData?.data

    console.log('🎁 [REWARD EFFECT] Should show rewards:', shouldShowRewards)

    if (shouldShowRewards) {
      setProcessingStage('rewards')

      // Only show processing toast for meaningful interactions
      if (totalAmount && totalAmount > 0 && !isManualMode) {
        toast.info('🤖 Finding applicable rewards...')
      }

      // Get already redeemed reward IDs for this member
      const redeemedRewardIds = memberRedemptionsData?.data?.redeemed_reward_ids || []

      console.log('[Reward Filtering] Starting reward filtering process...')
      console.log('[Reward Filtering] Total rewards available:', rewardsData.data.length)
      console.log('[Reward Filtering] Redeemed reward IDs for member:', redeemedRewardIds)

      // Filter rewards the member can potentially use
      const applicableRewards = rewardsData.data.filter((reward: {
        id: string
        title: string
        is_active: boolean
        expiration_date?: string
        reward_value_type?: string
        is_expired?: boolean
        points_required: number
      }) => {
        // Basic eligibility check (this would be enhanced with AI)
        const isActive = reward.is_active && !reward.is_expired &&
          (!reward.expiration_date || new Date(reward.expiration_date) > new Date())

        // Exclude rewards that have already been redeemed by this member
        const notAlreadyRedeemed = !redeemedRewardIds.includes(reward.id)

        // Check if member has enough points for the reward
        const hasEnoughPoints = memberAvailablePoints >= reward.points_required

        const isApplicable = isActive && notAlreadyRedeemed && hasEnoughPoints

        console.log(`[Reward Filtering] Reward "${reward.title}": active=${reward.is_active}, expired=${reward.is_expired}, notRedeemed=${notAlreadyRedeemed}, enoughPoints=${hasEnoughPoints}, applicable=${isApplicable}`)

        return isApplicable
      })

      console.log('[Reward Filtering] Applicable rewards found:', applicableRewards.length)

      setRecommendedRewards(applicableRewards.slice(0, 3)) // Top 3 recommendations
      setProcessingStage('complete')

      if (applicableRewards.length > 0 && totalAmount && totalAmount > 0 && !isManualMode) {
        toast.success(`🎁 Found ${applicableRewards.length} applicable rewards!`)
      } else if (applicableRewards.length > 0) {
        // Silent success for member selection without transaction amount
        console.log(`🎁 Found ${applicableRewards.length} applicable rewards for selected member`)
      } else if (totalAmount && totalAmount > 0 && !isManualMode) {
        toast.info('ℹ️ No applicable rewards found for this transaction.')
      }
    }
  }, [watchedValues, rewardsData?.data, ocrData, isManualMode, memberRedemptionsData, memberAvailablePoints])

  // Handle reward selection/deselection - only one reward can be selected at a time
  const toggleRewardSelection = (rewardId: string) => {
    setSelectedRewards(prev =>
      prev.includes(rewardId)
        ? [] // Deselect current reward
        : [rewardId] // Select only this reward, replacing any previously selected reward
    )
  }

  // Submit unified transaction
  const onSubmit = async (data: UnifiedTransactionFormValues) => {
    console.log('🚀 [TRANSACTION SUBMIT] Form submission started')
    console.log('🚀 [TRANSACTION SUBMIT] Form data:', data)
    console.log('🚀 [TRANSACTION SUBMIT] Company ID:', company?.id)

    if (!company?.id) {
      console.error('❌ [TRANSACTION SUBMIT] Company information not found')
      toast.error('Company information not found')
      return
    }

    // Clear any previous submission errors
    setSubmissionError(null)

    try {
      // Prepare unified transaction data - ensure total_amount is always a number
      const totalAmount = data.total_amount || data.subtotal || 0
      console.log('🚀 [TRANSACTION SUBMIT] Total amount calculated:', totalAmount)
      console.log('🚀 [TRANSACTION SUBMIT] data.total_amount:', data.total_amount)
      console.log('🚀 [TRANSACTION SUBMIT] data.subtotal:', data.subtotal)

      if (totalAmount <= 0) {
        console.error('❌ [TRANSACTION SUBMIT] Invalid transaction amount:', totalAmount)
        toast.error('Please enter a valid transaction amount')
        return
      }

      const unifiedTransactionData = {
        member_id: data.member_id,
        total_amount: totalAmount,
        subtotal: data.subtotal || totalAmount, // Include subtotal for proper reward calculations
        tax_amount: data.tax_amount || 0,
        business_name: data.business_name,
        financial_system_number: data.financial_system_number,
        transaction_date: data.transaction_date,
        description: data.description,
        receipt_image_url: data.receipt_image_url,
        points_earned: data.ai_suggested_points || data.points_change,
        // Include enhanced OCR data if available
        receipt_ocr_data: enhancedOcrData ? JSON.stringify(enhancedOcrData) : undefined,
        receipt_ocr_confidence: enhancedOcrData?.confidence,
        applied_rewards: selectedRewards.map(rewardId => {
          const reward = rewardsData?.data?.find((r: { id: string; points_required: number }) => r.id === rewardId)
          return {
            reward_id: rewardId,
            points_used: reward?.points_required || 0
          }
        })
      }

      const result = await createUnifiedTransactionMutation.mutateAsync(unifiedTransactionData)

      console.log('Transaction result:', result)
      console.log('Transactions array:', result.data?.transactions)
      console.log('First transaction:', result.data?.transactions?.[0])

      // Get the primary transaction ID (first transaction, usually the EARN transaction)
      const primaryTransactionId = result.data.transactions[0]?.transaction?.id

      console.log('Primary transaction ID:', primaryTransactionId)

      if (primaryTransactionId) {
        // Redirect to transaction success page with the transaction ID
        router.push(`/transactions/success?id=${primaryTransactionId}`)
      } else {
        // Fallback: redirect to transactions list
        router.push('/transactions')
      }

    } catch (error) {
      console.error('Transaction creation error:', error)
      console.error('Error type:', typeof error)
      console.error('Error constructor:', error?.constructor?.name)
      console.error('Error properties:', Object.keys(error || {}))

      // Handle duplicate receipt errors with dialog
      if (error instanceof Error && 'isDuplicateReceipt' in error && (error as DuplicateReceiptError).isDuplicateReceipt) {
        const duplicateError = error as DuplicateReceiptError
        console.log('Handling duplicate receipt error:', duplicateError)
        setDuplicateReceiptDialog({
          isOpen: true,
          message: duplicateError.errorData.message || 'This receipt has already been processed.',
          fsNumber: data.financial_system_number
        })
        return // Don't show additional error states
      }

      // Handle reward already redeemed errors with dialog
      if (error instanceof Error && 'isRewardAlreadyRedeemed' in error && (error as RewardAlreadyRedeemedError).isRewardAlreadyRedeemed) {
        const rewardError = error as RewardAlreadyRedeemedError
        console.log('Handling reward already redeemed error:', rewardError)
        setRewardAlreadyRedeemedDialog({
          isOpen: true,
          message: rewardError.errorData.message || 'This reward has already been redeemed by this member.',
          rewardTitle: rewardError.errorData.rewardTitle
        })
        return // Don't show additional error states
      }

      // Handle other error types
      const errorMessage = error instanceof Error ? error.message : 'Failed to process transaction'
      setSubmissionError(errorMessage)

      // Show toast for non-duplicate errors
      toast.error(errorMessage)
    }
  }

  if (authLoading || companyLoading || adminLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header - Compact Mobile-Friendly Layout */}
      <div className="space-y-3">
        {/* Navigation and Title Row */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" asChild className="shrink-0">
              <Link href="/transactions">
                <ArrowLeft className="h-4 w-4" />
                <span className="hidden sm:inline ml-2">Back to Transactions</span>
                <span className="sm:hidden ml-2">Back</span>
              </Link>
            </Button>
            <div className="min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold tracking-tight">
                Smart Transaction Processing
              </h1>
              <p className="text-xs sm:text-sm text-muted-foreground hidden sm:block">
                AI-powered receipt processing with automatic reward suggestions
              </p>
            </div>
          </div>

          {/* Admin Mode Toggle - Compact */}
          {isAdmin && (
            <div className="flex items-center gap-2 px-3 py-2 bg-muted/50 rounded-lg border">
              <Settings className="h-3 w-3 text-muted-foreground" />
              <Label htmlFor="manual-mode" className="text-xs font-medium cursor-pointer">
                Manual Entry
              </Label>
              <Button
                id="manual-mode"
                variant="ghost"
                size="sm"
                onClick={() => setIsManualMode(!isManualMode)}
                className="h-6 w-6 p-0"
              >
                {isManualMode ? (
                  <ToggleRight className="h-4 w-4 text-green-600" />
                ) : (
                  <ToggleLeft className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
              {isManualMode && (
                <Badge variant="secondary" className="bg-green-100 text-green-800 text-xs px-1.5 py-0.5">
                  ON
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Progress Indicator */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-500" />
            Processing Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 ${processingStage === 'upload' ? 'text-blue-600' : 'text-green-600'}`}>
              <Camera className="h-4 w-4" />
              <span className="text-sm font-medium">1. Upload Receipt</span>
              {processingStage !== 'upload' && <CheckCircle className="h-4 w-4" />}
            </div>
            <div className="h-px flex-1 bg-border" />
            <div className={`flex items-center gap-2 ${processingStage === 'ocr' ? 'text-blue-600' : processingStage === 'upload' ? 'text-muted-foreground' : 'text-green-600'}`}>
              <FileText className="h-4 w-4" />
              <span className="text-sm font-medium">2. AI Processing</span>
              {isProcessingOCR && <Loader2 className="h-4 w-4 animate-spin" />}
              {processingStage === 'complete' && <CheckCircle className="h-4 w-4" />}
            </div>
            <div className="h-px flex-1 bg-border" />
            <div className={`flex items-center gap-2 ${processingStage === 'rewards' || processingStage === 'complete' ? 'text-blue-600' : 'text-muted-foreground'}`}>
              <Trophy className="h-4 w-4" />
              <span className="text-sm font-medium">3. Apply Rewards</span>
              {processingStage === 'complete' && <CheckCircle className="h-4 w-4" />}
            </div>
          </div>
        </CardContent>
      </Card>

      <form onSubmit={(e) => {
        console.log('📝 [FORM SUBMIT] Form onSubmit event triggered')
        console.log('📝 [FORM SUBMIT] Event:', e)
        console.log('📝 [FORM SUBMIT] Form errors:', form.formState.errors)
        console.log('📝 [FORM SUBMIT] Form is valid:', form.formState.isValid)
        console.log('📝 [FORM SUBMIT] Current form values:', form.getValues())
        return form.handleSubmit(
          onSubmit,
          (errors) => {
            console.error('❌ [FORM VALIDATION] Form validation failed:', errors)
            console.error('❌ [FORM VALIDATION] All form errors:', form.formState.errors)
          }
        )(e)
      }} className="space-y-6">
        {/* Error Display */}
        {submissionError && (
          <Alert variant="destructive">
            <X className="h-4 w-4" />
            <AlertTitle>Transaction Failed</AlertTitle>
            <AlertDescription className="mt-2">
              {submissionError}
            </AlertDescription>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => setSubmissionError(null)}
            >
              Dismiss
            </Button>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Transaction Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Transaction Details
              </CardTitle>
              <CardDescription>
                Select member and upload receipt for AI processing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Member Selection */}
              <div className="space-y-2">
                <Label htmlFor="member_id">Member *</Label>
                <Controller
                  name="member_id"
                  control={form.control}
                  render={({ field, fieldState }) => (
                    <div className="space-y-1">
                      <MemberCombobox
                        value={field.value}
                        onValueChange={field.onChange}
                        members={membersData?.data || []}
                        placeholder="Select member..."
                      />
                      {fieldState.error && (
                        <p className="text-sm text-red-500">{fieldState.error.message}</p>
                      )}
                    </div>
                  )}
                />
              </div>

              {/* Receipt Upload - Only show in non-manual mode */}
              {!isManualMode && (
                <div className="space-y-2">
                  <Label htmlFor="receipt_image">Receipt Image</Label>
                  <div className="space-y-2">
                    <Input
                      id="receipt_image"
                      type="file"
                      accept="image/*"
                      disabled={isProcessingOCR}
                      className="bg-card border"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          handleReceiptUpload(file)
                        }
                      }}
                    />
                  {receiptFile && (
                    <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium text-green-700 dark:text-green-400">
                        {receiptFile.name}
                      </span>
                      <span className="text-xs text-green-600">
                        {(receiptFile.size / 1024 / 1024).toFixed(1)} MB
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setReceiptFile(null)
                          setOcrData(null)
                          setEnhancedOcrData(null)
                          setShowOcrResults(false)
                          const fileInput = document.getElementById('receipt_image') as HTMLInputElement
                          if (fileInput) fileInput.value = ''
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                  </div>
                </div>
              )}

              {/* Enhanced OCR Results Display */}
              {showOcrResults && (enhancedOcrData || ocrData) && (
                <ReceiptSummary
                  receiptData={enhancedOcrData || {
                    // Fallback to basic data if enhanced data is not available
                    business_name: ocrData?.business_name || '',
                    financial_system_number: ocrData?.financial_system_number || '',
                    total_amount: ocrData?.total_amount || 0,
                    payment_method: ocrData?.payment_method || '',
                    receipt_date: ocrData?.receipt_date || '',
                    items: [], // No items for basic OCR
                    confidence: ocrData?.confidence || 0
                  }}
                  confidence={ocrData?.confidence}
                  className="mt-4"
                  showItems={true}
                />
              )}

              {/* Editable receipt fields - users can correct AI extraction mistakes */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-2">
                  <Settings className="h-4 w-4 text-muted-foreground" />
                  <Label className="text-sm font-medium text-muted-foreground">
                    Receipt Details (Editable - Correct any AI mistakes)
                  </Label>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="subtotal">Subtotal (Before Tax) *</Label>
                    <Controller
                      name="subtotal"
                      control={form.control}
                      render={({ field, fieldState }) => (
                        <div className="space-y-1">
                          <Input
                            {...field}
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            className="bg-card"
                          />
                          {fieldState.error && (
                            <p className="text-sm text-red-500">{fieldState.error.message}</p>
                          )}
                          <p className="text-xs text-muted-foreground">Used for points calculation</p>
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="tax_amount">Tax Amount</Label>
                    <Controller
                      name="tax_amount"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          className="bg-card"
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="total_amount">Total Amount *</Label>
                    <Controller
                      name="total_amount"
                      control={form.control}
                      render={({ field, fieldState }) => (
                        <div className="space-y-1">
                          <Input
                            {...field}
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            className="bg-card"
                          />
                          {fieldState.error && (
                            <p className="text-sm text-red-500">{fieldState.error.message}</p>
                          )}
                        </div>
                      )}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="business_name">Business Name</Label>
                    <Controller
                      name="business_name"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Business name"
                          className="bg-card"
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="financial_system_number">Receipt Number</Label>
                    <Controller
                      name="financial_system_number"
                      control={form.control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="FS Number or Receipt ID"
                          className="bg-card"
                        />
                      )}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Right Column - AI Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wand2 className="h-5 w-5 text-purple-500" />
                Reward Recommendations
              </CardTitle>
              <CardDescription>
                {selectedMember && recommendedRewards.length > 0
                  ? `${selectedMember.name || 'Selected member'} (${memberAvailablePoints} points) qualifies for ${recommendedRewards.length} reward${recommendedRewards.length === 1 ? '' : 's'}`
                  : 'Select a member to see available rewards'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Points Calculation */}
              {(() => {
                const suggestedPoints = form.watch('ai_suggested_points')
                return suggestedPoints && suggestedPoints > 0
              })() && (
                <Alert>
                  <Calculator className="h-4 w-4" />
                  <AlertTitle>Points Earned</AlertTitle>
                  <AlertDescription>
                    This transaction will earn <strong>{form.watch('ai_suggested_points')} points</strong>
                  </AlertDescription>
                </Alert>
              )}

              {/* Recommended Rewards */}
              {recommendedRewards.length > 0 ? (
                <div className="space-y-3">
                  <h4 className="font-medium">Available Rewards:</h4>
                  {recommendedRewards.map((reward) => (
                    <Card
                      key={reward.id}
                      className={`p-3 transition-colors ${
                        memberAvailablePoints < reward.points_required
                          ? 'opacity-60 border-red-200 bg-red-50/30 dark:bg-red-900/10 cursor-not-allowed'
                          : selectedRewards.includes(reward.id)
                            ? 'border-green-500 bg-green-50 dark:bg-green-900/20 cursor-pointer'
                            : 'hover:border-gray-300 cursor-pointer'
                      }`}
                      onClick={() => {
                        if (memberAvailablePoints >= reward.points_required) {
                          toggleRewardSelection(reward.id)
                        }
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <h5 className="font-medium">{reward.title}</h5>
                            {selectedRewards.includes(reward.id) && (
                              <Badge variant="secondary" className="bg-green-100 text-green-800">
                                Selected
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{reward.description}</p>
                          <div className="flex items-center gap-4 mt-1">
                            <div className="flex items-center gap-2">
                              <span className="text-sm">
                                <strong>{reward.points_required}</strong> points
                              </span>
                              {memberAvailablePoints < reward.points_required && (
                                <Badge variant="destructive" className="text-xs px-1.5 py-0.5">
                                  Need {reward.points_required - memberAvailablePoints} more
                                </Badge>
                              )}
                            </div>
                            <span className="text-sm text-green-600">
                              {reward.reward_value_type === 'PERCENTAGE' ? `${reward.reward_value}% off` :
                               reward.reward_value_type === 'DOUBLE_POINTS' ? `${reward.reward_value}x points` :
                               `${reward.reward_value} Birr off`}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          {selectedRewards.includes(reward.id) ? (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          ) : (
                            <div className="h-5 w-5 border rounded-full" />
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  {selectedMember ? (
                    <div className="space-y-2">
                      <Trophy className="h-8 w-8 mx-auto opacity-50" />
                      <p className="font-medium">No eligible rewards found</p>
                      <p className="text-sm">
                        {selectedMember.name || 'This member'} needs more points or all available rewards have been redeemed.
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Sparkles className="h-8 w-8 mx-auto opacity-50" />
                      <p className="font-medium">Select a member to see rewards</p>
                      <p className="text-sm">Available rewards will be displayed here</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {selectedRewards.length > 0 && (
                  <span>💡 1 reward will be applied to this transaction</span>
                )}
              </div>
              <div className="flex gap-3">
                <Button type="button" variant="outline" asChild>
                  <Link href="/transactions">Cancel</Link>
                </Button>
                <Button
                  type="submit"
                  disabled={(() => {
                    const isPending = createUnifiedTransactionMutation.isPending
                    const memberId = form.watch('member_id')
                    const totalAmount = form.watch('total_amount')
                    const subtotal = form.watch('subtotal')

                    // Check if we have valid amounts - either total_amount or subtotal must be > 0
                    const hasValidTotalAmount = totalAmount && totalAmount > 0
                    const hasValidSubtotal = subtotal && subtotal > 0
                    const hasValidAmount = hasValidTotalAmount || hasValidSubtotal

                    const isDisabled = isPending || !memberId || !hasValidAmount

                    console.log('🔘 [BUTTON STATE] Button disabled check:', {
                      isPending,
                      memberId,
                      totalAmount,
                      subtotal,
                      hasValidTotalAmount,
                      hasValidSubtotal,
                      hasValidAmount,
                      isDisabled
                    })

                    return isDisabled
                  })()}
                  className="min-w-[140px]"
                  onClick={() => {
                    console.log('🔘 [BUTTON CLICK] Process Transaction button clicked!')
                    console.log('🔘 [BUTTON CLICK] Current form values:', form.getValues())
                  }}
                >
                  {createUnifiedTransactionMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="mr-2 h-4 w-4" />
                      Process Transaction
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>

      {/* Duplicate Receipt Warning Dialog */}
      <AlertDialog open={duplicateReceiptDialog.isOpen} onOpenChange={(open) =>
        setDuplicateReceiptDialog({ isOpen: open })
      }>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Duplicate Receipt Detected
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p className="text-base">
                {duplicateReceiptDialog.message}
              </p>
              {duplicateReceiptDialog.fsNumber && (
                <p className="text-sm text-muted-foreground">
                  Receipt Number: <span className="font-mono">{duplicateReceiptDialog.fsNumber}</span>
                </p>
              )}
              <p className="text-sm text-muted-foreground">
                Each receipt can only be submitted once to prevent duplicate transactions.
                Please check your transaction history or use a different receipt.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setDuplicateReceiptDialog({ isOpen: false })}>
              Understood
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reward Already Redeemed Warning Dialog */}
      <AlertDialog open={rewardAlreadyRedeemedDialog.isOpen} onOpenChange={(open) =>
        setRewardAlreadyRedeemedDialog({ isOpen: open })
      }>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Reward Already Redeemed
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p className="text-base">
                {rewardAlreadyRedeemedDialog.message}
              </p>
              {rewardAlreadyRedeemedDialog.rewardTitle && (
                <p className="text-sm text-muted-foreground">
                  Reward: <span className="font-medium">{rewardAlreadyRedeemedDialog.rewardTitle}</span>
                </p>
              )}
              <p className="text-sm text-muted-foreground">
                Each reward can only be redeemed once per member. Please select a different reward or check the member&apos;s redemption history.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={() => setRewardAlreadyRedeemedDialog({ isOpen: false })}>
              Understood
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
