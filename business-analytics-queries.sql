-- Business Analytics SQL Queries
-- These queries provide insights into popular items, valuable members, and business performance

-- 1. Most Popular Items (by quantity sold)
CREATE OR REPLACE VIEW popular_items_by_quantity AS
SELECT 
    bi.id,
    bi.item_name,
    bi.item_category,
    bi.item_subcategory,
    bi.standard_price,
    COALESCE(SUM(ri.quantity), 0) as total_quantity_sold,
    COUNT(DISTINCT ri.receipt_id) as number_of_orders,
    COALESCE(SUM(ri.total_price), 0) as total_revenue,
    COALESCE(AVG(ri.unit_price), 0) as avg_selling_price,
    MAX(r.purchase_date) as last_sold_date,
    bi.company_id
FROM business_items bi
LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
LEFT JOIN receipts r ON ri.receipt_id = r.id
WHERE bi.is_active = true
GROUP BY bi.id, bi.item_name, bi.item_category, bi.item_subcategory, bi.standard_price, bi.company_id
ORDER BY total_quantity_sold DESC;

-- 2. Most Valuable Items (by revenue)
CREATE OR REPLACE VIEW popular_items_by_revenue AS
SELECT 
    bi.id,
    bi.item_name,
    bi.item_category,
    bi.item_subcategory,
    bi.standard_price,
    COALESCE(SUM(ri.quantity), 0) as total_quantity_sold,
    COUNT(DISTINCT ri.receipt_id) as number_of_orders,
    COALESCE(SUM(ri.total_price), 0) as total_revenue,
    COALESCE(AVG(ri.unit_price), 0) as avg_selling_price,
    MAX(r.purchase_date) as last_sold_date,
    bi.company_id
FROM business_items bi
LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
LEFT JOIN receipts r ON ri.receipt_id = r.id
WHERE bi.is_active = true
GROUP BY bi.id, bi.item_name, bi.item_category, bi.item_subcategory, bi.standard_price, bi.company_id
ORDER BY total_revenue DESC;

-- 3. Most Active Members (by transaction count)
CREATE OR REPLACE VIEW most_active_members AS
SELECT 
    lm.id,
    lm.name,
    lm.loyalty_id,
    lm.phone_number,
    lm.email,
    lm.loyalty_tier,
    lm.registration_date,
    COUNT(DISTINCT pt.id) as total_transactions,
    COUNT(DISTINCT r.id) as total_purchases,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0) as total_points_earned,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0) as total_points_redeemed,
    COALESCE(lm.lifetime_points, 0) as lifetime_points,
    COALESCE(mp.available_points, 0) as available_points,
    COALESCE(SUM(r.total_amount), 0) as total_spent,
    COALESCE(AVG(r.total_amount), 0) as avg_order_value,
    MAX(pt.transaction_date) as last_activity_date,
    lm.company_id
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
LEFT JOIN receipts r ON pt.receipt_id = r.id
LEFT JOIN member_points mp ON lm.id = mp.id
GROUP BY lm.id, lm.name, lm.loyalty_id, lm.phone_number, lm.email, lm.loyalty_tier, 
         lm.registration_date, lm.lifetime_points, mp.available_points, lm.company_id
ORDER BY total_transactions DESC;

-- 4. Most Valuable Members (by total spent)
CREATE OR REPLACE VIEW most_valuable_members AS
SELECT 
    lm.id,
    lm.name,
    lm.loyalty_id,
    lm.phone_number,
    lm.email,
    lm.loyalty_tier,
    lm.registration_date,
    COUNT(DISTINCT pt.id) as total_transactions,
    COUNT(DISTINCT r.id) as total_purchases,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'EARN' THEN pt.points_change ELSE 0 END), 0) as total_points_earned,
    COALESCE(SUM(CASE WHEN pt.transaction_type = 'REDEEM' THEN ABS(pt.points_change) ELSE 0 END), 0) as total_points_redeemed,
    COALESCE(lm.lifetime_points, 0) as lifetime_points,
    COALESCE(mp.available_points, 0) as available_points,
    COALESCE(SUM(r.total_amount), 0) as total_spent,
    COALESCE(AVG(r.total_amount), 0) as avg_order_value,
    MAX(pt.transaction_date) as last_activity_date,
    lm.company_id
FROM loyalty_members lm
LEFT JOIN points_transactions pt ON lm.id = pt.member_id
LEFT JOIN receipts r ON pt.receipt_id = r.id
LEFT JOIN member_points mp ON lm.id = mp.id
GROUP BY lm.id, lm.name, lm.loyalty_id, lm.phone_number, lm.email, lm.loyalty_tier, 
         lm.registration_date, lm.lifetime_points, mp.available_points, lm.company_id
ORDER BY total_spent DESC;

-- 5. Business Performance Summary
CREATE OR REPLACE VIEW business_performance_summary AS
SELECT 
    c.id as company_id,
    c.name as company_name,
    COUNT(DISTINCT lm.id) as total_members,
    COUNT(DISTINCT r.id) as total_receipts,
    COUNT(DISTINCT pt.id) as total_transactions,
    COUNT(DISTINCT bi.id) as total_items,
    COALESCE(SUM(r.total_amount), 0) as total_revenue,
    COALESCE(AVG(r.total_amount), 0) as avg_order_value,
    COALESCE(SUM(pt.points_change), 0) as total_points_issued,
    DATE_TRUNC('month', CURRENT_DATE) as report_month
FROM companies c
LEFT JOIN loyalty_members lm ON c.id = lm.company_id
LEFT JOIN receipts r ON c.id = r.company_id
LEFT JOIN points_transactions pt ON c.id = pt.company_id
LEFT JOIN business_items bi ON c.id = bi.company_id
WHERE c.is_active = true
GROUP BY c.id, c.name;

-- 6. Category Performance Analysis
CREATE OR REPLACE VIEW category_performance AS
SELECT 
    bi.company_id,
    bi.item_category,
    COUNT(DISTINCT bi.id) as total_items,
    COALESCE(SUM(ri.quantity), 0) as total_quantity_sold,
    COUNT(DISTINCT ri.receipt_id) as number_of_orders,
    COALESCE(SUM(ri.total_price), 0) as total_revenue,
    COALESCE(AVG(ri.unit_price), 0) as avg_selling_price,
    COALESCE(AVG(ri.quantity), 0) as avg_quantity_per_order
FROM business_items bi
LEFT JOIN receipt_items ri ON bi.id = ri.business_item_id
WHERE bi.is_active = true
GROUP BY bi.company_id, bi.item_category
ORDER BY total_revenue DESC;

-- 7. Recent Activity Summary (Last 30 days)
CREATE OR REPLACE VIEW recent_activity_summary AS
SELECT 
    c.id as company_id,
    c.name as company_name,
    COUNT(DISTINCT lm.id) FILTER (WHERE lm.registration_date >= CURRENT_DATE - INTERVAL '30 days') as new_members_30d,
    COUNT(DISTINCT r.id) FILTER (WHERE r.purchase_date >= CURRENT_DATE - INTERVAL '30 days') as receipts_30d,
    COUNT(DISTINCT pt.id) FILTER (WHERE pt.transaction_date >= CURRENT_DATE - INTERVAL '30 days') as transactions_30d,
    COALESCE(SUM(r.total_amount) FILTER (WHERE r.purchase_date >= CURRENT_DATE - INTERVAL '30 days'), 0) as revenue_30d,
    COUNT(DISTINCT lm.id) FILTER (WHERE EXISTS (
        SELECT 1 FROM points_transactions pt2 
        WHERE pt2.member_id = lm.id 
        AND pt2.transaction_date >= CURRENT_DATE - INTERVAL '30 days'
    )) as active_members_30d
FROM companies c
LEFT JOIN loyalty_members lm ON c.id = lm.company_id
LEFT JOIN receipts r ON c.id = r.company_id
LEFT JOIN points_transactions pt ON c.id = pt.company_id
WHERE c.is_active = true
GROUP BY c.id, c.name;

-- 8. Monthly Trends (Last 12 months)
CREATE OR REPLACE VIEW monthly_trends AS
SELECT 
    c.id as company_id,
    c.name as company_name,
    DATE_TRUNC('month', r.purchase_date) as month,
    COUNT(DISTINCT r.id) as receipts_count,
    COUNT(DISTINCT lm.id) as unique_customers,
    COALESCE(SUM(r.total_amount), 0) as revenue,
    COALESCE(AVG(r.total_amount), 0) as avg_order_value
FROM companies c
LEFT JOIN receipts r ON c.id = r.company_id
LEFT JOIN loyalty_members lm ON r.member_id = lm.id
WHERE c.is_active = true 
AND r.purchase_date >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY c.id, c.name, DATE_TRUNC('month', r.purchase_date)
ORDER BY c.id, month DESC;
